package com.chinamobile.retail.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.common.Constant;
import com.chinamobile.iot.sc.common.redis.RedisUtil;
import com.chinamobile.iot.sc.common.utils.BaseServiceUtils;
import com.chinamobile.iot.sc.enums.AtomOfferingClassEnum;
import com.chinamobile.iot.sc.enums.SPUOfferingClassEnum;
import com.chinamobile.iot.sc.enums.log.LogResultEnum;
import com.chinamobile.iot.sc.enums.log.MiniProgramOperateEnum;
import com.chinamobile.iot.sc.enums.log.ModuleEnum;
import com.chinamobile.iot.sc.exceptions.BaseErrorConstant;
import com.chinamobile.iot.sc.exceptions.BusinessException;
import com.chinamobile.iot.sc.feign.UserFeignClient;
import com.chinamobile.iot.sc.mode.LoginIfo4Redis;
import com.chinamobile.iot.sc.mode.PageData;
import com.chinamobile.iot.sc.service.LogService;
import com.chinamobile.iot.sc.util.DateTimeUtil;
import com.chinamobile.iot.sc.util.DateUtils;
import com.chinamobile.iot.sc.util.excel.EasyExcelUtils;
import com.chinamobile.retail.constant.ActivityAuditStatusEnum;
import com.chinamobile.retail.constant.ActivityAwardStatusEnum;
import com.chinamobile.retail.constant.ActivityStatusEnum;
import com.chinamobile.retail.constant.RedisLockConstant;
import com.chinamobile.retail.dao.*;
import com.chinamobile.retail.dao.ext.MiniProgramActivityMapperExt;
import com.chinamobile.retail.enums.ActivityOfferingClassEnum;
import com.chinamobile.retail.enums.OrderStatusInnerEnum;
import com.chinamobile.retail.exception.StatusContant;
import com.chinamobile.retail.pojo.dto.ActivityDataRankDTO;
import com.chinamobile.retail.pojo.dto.ActivityWeeklyFunAwardCountDTO;
import com.chinamobile.retail.pojo.dto.MiniProgramActivityUserRankingDTO;
import com.chinamobile.retail.pojo.dto.Order2CInfoDTO;
import com.chinamobile.retail.pojo.entity.*;
import com.chinamobile.retail.pojo.handel.OrderInfoHandle;
import com.chinamobile.retail.pojo.mapper.SelectRankActivityUserAwardDTO;
import com.chinamobile.retail.pojo.param.miniprogram.*;
import com.chinamobile.retail.pojo.ro.ActivityRO;
import com.chinamobile.retail.pojo.vo.miniprogram.*;
import com.chinamobile.retail.quartz.GeneralJobData;
import com.chinamobile.retail.quartz.GeneralJobManager;
import com.chinamobile.retail.quartz.job.MiniProgramActivityConfirmJob;
import com.chinamobile.retail.quartz.job.MiniProgramActivityFinishJob;
import com.chinamobile.retail.quartz.job.MiniProgramActivitySettlementJob;
import com.chinamobile.retail.quartz.job.MiniProgramActivityStartJob;
import com.chinamobile.retail.service.IMiniActivityService;
import com.chinamobile.retail.util.PinyinUtils;
import com.chinamobile.retail.util.cache.RedisCacheUtil;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ClassPathResource;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.time.*;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingDeque;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

import static com.chinamobile.iot.sc.exceptions.BaseErrorConstant.PARAM_ERROR;
import static com.chinamobile.retail.constant.RedisLockConstant.ACTIVITY_LOCK;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/7/16 09:43
 * @description TODO
 */
@Slf4j
@Service
public class MiniActivityServiceImpl implements IMiniActivityService {
    @Autowired
    private RedisUtil redisUtil;
    @Resource
    private MiniProgramActivityMapper miniProgramActivityMapper;
    @Resource
    private MiniActivityServiceImpl miniActivityService;
    @Resource
    private PartnerPointMapper partnerPointMapper;
    @Resource
    PointOperateMapper pointOperateMapper;
    @Resource
    private PointExchangePartnerMapper pointExchangePartnerMapper;
    @Resource
    private MiniProgramActivityRankMapper miniProgramActivityRankMapper;

    @Resource
    private MiniProgramActivityWeeklyFunMapper miniProgramActivityWeeklyFunMapper;
    @Resource
    private UserFeignClient userFeignClient;
    @Resource
    private MiniProgramActivityRegionMapper miniProgramActivityRegionMapper;
    @Resource
    UserMiniProgramMapper userMiniProgramMapper;
    @Resource
    UserMiniProgramAddressMapper userMiniProgramAddressMapper;

    @Resource
    private MiniProgramActivitySpuCodeMapper miniProgramActivitySpuCodeMapper;

    @Resource
    private MiniProgramActivityBusinessCodeMapper miniProgramActivityBusinessCodeMapper;

    @Resource
    private MiniProgramActivityOfferingClassMapper miniProgramActivityOfferingClassMapper;
    @Resource
    MiniProgramActivityAccessMapper miniProgramActivityAccessMapper;

    @Resource
    private MiniProgramActivityOrderTypeMapper miniProgramActivityOrderTypeMapper;

    @Resource
    private MiniProgramActivityRankAwardMapper miniProgramActivityRankAwardMapper;

    @Resource
    private MiniProgramActivityWeeklyFunAwardMapper miniProgramActivityWeeklyFunAwardMapper;
    @Autowired
    private LogService logService;
    @Resource
    private MiniProgramActivityUserAwardMapper miniProgramActivityUserAwardMapper;

    @Resource
    private MiniProgramActivityUserMapper miniProgramActivityUserMapper;

    @Resource
    private MiniProgramActivityMapperExt miniProgramActivityMapperExt;

    @Resource
    private UserMapper userMapper;

    @Resource
    private GeneralJobManager jobManager;

    @Resource
    private PointSupplierMapper pointSupplierMapper;

    @Resource
    private SpuOfferingInfoMapper spuOfferingInfoMapper;

    private static final String QUARTZ_ACTIVITY_START = "quartz_activity_start";
    private static final String QUARTZ_ACTIVITY_SETTLEMENT = "quartz_activity_settlement";
    private static final String QUARTZ_ACTIVITY_CONFIRM = "quartz_activity_confirm";
    private static final String QUARTZ_ACTIVITY_FINISH = "quartz_activity_finish";

    private static final int TARGET_MASK_MANAGER = 0x01;
    private static final int TARGET_MASK_DISTRIBUTOR = 0x02;
    private static final int TARGET_MASK_CHANNEL = 0x04;

    @Value("${tocustomer.orderType}")
    private List toCustomerOrderType;

    @Resource
    private RedisTemplate redisTemplate;

    @Resource
    private ContractProvinceInfoMapper contractProvinceInfoMapper;
    @Resource
    private ContractCityInfoMapper contractCityInfoMapper;

    @Resource
    private RedisCacheUtil redisCacheUtil;

    private ExecutorService executorService = new ThreadPoolExecutor(50, 50, 1L, TimeUnit.MINUTES, new LinkedBlockingDeque<>(10000));

    /**
     * /*
     * 发布排位赛活动
     *
     * @param param
     * @param userId
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @DS("save")
    public void publishRankActivity(PublishRankActivityParam param, String userId) {

        log.info("发布排位赛活动，用户id：{}，请求参数：{}", userId, JSON.toJSONString(param));
        String logContent = generatePublishRankActivityLogContent(param);
        // 保存活动基本信息
        MiniProgramActivity miniProgramActivity = saveActivityBaseInfo(param, userId);
        // 保存排位赛活动信息
        saveActivityRank(param, miniProgramActivity.getId());
        // 保存活动省份
        saveActivityRegions(param, miniProgramActivity.getId());
        // 保存产品范式
        saveActivityOfferingClasses(param, miniProgramActivity.getId());
        // 保存SPU编码
        saveActivitySpuCodes(param, miniProgramActivity.getId());
        // 保存订单类型
        saveActivityOrderTypes(param, miniProgramActivity.getId());
        // 保存客户类型
        saveActivityBusinessCodes(param, miniProgramActivity.getId());
        // 保存活动奖品
        saveRankActivityAwards(param, miniProgramActivity.getId());
        logService.recordOperateLog(ModuleEnum.MINI_PROGRAM.code, MiniProgramOperateEnum.ACTIVITY.code, logContent, userId, 0, LogResultEnum.LOG_SUCESS.code, null);
//        TransactionUtil.afterCommit(() -> syncLoadActivity2Redis(miniProgramActivity.getId()));
    }

    /**
     * 发布周周乐活动
     *
     * @param param
     * @param userId
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @DS("save")
    public void publishWeeklyActivity(PublishWeeklyActivityParam param, String userId, LoginIfo4Redis loginIfo4Redis) {

        log.info("发布周周乐活动，用户id：{}，请求参数：{}", userId, JSON.toJSONString(param));
        String logContent = generatePublishWeeklyActivityLogContent(param);
        // 保存活动基本信息
        MiniProgramActivity miniProgramActivity = saveActivityBaseInfo(param, userId);
        // 保存周周乐活动信息
        saveActivityWeeklyFun(param, miniProgramActivity.getId());
        // 保存活动省份
        saveActivityRegions(param, miniProgramActivity.getId());
        // 保存产品范式
        saveActivityOfferingClasses(param, miniProgramActivity.getId());
        // 保存SPU编码
        saveActivitySpuCodes(param, miniProgramActivity.getId());
        // 保存活动参与人员
        saveWeeklyActivityUsers(param, miniProgramActivity.getId());
        // 保存活动奖品
        saveWeeklyActivityAwards(param, miniProgramActivity.getId());
        logService.recordOperateLog(ModuleEnum.MINI_PROGRAM.code, MiniProgramOperateEnum.ACTIVITY.code, logContent, userId, 0, LogResultEnum.LOG_SUCESS.code, null);
//        TransactionUtil.afterCommit(() -> syncLoadActivity2Redis(miniProgramActivity.getId()));
    }

    /**
     * 获取活动详情
     *
     * @param activityId
     * @return
     */
    @Override
    @DS("query")
    public ActivityDetailVO getActivityDetail(String activityId, Integer flag, String userId, LoginIfo4Redis loginIfo4Redis) {
        String roleType = loginIfo4Redis.getRoleType();

        MiniProgramActivity miniProgramActivity = miniProgramActivityMapper.selectByPrimaryKey(activityId);
        if (null == miniProgramActivity || miniProgramActivity.getIsDelete() == 1) {
            throw new BusinessException(StatusContant.MINI_PROGRAM_ACTIVITY_NOT_EXIST);
        }

        StringBuilder logTitleBuilder = new StringBuilder();
        if (flag == 0) {
            logTitleBuilder.append("【查看活动】\n活动名称：");
        } else {
            logTitleBuilder.append("【查看配置详情】\n活动名称：");
        }
        logTitleBuilder.append(miniProgramActivity.getName());
        logService.recordOperateLog(ModuleEnum.MINI_PROGRAM.code, MiniProgramOperateEnum.ACTIVITY.code, logTitleBuilder.toString(), userId, 0, LogResultEnum.LOG_SUCESS.code, null);

        int activityType = miniProgramActivity.getActivityType();
        ActivityDetailVO activityDetailVO = new ActivityDetailVO();
        BeanUtils.copyProperties(miniProgramActivity, activityDetailVO);

        User user = userMapper.selectByPrimaryKey(miniProgramActivity.getCreateUid());
        activityDetailVO.setCreateUserName(StringUtils.isEmpty(user.getName()) ? user.getPhone() : user.getName());

        Integer target = miniProgramActivity.getTarget();
        if (target != null) {
            List<Integer> targets = new ArrayList<>();
            if ((target & TARGET_MASK_MANAGER) != 0) {
                targets.add(TARGET_MASK_MANAGER);
            }
            if ((target & TARGET_MASK_DISTRIBUTOR) != 0) {
                targets.add(TARGET_MASK_DISTRIBUTOR);
            }
            if ((target & TARGET_MASK_CHANNEL) != 0) {
                targets.add(TARGET_MASK_CHANNEL);
            }
            activityDetailVO.setTargetList(targets);
        }

        List<MiniProgramActivityRegion> regions = miniProgramActivityRegionMapper.selectByExample(
                new MiniProgramActivityRegionExample().createCriteria()
                        .andActivityIdEqualTo(activityId).example()
        );
        if (!CollectionUtils.isEmpty(regions)) {
            List<RegionVO> regionVOS = regions.stream().map(miniProgramActivityRegion -> {
                RegionVO regionVO = new RegionVO();
                ContractProvinceInfo contractProvinceInfo = contractProvinceInfoMapper.selectByPrimaryKey(miniProgramActivityRegion.getProvinceCode());
                ContractCityInfo contractCityInfo = contractCityInfoMapper.selectByPrimaryKey(miniProgramActivityRegion.getCityCode());
                regionVO.setProvinceCode(contractProvinceInfo != null ? contractProvinceInfo.getMallCode() : null);
                regionVO.setProvinceName(contractProvinceInfo != null ? contractProvinceInfo.getMallName() : null);
                regionVO.setCityCode(contractCityInfo != null ? contractCityInfo.getMallCode() : null);
                regionVO.setCityName(contractCityInfo != null ? contractCityInfo.getMallName() : null);
                return regionVO;
            }).collect(Collectors.toList());
            activityDetailVO.setRegions(regionVOS);
        }

        List<MiniProgramActivityOfferingClass> offeringClasses = miniProgramActivityOfferingClassMapper.selectByExample(
                new MiniProgramActivityOfferingClassExample().createCriteria()
                        .andActivityIdEqualTo(activityId).example()
        );
        if (!CollectionUtils.isEmpty(offeringClasses)) {
            List<String> classes = offeringClasses.stream().map(MiniProgramActivityOfferingClass::getSpuOfferingClass).collect(Collectors.toList());
            activityDetailVO.setOfferingClasses(classes);
        }

        List<MiniProgramActivitySpuCode> spuCodes = miniProgramActivitySpuCodeMapper.selectByExample(
                new MiniProgramActivitySpuCodeExample().createCriteria()
                        .andActivityIdEqualTo(activityId).example()
        );
        if (!CollectionUtils.isEmpty(spuCodes)) {
            List<String> spuCodeStrs = spuCodes.stream().map(MiniProgramActivitySpuCode::getSpuOfferingCode).collect(Collectors.toList());
            activityDetailVO.setSpuCodes(spuCodeStrs);
        }

        if (1 == activityType) {
            ActivityExtraRankVO activityExtraRankVO = new ActivityExtraRankVO();
            activityDetailVO.setExtra(activityExtraRankVO);
            List<MiniProgramActivityRank> activityRanks = miniProgramActivityRankMapper.selectByExample(
                    new MiniProgramActivityRankExample().createCriteria()
                            .andActivityIdEqualTo(activityId).example()
            );
            if (!CollectionUtils.isEmpty(activityRanks)) {
                MiniProgramActivityRank activityRank = activityRanks.get(0);
                BeanUtils.copyProperties(activityRank, activityExtraRankVO);

                List<MiniProgramActivityOrderType> orderTypes = miniProgramActivityOrderTypeMapper.selectByExample(
                        new MiniProgramActivityOrderTypeExample().createCriteria()
                                .andActivityIdEqualTo(activityId).example()
                );
                if (!CollectionUtils.isEmpty(orderTypes)) {
                    List<String> types = orderTypes.stream().map(MiniProgramActivityOrderType::getOrderType).collect(Collectors.toList());
                    activityExtraRankVO.setOrderTypes(types);
                }

                List<MiniProgramActivityBusinessCode> businessCodes = miniProgramActivityBusinessCodeMapper.selectByExample(
                        new MiniProgramActivityBusinessCodeExample().createCriteria()
                                .andActivityIdEqualTo(activityId).example()
                );
                if (!CollectionUtils.isEmpty(businessCodes)) {
                    List<String> businessCodeStrs = businessCodes.stream().map(MiniProgramActivityBusinessCode::getBusinessCode).collect(Collectors.toList());
                    activityExtraRankVO.setBusinessCodes(businessCodeStrs);
                }
            }
            List<ActivityExtraRankAwardVO> awards = miniProgramActivityMapperExt.getActivityExtraRankAward(activityId);
            activityExtraRankVO.setAwards(awards);
        } else if (2 == activityType) {
            ActivityExtraWeeklyVO activityExtraWeeklyVO = new ActivityExtraWeeklyVO();
            activityDetailVO.setExtra(activityExtraWeeklyVO);
            List<MiniProgramActivityWeeklyFun> activityWeeklyFuns = miniProgramActivityWeeklyFunMapper.selectByExample(
                    new MiniProgramActivityWeeklyFunExample().createCriteria()
                            .andActivityIdEqualTo(activityId).example()
            );
            if (!CollectionUtils.isEmpty(activityWeeklyFuns)) {
                MiniProgramActivityWeeklyFun activityWeekly = activityWeeklyFuns.get(0);
                BeanUtils.copyProperties(activityWeekly, activityExtraWeeklyVO);
            }
            List<MiniProgramActivityUser> users = miniProgramActivityUserMapper.selectByExample(
                    new MiniProgramActivityUserExample().createCriteria()
                            .andActivityIdEqualTo(activityId)
                            .andIsAddEqualTo(1)
                            .example()
            );
            if (!CollectionUtils.isEmpty(users)) {
                List<ActivityUserParam> userParams = users.stream().map(miniProgramActivityUser -> {
                    ActivityUserParam activityUserParam = new ActivityUserParam();
                    BeanUtils.copyProperties(miniProgramActivityUser, activityUserParam);
                    List<UserMiniProgram> userMiniPrograms = userMiniProgramMapper.selectByExample(
                            new UserMiniProgramExample().createCriteria()
                                    .andUserIdEqualTo(miniProgramActivityUser.getUserId()).example()
                    );
                    if (!CollectionUtils.isEmpty(userMiniPrograms)) {
                        activityUserParam.setPhone(userMiniPrograms.get(0).getPhone());
                    }
                    return activityUserParam;
                }).collect(Collectors.toList());
                activityExtraWeeklyVO.setActivityUsers(userParams);
            }
            List<ActivityExtraWeeklyAwardVO> awards = miniProgramActivityMapperExt.getActivityExtraWeeklyAward(activityId);
            activityExtraWeeklyVO.setAwards(awards);
        } else {
            throw new BusinessException(StatusContant.MINI_PROGRAM_ACTIVITY_NOT_EXIST);
        }
        Date now = new Date();
        if (now.compareTo(activityDetailVO.getConfirmTime()) > 0) {
            activityDetailVO.setIsOverOrderTime(true);
        } else {
            activityDetailVO.setIsOverOrderTime(false);
        }

        return activityDetailVO;
    }

    /**
     * 分页查询管理后台活动列表
     *
     * @param param
     * @return
     */
    @Override
    @DS("query")
    public PageData<PageActivityVO> pageActivityList(PageActivityListParam param, LoginIfo4Redis loginIfo4Redis) {
        String roleType = loginIfo4Redis.getRoleType();

        Integer pageNum = param.getPageNum();
        Integer pageSize = param.getPageSize();
        if (pageNum < 0 || pageSize < 0) {
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "页码和每页数量必须大于0");
        }
        PageData<PageActivityVO> pageData = new PageData<>();
        pageData.setPage(pageNum);
        List<PageActivityVO> list = miniProgramActivityMapperExt.pageActivityList(param);
        Long total = miniProgramActivityMapperExt.countActivityList(param);
        if (!CollectionUtils.isEmpty(list)) {
            for (PageActivityVO pageActivityVO : list) {
                Integer target = pageActivityVO.getTarget();
                if (null == target) {
                    break;
                }
                List<Integer> targetList = new ArrayList<>();
                if ((TARGET_MASK_MANAGER & target) != 0) {
                    targetList.add(TARGET_MASK_MANAGER);
                }
                if ((TARGET_MASK_DISTRIBUTOR & target) != 0) {
                    targetList.add(TARGET_MASK_DISTRIBUTOR);
                }
                if ((TARGET_MASK_CHANNEL & target) != 0) {
                    targetList.add(TARGET_MASK_CHANNEL);
                }
                pageActivityVO.setTargetList(targetList);
            }
        }
        pageData.setData(list);
        pageData.setCount(total != null ? total : 0);
        return pageData;
    }

    @Override
    @DS("query")
    public PageData<ActivityDataRankVO> getDataRankList(ActivityDataRankParam param, String userId) {
        Integer pageNum = param.getPageNum();
        Integer pageSize = param.getPageSize();
        if (pageNum < 0 || pageSize < 0) {
            logService.recordOperateLog(ModuleEnum.MINI_PROGRAM.code, MiniProgramOperateEnum.ACTIVITY.code,
                    "-", userId, 0, LogResultEnum.LOG_FAIL.code, "页码和每页数量必须大于0");
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "页码和每页数量必须大于0");
        }
        MiniProgramActivity miniProgramActivity = miniProgramActivityMapper.selectByPrimaryKey(param.getId());
        if (miniProgramActivity == null) {
            throw new BusinessException(StatusContant.MINI_PROGRAM_ACTIVITY_NOT_EXIST);
        }
        PageData<ActivityDataRankVO> pageData = new PageData<>();
        pageData.setPage(pageNum);
        PageHelper.startPage(pageNum, pageSize);
        List<ActivityDataRankDTO> list = miniProgramActivityMapperExt.getDataRankList(param.getId(), param.getName());
        PageInfo<ActivityDataRankDTO> pageInfo = new PageInfo<>(list);
        pageData.setCount(pageInfo.getTotal());
        List<ActivityDataRankVO> vo = null;
        if (!CollectionUtils.isEmpty(list)) {
            vo = list.stream().map(item -> {
                ActivityDataRankVO activityDataRankVO = new ActivityDataRankVO();
                BeanUtils.copyProperties(item, activityDataRankVO);
                activityDataRankVO.setLocation(item.getProvinceName() + item.getCityName());
                String addr = "";
                addr = item.getAddr1() == null ? addr : addr + item.getAddr1();
                addr = item.getAddr2() == null ? addr : addr + item.getAddr2();
                addr = item.getAddr3() == null ? addr : addr + item.getAddr3();
                addr = item.getUsaddr() == null ? addr : addr + item.getUsaddr();
                activityDataRankVO.setDeliveryAddress(addr);
                return activityDataRankVO;
            }).collect(Collectors.toList());
        }
        pageData.setData(vo);

        logService.recordOperateLog(ModuleEnum.MINI_PROGRAM.code, MiniProgramOperateEnum.ACTIVITY.code,
                getLogContent("【查看中奖名单】", miniProgramActivity.getName(), null, null, null), userId, 0, LogResultEnum.LOG_SUCESS.code, null);
        return pageData;
    }

    @Override
    @DS("query")
    public PageData<ActivityDataRankMiniprogramVO> getDataRankListMiniProgram(ActivityDataRankParam param, String userId) {
        Integer pageNum = param.getPageNum();
        Integer pageSize = param.getPageSize();
        if (pageNum < 0 || pageSize < 0) {
            logService.recordOperateLog(ModuleEnum.MINI_PROGRAM.code, MiniProgramOperateEnum.ACTIVITY.code,
                    "-", userId, 0, LogResultEnum.LOG_FAIL.code, "页码和每页数量必须大于0");
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "页码和每页数量必须大于0");
        }
        MiniProgramActivity miniProgramActivity = miniProgramActivityMapper.selectByPrimaryKey(param.getId());
        if (miniProgramActivity == null) {
            throw new BusinessException(StatusContant.MINI_PROGRAM_ACTIVITY_NOT_EXIST);
        }
        PageData<ActivityDataRankMiniprogramVO> pageData = new PageData<>();
        pageData.setPage(pageNum);
        PageHelper.startPage(pageNum, pageSize);
        List<ActivityDataRankDTO> list = miniProgramActivityMapperExt.getDataRankList(param.getId(), param.getName());
        PageInfo<ActivityDataRankDTO> pageInfo = new PageInfo<>(list);
        pageData.setCount(pageInfo.getTotal());
        List<ActivityDataRankMiniprogramVO> vo = null;
        if (!CollectionUtils.isEmpty(list)) {
            vo = list.stream().map(item -> {

                ActivityDataRankMiniprogramVO activityDataRankVO = new ActivityDataRankMiniprogramVO();
                BeanUtils.copyProperties(item, activityDataRankVO);

                return activityDataRankVO;
            }).collect(Collectors.toList());
        }
        pageData.setData(vo);

        logService.recordOperateLog(ModuleEnum.MINI_PROGRAM.code, MiniProgramOperateEnum.ACTIVITY.code,
                getLogContent("【查看中奖名单】", miniProgramActivity.getName(), null, null, null), userId, 0, LogResultEnum.LOG_SUCESS.code, null);
        return pageData;
    }

    /**
     * 启用/停用活动
     *
     * @param param
     * @param userId
     */
    @Override
    @DS("save")
    public void activate(ActivityActivateParam param, String userId) {
        MiniProgramActivity activity = miniProgramActivityMapper.selectByPrimaryKey(param.getId());
        if (null == activity) {
            throw new BusinessException(StatusContant.MINI_PROGRAM_ACTIVITY_NOT_EXIST);
        }
        if (param.getActive()) {
            log.info("启用活动，用户id：{}，活动id：{}，名称：{}", userId, activity.getId(), activity.getName());
        } else {
            log.info("停用活动，用户id：{}，活动id：{}，名称：{}", userId, activity.getId(), activity.getName());
        }
        if (ActivityAuditStatusEnum.PASSED.getStatus().equals(activity.getAuditStatus())
                && (ActivityStatusEnum.SETTLEMENT.getStatus() > activity.getStatus() || ActivityStatusEnum.CONFIRMING.getStatus().equals(activity.getStatus()))) {
            // 对于已通过审核活动，并且处于【结算中】之前，运营人员可点击【停用】停止活动。
            activity.setActive(param.getActive());
            activity.setUpdateTime(new Date());
            miniProgramActivityMapper.updateByPrimaryKeySelective(activity);
//            syncLoadActivity2Redis(activity.getId());
            String logTitle = (param.getActive() ? "【启用活动】" : "【停用活动】") + "\n活动名称：" + activity.getName();
            logService.recordOperateLog(ModuleEnum.MINI_PROGRAM.code, MiniProgramOperateEnum.ACTIVITY.code, logTitle, userId, 0, LogResultEnum.LOG_SUCESS.code, null);
        } else {
            // 只能停用结算之前的应用
            throw new BusinessException(StatusContant.MINI_PROGRAM_ACTIVITY_DEACTIVATE_STATUS_WRONG);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void offline(String id, String userId) {
        MiniProgramActivity activity = miniProgramActivityMapper.selectByPrimaryKey(id);
        if (null == activity || activity.getIsDelete() == 1) {
            throw new BusinessException(StatusContant.MINI_PROGRAM_ACTIVITY_NOT_EXIST);
        }
        log.info("下线活动，用户id：{}，活动id：{}，名称：{}", userId, activity.getId(), activity.getName());
        if (ActivityAuditStatusEnum.PASSED.getStatus().equals(activity.getAuditStatus())) {
            // 对于已通过审核活动，运营人员可点击【下线】下线活动。
            activity.setStatus(ActivityStatusEnum.OFFLINE.getStatus());
            activity.setUpdateTime(new Date());
            miniProgramActivityMapper.updateByPrimaryKeySelective(activity);
//            syncLoadActivity2Redis(activity.getId());
            String logTitle = "【下线活动】" + "\n活动名称：" + activity.getName();
            logService.recordOperateLog(ModuleEnum.MINI_PROGRAM.code, MiniProgramOperateEnum.ACTIVITY.code, logTitle, userId, 0, LogResultEnum.LOG_SUCESS.code, null);
        }
    }

    /**
     * 活动审核
     *
     * @param param
     * @param loginIfo4Redis
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @DS("save")
    public void audit(ActivityAuditParam param, LoginIfo4Redis loginIfo4Redis) {
        log.info("活动审核，用户id：{}，请求参数：{}", loginIfo4Redis.getUserId(), JSON.toJSONString(param));

        List<String> ids = param.getIds();
        List<String> activityNames = new ArrayList<>();
        if (param.getApprove()) {
            //同意需要异步处理,数据量太大
            executorService.execute(() -> asyncAuditActivity(ids, loginIfo4Redis));
        } else {
            for (String id : ids) {
                MiniProgramActivity activity = miniProgramActivityMapper.selectByPrimaryKey(id);
                if (null == activity || activity.getIsDelete() == 1) {
                    throw new BusinessException(StatusContant.MINI_PROGRAM_ACTIVITY_NOT_EXIST);
                }
                if (ActivityAuditStatusEnum.DRAFT.getStatus().equals(activity.getAuditStatus())) {
                    // 只能审核已提交审核的活动
                    activity.setAuditStatus(ActivityAuditStatusEnum.DENIED.getStatus());
                    activity.setStatus(ActivityStatusEnum.REJECTED.getStatus());
                    Date now = new Date();
                    activity.setUpdateTime(now);
                    miniProgramActivityMapper.updateByPrimaryKeySelective(activity);
//                    syncLoadActivity2Redis(activity.getId());
                    activityNames.add(activity.getName());
                    StringBuilder logContent = new StringBuilder();
                    logContent.append("【");
                    if (activityNames.size() > 1) {
                        logContent.append("批量");
                    }
                    logContent.append(param.getApprove() ? "同意" : "驳回");
                    logContent.append("】\n");
                    for (String activityName : activityNames) {
                        logContent.append("活动名称：").append(activityName).append("\n");
                    }
                    logService.recordOperateLog(ModuleEnum.MINI_PROGRAM.code, MiniProgramOperateEnum.ACTIVITY.code, logContent.toString(), loginIfo4Redis.getUserId(), 0, LogResultEnum.LOG_SUCESS.code, null);
                } else {
                    throw new BusinessException(StatusContant.MINI_PROGRAM_ACTIVITY_AUDIT_STATUS_WRONG);
                }
            }

        }


    }

    @DS("save")
    public void asyncAuditActivity(List<String> ids, LoginIfo4Redis loginIfo4Redis) {
        List<String> activityNames = new ArrayList<>();
        for (String id : ids) {
            MiniProgramActivity activity = miniProgramActivityMapper.selectByPrimaryKey(id);
            if (null == activity) {
                throw new BusinessException(StatusContant.MINI_PROGRAM_ACTIVITY_NOT_EXIST);
            }
            if (activity.getActive() && ActivityAuditStatusEnum.DRAFT.getStatus().equals(activity.getAuditStatus())) {
                // 只能审核已提交审核的活动
                activity.setStatus(ActivityStatusEnum.AUDITING.getStatus());
                activity.setAuditStatus(ActivityAuditStatusEnum.IN_PROGRESS.getStatus());
                Date now = new Date();
                activity.setUpdateTime(now);
                miniProgramActivityMapper.updateByPrimaryKeySelective(activity);


                log.info("小程序活动审核通过");
                // 对于审核通过的活动，按照不同时间分别设置定时任务修改活动状态，保存参与用户与得奖用户
                if (now.before(activity.getStopTime()) && now.after(activity.getStartTime())) {
                    // 如果在活动开始时间之后，结束时间之前审核通过，则立刻将活动状态变为进行中
                    log.info("审核时间在活动开始之后，结束之前，活动状态变为进行中");
                    activity.setStatus(ActivityStatusEnum.IN_PROGRESS.getStatus());
                    saveActivityConfirmJob(activity);
                    saveActivitySettlementJob(activity);
                    saveActivityFinishJob(activity);
                    // 审核通过，结算之前，保存排位赛参加用户
                    saveRankActivityUsers(activity);
                    saveRankActivityUserAwards(activity);
                } else if (now.before(activity.getConfirmTime()) && now.after(activity.getStopTime())) {
                    // 如果在活动结束时间之后，订单确认时间之前审核通过，则立刻将活动状态变为订单确认中
                    log.info("审核时间在活动结束之后，订单确认时间之前之前，活动状态变为订单确认中");
                    activity.setStatus(ActivityStatusEnum.CONFIRMING.getStatus());
                    saveActivityConfirmJob(activity);
                    saveActivitySettlementJob(activity);
                    saveActivityFinishJob(activity);
                    // 审核通过，订单确认时间之前，保存排位赛参加用户
                    saveRankActivityUsers(activity);
                    saveRankActivityUserAwards(activity);
                } else if (now.before(activity.getSettlementTime()) && now.after(activity.getConfirmTime())) {
                    // 如果在活动结束时间之后，结算时间之前审核通过，则立刻将活动状态变为结算中
                    log.info("审核时间在活动结束之后，结算之前，活动状态变为结算中");
                    activity.setStatus(ActivityStatusEnum.SETTLEMENT.getStatus());
                    saveActivitySettlementJob(activity);
                    saveActivityFinishJob(activity);
                    // 审核通过，结算之前，保存排位赛参加用户
                    saveRankActivityUsers(activity);
                    saveRankActivityUserAwards(activity);
                } else if (now.after(activity.getSettlementTime())) {
                    // 如果在活动结算时间之后审核通过，则立刻将活动状态变为已结束
                    log.info("审核时间在活动结算完成之后，直接结束活动");
                    activity.setStatus(ActivityStatusEnum.FINISH.getStatus());
                } else {
                    // 活动开始之前审核通过，启动3个定时器，分别用来修改活动状态为进行中、结算中、已结束
                    log.info("审核时间在活动开始之前");
                    activity.setStatus(ActivityStatusEnum.READY.getStatus());
                    saveActivityStartJob(activity);
                    saveActivitySettlementJob(activity);
                    saveActivityConfirmJob(activity);
                    saveActivityFinishJob(activity);
                }

                now = new Date();
                activity.setUpdateTime(now);
                activity.setAuditStatus(ActivityAuditStatusEnum.PASSED.getStatus());
                miniProgramActivityMapper.updateByPrimaryKeySelective(activity);
//                syncLoadActivity2Redis(activity.getId());
                activityNames.add(activity.getName());
                StringBuilder logContent = new StringBuilder();
                logContent.append("【");
                if (activityNames.size() > 1) {
                    logContent.append("批量");
                }
                logContent.append("同意");
                logContent.append("】\n");
                for (String activityName : activityNames) {
                    logContent.append("活动名称：").append(activityName).append("\n");
                }
                logService.recordOperateLog(ModuleEnum.MINI_PROGRAM.code, MiniProgramOperateEnum.ACTIVITY.code, logContent.toString(), loginIfo4Redis.getUserId(), 0, LogResultEnum.LOG_SUCESS.code, null);
            } else {
                throw new BusinessException(StatusContant.MINI_PROGRAM_ACTIVITY_AUDIT_STATUS_WRONG);
            }


        }
    }

    @Transactional(rollbackFor = Exception.class)
    @DS("save")
    public void addView(MiniProgramActivity miniProgramActivity, String userId) throws Exception {
        //更新浏览次数
        miniProgramActivity.setViews(miniProgramActivity.getViews() + 1);
        miniProgramActivityMapper.updateByPrimaryKeySelective(miniProgramActivity);
        //添加访问人数,以天为统计
        MiniProgramActivityAccessExample miniProgramActivityAccessExample = new MiniProgramActivityAccessExample();
        MiniProgramActivityAccessExample.Criteria criteria = miniProgramActivityAccessExample.createCriteria();
        criteria.andActivityIdEqualTo(miniProgramActivity.getId());
        criteria.andUserIdEqualTo(userId);
        //获取当前时间
        Date currentTime = new Date();
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
        String dateString = formatter.format(currentTime);
        Date formatTime = DateTimeUtil.getFormatDate(dateString, DateTimeUtil.STANDARD_DAY);
        criteria.andAccessTimeEqualTo(formatTime);
        List<MiniProgramActivityAccess> miniProgramActivityAccessList = miniProgramActivityAccessMapper.selectByExample(miniProgramActivityAccessExample);
        if (miniProgramActivityAccessList == null || miniProgramActivityAccessList.size() == 0) {
            //添加
            MiniProgramActivityAccess miniProgramActivityAccess = new MiniProgramActivityAccess();
            miniProgramActivityAccess.setActivityId(miniProgramActivity.getId());
            miniProgramActivityAccess.setAccessTime(formatTime);
            miniProgramActivityAccess.setUserId(userId);
            miniProgramActivityAccessMapper.insert(miniProgramActivityAccess);
        }

    }

    @Override
    @DS("query")
    public PageData<PageActivityVO> getListFrontPage(PageActivityListFrontParam param, LoginIfo4Redis loginIfo4Redis) {
        Integer pageNum = param.getPageNum();
        Integer pageSize = param.getPageSize();
        if (pageNum < 0 || pageSize < 0) {
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "页码和每页数量必须大于0");
        }
        List<String> activityIds = null;
        if (StringUtils.isEmpty(loginIfo4Redis.getLocation())) {
            Set<String> keys = redisTemplate.keys(Constant.REDIS_KEY_MINI_ACTIVITY_REGION + StringUtils.defaultString(loginIfo4Redis.getBeId()) + "*");
            List<List<String>> allCityIds = redisTemplate.opsForValue().multiGet(keys);
            if (!CollectionUtils.isEmpty(allCityIds)) {
                activityIds = new ArrayList<>();
                allCityIds.forEach(activityIds::addAll);
                activityIds = activityIds.stream().distinct().collect(Collectors.toList());
            }
        } else {
            String key = Constant.REDIS_KEY_MINI_ACTIVITY_REGION + loginIfo4Redis.getBeId() + ":" + loginIfo4Redis.getLocation();
            activityIds = (List<String>) redisTemplate.opsForValue().get(key);
        }

        PageData<PageActivityVO> pageData = new PageData<>();
        pageData.setPage(pageNum);
        if (CollectionUtils.isEmpty(activityIds)) {
            pageData.setCount(0L);
            return pageData;
        }

        Set<String> keys = activityIds.stream().map(x -> Constant.REDIS_KEY_MINI_ACTIVITY_ID + x).collect(Collectors.toSet());
        List<PageActivityVO> activityList = redisTemplate.opsForValue().multiGet(keys);
        activityList = activityList.stream().filter(x -> {
            if (x == null) {
                return false;
            }
            if (Objects.equals(x.getStatus(), ActivityStatusEnum.OFFLINE.getStatus())
                    || Objects.equals(x.getIsDelete(), 1)) {
                return false;
            }
            if (!Objects.equals(x.getAuditStatus(), ActivityAuditStatusEnum.PASSED.getStatus())) {
                return false;
            }
            if (param.getActivityType() != null && !Objects.equals(x.getActivityType(), param.getActivityType())) {
                return false;
            }
            if (param.getStatus() != null && !Objects.equals(x.getStatus(), param.getStatus())) {
                if (x.getActivityType() == 1) {
                    return Objects.equals(x.getStatus(), param.getStatus());
                } else if (x.getActivityType() == 2) {
                    if (param.getStatus() == 2) {
                        return x.getStatus() == 2 || x.getStatus() == 9;
                    } else if (param.getStatus() == 9) {
                        return false;
                    }
                    return Objects.equals(x.getStatus(), param.getStatus());
                }
                return false;


            }
            List<String> userIds = redisCacheUtil.get(Constant.REDIS_KEY_MINI_ACTIVITY_USER + x.getId());
            if (param.getIsParticipate() != null) {

                if (param.getIsParticipate()) {
                    if (CollectionUtils.isEmpty(userIds) || !userIds.contains(loginIfo4Redis.getMallUserId())) {
                        return false;
                    } else {
                        x.setParticipatedUserIds(userIds);
                    }
                } else {
                    if (CollectionUtils.isNotEmpty(userIds) && userIds.contains(loginIfo4Redis.getMallUserId())) {
                        return false;
                    } else {
                        x.setParticipatedUserIds(userIds);
                    }
                }
            }else{
                x.setParticipatedUserIds(userIds);
            }
            return true;
        }).sorted(Comparator.comparing(PageActivityVO::getCreateTime).reversed()).collect(Collectors.toList());
        Integer count = activityList.size();
        pageData.setCount(count);
        Integer start = (pageNum - 1) * pageSize;
        Integer end = Math.min(pageNum * pageSize, activityList.size());
        if (start < activityList.size()) {
            List<PageActivityVO> pageActivityVOS = activityList.subList(start, end);
            pageActivityVOS.forEach(x -> {
                x.setIsParticipate(CollectionUtils.isNotEmpty(x.getParticipatedUserIds())
                        && x.getParticipatedUserIds().contains(loginIfo4Redis.getMallUserId()));
                x.setParticipatedUserIds(null);
            });
            pageData.setData(pageActivityVOS);
        }
//        List<PageActivityVO> list = miniProgramActivityMapperExt.getListFrontPage(param.getActivityType(), param.getStatus(), param.getIsParticipate(), loginIfo4Redis.getMallUserId(), loginIfo4Redis.getBeId(), loginIfo4Redis.getLocation());
//        //List<PageActivityVO> list = miniProgramActivityMapperExt.getListFrontPage(null,null,null);
//        PageInfo<PageActivityVO> pageInfo = new PageInfo<>(list);
//        pageData.setCount(pageInfo.getTotal());
//        pageData.setData(list);
        return pageData;
    }

    private PageActivityVO activityRO2PageActivityVO(ActivityRO activityRO, String userId) {
        PageActivityVO vo = new PageActivityVO();
        BeanUtils.copyProperties(activityRO, vo);
        vo.setIsParticipate(activityRO.getParticipatedUserIds().contains(userId));
        return vo;
    }

    @Override
    @DS("query")
    public PageFrontRankDetailVO getFrontPageRankDetail(String activityId, String userId) throws Exception {
        PageFrontRankDetailVO pageFrontDetailVO = new PageFrontRankDetailVO();
        MiniProgramActivity miniProgramActivity = miniProgramActivityMapper.selectByPrimaryKey(activityId);
        log.info("userId:{}", userId);
        if (miniProgramActivity == null || miniProgramActivity.getIsDelete() == 1 || ActivityStatusEnum.OFFLINE.getStatus().equals(miniProgramActivity.getStatus())) {
            throw new BusinessException(StatusContant.MINI_PROGRAM_ACTIVITY_NOT_EXIST);
        }
        //根据id去查userId
        UserMiniProgram userMiniProgram = userMiniProgramMapper.selectByPrimaryKey(userId);
        if (userMiniProgram == null) {
            throw new BusinessException(StatusContant.MINI_PROGRAM_ACTIVITY_USER_NOT_EXIST);
        }
        BeanUtils.copyProperties(miniProgramActivity, pageFrontDetailVO);
        //解析活动对象
        List<Integer> targetList = new ArrayList<>();
        if ((miniProgramActivity.getTarget() & TARGET_MASK_MANAGER) != 0) {
            //客户经理0x01
            targetList.add(1);
        }
        if ((miniProgramActivity.getTarget() & TARGET_MASK_DISTRIBUTOR) != 0) {
            //分销员0x02
            targetList.add(2);
        }
        if ((miniProgramActivity.getTarget() & TARGET_MASK_CHANNEL) != 0) {
            //渠道商0x04
            targetList.add(4);
        }
        pageFrontDetailVO.setTargetList(targetList);
        List<MiniProgramActivityRank> miniProgramActivityRanks = miniProgramActivityRankMapper.selectByExample(new MiniProgramActivityRankExample().createCriteria().andActivityIdEqualTo(activityId).example());
        if (miniProgramActivityRanks == null || miniProgramActivityRanks.size() == 0) {
            throw new BusinessException(StatusContant.MINI_PROGRAM_ACTIVITY_RANK_NOT_EXIST);
        }
        MiniProgramActivityRank miniProgramActivityRank = miniProgramActivityRanks.get(0);
        BeanUtils.copyProperties(miniProgramActivityRank, pageFrontDetailVO);
        if (!StringUtils.isEmpty(userMiniProgram.getUserId())) {

            //获取活动数据
            PageFrontRankDetailVO.ActivityData activityData = new PageFrontRankDetailVO.ActivityData();
            //获取奖品名称
            List<MiniProgramActivityUserAward> miniProgramActivityUserAwards = miniProgramActivityUserAwardMapper.selectByExample(new MiniProgramActivityUserAwardExample().createCriteria()
                    .andActivityIdEqualTo(activityId)
                    .andUserIdEqualTo(userMiniProgram.getUserId())
                    .example());
            if (miniProgramActivityUserAwards != null && miniProgramActivityUserAwards.size() > 0) {

                MiniProgramActivityUserAward miniProgramActivityUserAward = miniProgramActivityUserAwards.get(0);
                activityData.setRankNumber(miniProgramActivityUserAward.getRanking());
                MiniProgramActivityRankAward miniProgramActivityRankAward = miniProgramActivityRankAwardMapper.selectByPrimaryKey(miniProgramActivityUserAward.getAwardId());
                if (miniProgramActivityRankAward != null && miniProgramActivityRankAward.getType() == 1) {
                    activityData.setRankAward(miniProgramActivityRankAward.getPoints() + "积分");
                } else if (miniProgramActivityRankAward != null && miniProgramActivityRankAward.getType() == 2) {
                    activityData.setRankAward(miniProgramActivityRankAward.getAwardName());
                }
            }
            ActivityDataRankParam param = new ActivityDataRankParam();
            param.setId(activityId);
            param.setUserId(userMiniProgram.getUserId());
            //获取实时订单数据
            ActivityStatisticsVO activityStatisticsVO = miniProgramActivityMapperExt.getTotalNum(activityId, userMiniProgram.getUserId());
            if (activityStatisticsVO != null) {
                activityData.setOrderAmount(activityStatisticsVO.getAtomOrderAmount());
                activityData.setOrderNumber(activityStatisticsVO.getAtomOrderCount());
            }

            //判断是否可参与
//        List<MiniProgramActivityUser> miniProgramActivityUsers = miniProgramActivityUserMapper.selectByExample(new MiniProgramActivityUserExample().createCriteria().andActivityIdEqualTo(activityId).andUserIdEqualTo(userMiniProgram.getUserId()).example());
//        if (miniProgramActivityUsers != null && miniProgramActivityUsers.size() > 0) {
//            pageFrontDetailVO.setCanJoin(true);
//        } else {
//            pageFrontDetailVO.setCanJoin(false);
//        }

            pageFrontDetailVO.setCanJoin(isCanJoinForRank(activityId, userId));
            //判断是否已参与
            List<MiniProgramActivityUser> miniProgramActivityUsers = miniProgramActivityUserMapper.selectByExample(new MiniProgramActivityUserExample().createCriteria().andActivityIdEqualTo(activityId).andUserIdEqualTo(userMiniProgram.getUserId()).example());
            if (miniProgramActivityUsers != null && miniProgramActivityUsers.size() > 0) {
                pageFrontDetailVO.setIsParticipate(true);
            } else {
                pageFrontDetailVO.setIsParticipate(false);
            }
            pageFrontDetailVO.setActivityData(activityData);
        }
        //添加访问人数
        executorService.execute(() -> {
            try {
                addView(miniProgramActivity, userId);
            } catch (Exception e) {
                log.info("getFrontPageRankDetail error:{}", e.getMessage());
            }
        });
        return pageFrontDetailVO;
    }

    @Override
    @DS("query")
    public PageFrontWeeklyDetailVO getFrontPageWeeklyDetail(String activityId, String userId) throws Exception {
        PageFrontWeeklyDetailVO pageFrontWeeklyDetailVO = new PageFrontWeeklyDetailVO();
        MiniProgramActivity miniProgramActivity = miniProgramActivityMapper.selectByPrimaryKey(activityId);
        if (miniProgramActivity == null || miniProgramActivity.getIsDelete() == 1 || ActivityStatusEnum.OFFLINE.getStatus().equals(miniProgramActivity.getStatus())) {
            throw new BusinessException(StatusContant.MINI_PROGRAM_ACTIVITY_NOT_EXIST);
        }
        BeanUtils.copyProperties(miniProgramActivity, pageFrontWeeklyDetailVO);
        List<MiniProgramActivityWeeklyFun> miniProgramActivityWeeklyFuns = miniProgramActivityWeeklyFunMapper.selectByExample(new MiniProgramActivityWeeklyFunExample().createCriteria().andActivityIdEqualTo(activityId).example());
        if (miniProgramActivityWeeklyFuns == null || miniProgramActivityWeeklyFuns.size() == 0) {
            throw new BusinessException(StatusContant.MINI_PROGRAM_ACTIVITY_RANK_NOT_EXIST);
        }

        MiniProgramActivityWeeklyFun miniProgramActivityWeeklyFun = miniProgramActivityWeeklyFuns.get(0);
        BeanUtils.copyProperties(miniProgramActivityWeeklyFun, pageFrontWeeklyDetailVO);
        //获取奖品列表
        List<MiniProgramActivityWeeklyFunAward> miniProgramActivityWeeklyFunAwards = miniProgramActivityWeeklyFunAwardMapper.selectByExample(new MiniProgramActivityWeeklyFunAwardExample().createCriteria().andActivityIdEqualTo(activityId).example());
        pageFrontWeeklyDetailVO.setAwards(miniProgramActivityWeeklyFunAwards);

        //添加访问人数
        executorService.execute(() -> {
            try {
                addView(miniProgramActivity, userId);
            } catch (Exception e) {
                log.info("getFrontPageRankDetail error:{}", e.getMessage());
            }
        });
        //判断抽奖资格
        UserMiniProgram userMiniProgram = userMiniProgramMapper.selectByPrimaryKey(userId);
        if (userMiniProgram == null) {
            pageFrontWeeklyDetailVO.setQualification(0);
            pageFrontWeeklyDetailVO.setCanJoin(false);
            log.info("用户无抽奖资格:用户不存在,用户id:{},活动id:{}", userId, activityId);
            return pageFrontWeeklyDetailVO;
        }
        if (!StringUtils.isEmpty(userMiniProgram.getUserId())) {
            //获取中奖奖品id
            List<MiniProgramActivityUserAward> miniProgramActivityUserAwards = miniProgramActivityUserAwardMapper.selectByExample(new MiniProgramActivityUserAwardExample().createCriteria().andActivityIdEqualTo(activityId).andUserIdEqualTo(userMiniProgram.getUserId()).example());
            pageFrontWeeklyDetailVO.setIsParticipate(false);
            //已抽奖

            if (miniProgramActivityUserAwards != null && miniProgramActivityUserAwards.size() > 0) {
                pageFrontWeeklyDetailVO.setAwardId(miniProgramActivityUserAwards.get(0).getAwardId());
                pageFrontWeeklyDetailVO.setQualification(0);
                pageFrontWeeklyDetailVO.setIsParticipate(true);
                pageFrontWeeklyDetailVO.setCanJoin(true);
                log.info("用户已抽奖,用户id:{},活动id:{},奖品id:{}", userMiniProgram.getUserId(), activityId, miniProgramActivityUserAwards.get(0).getAwardId());
                return pageFrontWeeklyDetailVO;
            }
        }
        //判断是否已达最大抽奖人数
        Boolean isMaxNum = false;
        Boolean isParticipate = false;
        List<MiniProgramActivityUser> miniProgramActivityUsers = miniProgramActivityUserMapper.selectByExample(new MiniProgramActivityUserExample().createCriteria().andActivityIdEqualTo(activityId).example());
        MiniProgramActivityUser miniProgramActivityUser = new MiniProgramActivityUser();
        if (miniProgramActivityUsers != null) {
            for (MiniProgramActivityUser m : miniProgramActivityUsers) {
                if (StringUtils.equals(m.getUserId(),userMiniProgram.getUserId())) {
                    isParticipate = true;
                    miniProgramActivityUser = m;
                }
            }
        }

        if (miniProgramActivityUsers != null && miniProgramActivityUsers.size() >= miniProgramActivityWeeklyFuns.get(0).getMaxPlayer()) {
            isMaxNum = true;
        }

        //判断是不是直接添加的用户
        if (isParticipate) {
            boolean isNotDrawnAndNotMaxNum = (miniProgramActivityUser.getIsDraw() == null || miniProgramActivityUser.getIsDraw() == 0) && !isMaxNum;
            pageFrontWeeklyDetailVO.setQualification(isNotDrawnAndNotMaxNum ? 1 : 0);
            pageFrontWeeklyDetailVO.setIsParticipate(true);
            pageFrontWeeklyDetailVO.setCanJoin(true);
            return pageFrontWeeklyDetailVO;
        }
        //其他用户
        if (isMaxNum) {
            log.info("用户无抽奖资格:已达最大抽奖人数,用户id:{},活动id:{}", userMiniProgram.getUserId(), activityId);
            pageFrontWeeklyDetailVO.setQualification(0);
            pageFrontWeeklyDetailVO.setCanJoin(false);
            return pageFrontWeeklyDetailVO;
        }

        //判断活动对象身份
        List<String> targetList = new ArrayList<>();
        if ((miniProgramActivity.getTarget() & 0x01) != 0) {
            //客户经理0x01
            targetList.add("4");
        }
        if ((miniProgramActivity.getTarget() & 0x02) != 0) {
            //分销员0x02
            targetList.add("1");
            targetList.add("2");
        }
        if ((miniProgramActivity.getTarget() & 0x04) != 0) {
            //渠道0x04
            targetList.add("3");
        }
        if (!targetList.contains(userMiniProgram.getRoleType())) {
            log.info("用户无抽奖资格:用户角色不在活动角色范围内,用户id:{},活动id:{}", userMiniProgram.getUserId(), activityId);

            pageFrontWeeklyDetailVO.setQualification(0);
            pageFrontWeeklyDetailVO.setCanJoin(false);
            return pageFrontWeeklyDetailVO;
        }
        //判断活动对象省份
        List<MiniProgramActivityRegion> miniProgramActivityRegions = miniProgramActivityRegionMapper.selectByExample(new MiniProgramActivityRegionExample().createCriteria().andActivityIdEqualTo(activityId).example());
        List<String> provinceList = new ArrayList<>();
        List<String> cityList = new ArrayList<>();
        if (miniProgramActivityRegions != null && miniProgramActivityRegions.size() > 0) {
            for (MiniProgramActivityRegion miniProgramActivityRegion : miniProgramActivityRegions) {
                if (!provinceList.contains(miniProgramActivityRegion.getProvinceCode())) {
                    provinceList.add(miniProgramActivityRegion.getProvinceCode());
                }
                if (!cityList.contains(miniProgramActivityRegion.getCityCode())) {
                    cityList.add(miniProgramActivityRegion.getCityCode());
                }

            }
            if (!provinceList.contains(userMiniProgram.getBeId())) {
                log.info("用户无抽奖资格:用户不在活动区域内");
                pageFrontWeeklyDetailVO.setQualification(0);
                pageFrontWeeklyDetailVO.setCanJoin(false);
                return pageFrontWeeklyDetailVO;
            } else {
                if (!ObjectUtils.isEmpty(userMiniProgram.getLocation()) && !cityList.contains(userMiniProgram.getLocation())) {
                    log.info("用户无抽奖资格:用户不在活动区域内");
                    pageFrontWeeklyDetailVO.setQualification(0);
                    pageFrontWeeklyDetailVO.setCanJoin(false);
                    return pageFrontWeeklyDetailVO;
                }
            }

        } else {
            log.info("用户无抽奖资格:活动区域为空,用户id:{},活动id:{}", userMiniProgram.getUserId(), activityId);

            pageFrontWeeklyDetailVO.setCanJoin(false);
            pageFrontWeeklyDetailVO.setQualification(0);
            return pageFrontWeeklyDetailVO;
        }

        //判断销售人员注册时间
        if (miniProgramActivityWeeklyFun.getRegisterStart() != null && miniProgramActivityWeeklyFun.getRegisterStop() != null) {
            if (miniProgramActivityWeeklyFun.getRegisterStart().before(miniProgramActivity.getStartTime()) || miniProgramActivityWeeklyFun.getRegisterStop().after(miniProgramActivity.getStopTime())) {
                log.info("用户无抽奖资格:销售人员注册时间不在范围内,用户id:{},活动id:{}", userMiniProgram.getUserId(), activityId);

                pageFrontWeeklyDetailVO.setCanJoin(false);
                pageFrontWeeklyDetailVO.setQualification(0);
                return pageFrontWeeklyDetailVO;
            }
        }

        //判断开始时间，结束时间内，达到一定数量订单数量
        Integer orderNum = 0;
        //获取活动选定的spu列表
        List<MiniProgramActivitySpuCode> miniProgramActivitySpuCodes = miniProgramActivitySpuCodeMapper.selectByExample(new MiniProgramActivitySpuCodeExample().createCriteria().andActivityIdEqualTo(activityId).example());
        List<String> spuCodeList = new ArrayList<>();
        if (miniProgramActivitySpuCodes != null && miniProgramActivitySpuCodes.size() > 0) {
            for (MiniProgramActivitySpuCode miniProgramActivitySpuCode : miniProgramActivitySpuCodes) {
                if (!spuCodeList.contains(miniProgramActivitySpuCode.getSpuOfferingCode())) {
                    spuCodeList.add(miniProgramActivitySpuCode.getSpuOfferingCode());
                }

            }
        }
        // 产品范式
        List<String> offeringClassList = new ArrayList<>();
        List<MiniProgramActivityOfferingClass> offeringClasses = miniProgramActivityOfferingClassMapper.selectByExample(
                new MiniProgramActivityOfferingClassExample().createCriteria().andActivityIdEqualTo(activityId).example()
        );
        if (!CollectionUtils.isEmpty(offeringClasses)) {
            offeringClassList = offeringClasses.stream().map(MiniProgramActivityOfferingClass::getSpuOfferingClass).collect(Collectors.toList());

        }
        if (!StringUtils.isEmpty(userMiniProgram.getUserId())) {
            orderNum = miniProgramActivityMapperExt.getOrderNum(miniProgramActivityWeeklyFun.getOrderStart(), miniProgramActivityWeeklyFun.getOrderStop(), spuCodeList, offeringClassList, userMiniProgram.getUserId());

            if (orderNum < miniProgramActivityWeeklyFun.getOrderCount()) {
                log.info("用户无抽奖资格:销售人员订单量不达标,用户id:{},活动id:{},订单数量：{}", userMiniProgram.getUserId(), activityId, orderNum);

                pageFrontWeeklyDetailVO.setCanJoin(false);
                pageFrontWeeklyDetailVO.setQualification(0);
                return pageFrontWeeklyDetailVO;
            }
        }

        pageFrontWeeklyDetailVO.setIsParticipate(true);
        pageFrontWeeklyDetailVO.setQualification(1);
        pageFrontWeeklyDetailVO.setCanJoin(true);
        return pageFrontWeeklyDetailVO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @DS("save")
    public MiniProgramActivityWeeklyFunAward weeklyLottery(String activityId, String userId) {
        //判断活动是否存在
        MiniProgramActivity miniProgramActivity = miniProgramActivityMapper.selectByPrimaryKey(activityId);
        if (miniProgramActivity == null || miniProgramActivity.getIsDelete() == 1) {
            throw new BusinessException(StatusContant.MINI_PROGRAM_ACTIVITY_NOT_EXIST);
        }
        //抽奖
        //获取所有奖品
        List<MiniProgramActivityWeeklyFunAward> miniProgramActivityWeeklyFunAwards = miniProgramActivityWeeklyFunAwardMapper.selectByExample(new MiniProgramActivityWeeklyFunAwardExample().createCriteria().andActivityIdEqualTo(activityId).example());
        if (miniProgramActivityWeeklyFunAwards == null || miniProgramActivityWeeklyFunAwards.size() == 0) {
            throw new BusinessException(StatusContant.MINI_PROGRAM_ACTIVITY_AWARD_NOT_EXIST);
        }
        //判断活动状态
        if (miniProgramActivity.getStatus() != 2) {
            throw new BusinessException(StatusContant.MINI_PROGRAM_ACTIVITY_NOT_UNDER_WAY);
        }
        UserMiniProgram userMiniProgram = userMiniProgramMapper.selectByPrimaryKey(userId);
        if (userMiniProgram == null) {
            throw new BusinessException(StatusContant.MINI_PROGRAM_ACTIVITY_USER_NOT_EXIST);
        }
        return redisUtil.smartLock(ACTIVITY_LOCK + activityId, () -> {
                    String awardId = draw(miniProgramActivityWeeklyFunAwards);
                    //判断用户是否参与了活动，没参与，则添加
                    Boolean isParticipate = false;
                    List<MiniProgramActivityUser> miniProgramActivityUsers = miniProgramActivityUserMapper.selectByExample(new MiniProgramActivityUserExample().createCriteria().andActivityIdEqualTo(activityId).example());
                    if (miniProgramActivityUsers != null) {
                        for (MiniProgramActivityUser miniProgramActivityUser : miniProgramActivityUsers) {
                            if (miniProgramActivityUser.getUserId().equals(userMiniProgram.getUserId())) {
                                isParticipate = true;
                            }
                        }
                    }
                    if (!isParticipate) {
                        MiniProgramActivityUser miniProgramActivityUser = new MiniProgramActivityUser();
                        //UserMiniProgram userMiniProgram = userMiniProgramMapper.selectByPrimaryKey(userId);
                        miniProgramActivityUser.setActivityId(activityId);
                        miniProgramActivityUser.setUserId(userMiniProgram.getUserId());
                        miniProgramActivityUser.setIsDraw(1);
                        //miniProgramActivityUser.setRoleType(userMiniProgram.getRoleType());
                        miniProgramActivityUser.setId(BaseServiceUtils.getId());
                        miniProgramActivityUserMapper.insert(miniProgramActivityUser);
//                        TransactionUtil.afterCommit(() -> syncLoadUser2Redis(activityId, userMiniProgram.getUserId()));
                    } else {
                        MiniProgramActivityUser miniProgramActivityUser = miniProgramActivityUsers.get(0);
                        if (miniProgramActivityUser.getIsDraw() != null && miniProgramActivityUser.getIsDraw() == 1) {
                            throw new BusinessException(StatusContant.MINI_PROGRAM_ACTIVITY_USER_DRAW);
                        }
                        miniProgramActivityUser.setIsDraw(1);
                        miniProgramActivityUserMapper.updateByPrimaryKeySelective(miniProgramActivityUser);
                    }
                    //抽奖已达到最大参与人数,则直接进入结算中状态
                    List<MiniProgramActivityWeeklyFun> miniProgramActivityWeeklyFuns = miniProgramActivityWeeklyFunMapper.selectByExample(new MiniProgramActivityWeeklyFunExample().createCriteria().andActivityIdEqualTo(activityId).example());
                    if (miniProgramActivityWeeklyFuns.isEmpty()) {
                        throw new BusinessException(StatusContant.MINI_PROGRAM_ACTIVITY_NOT_EXIST);
                    }
                    Integer nowPlayer = miniProgramActivityUsers != null ? miniProgramActivityUsers.size() + 1 : 1;
                    if (nowPlayer >= miniProgramActivityWeeklyFuns.get(0).getMaxPlayer()) {
                        miniProgramActivity.setStatus(ActivityStatusEnum.SETTLEMENT.getStatus());
                        miniProgramActivity.setUpdateTime(new Date());
                        miniProgramActivityMapper.updateByPrimaryKeySelective(miniProgramActivity);
//                        TransactionUtil.afterCommit(() -> syncLoadActivity2Redis(activityId));
                        //为了防止用户等待太久，采用异步方式进行排名
                        executorService.execute(() -> {
                            log.info("抽奖已达到最大参与人数，执行小程序活动开始结算任务，活动id：{}，活动名称：{}", miniProgramActivity.getId(), miniProgramActivity.getName());
                            miniActivityService.saveRankActivityUserAwards(miniProgramActivity);
                        });


                    }
                    if (awardId.equals("谢谢惠顾")) {

                        return null;
                    } else {
                        MiniProgramActivityWeeklyFunAward miniProgramActivityWeeklyFunAward = miniProgramActivityWeeklyFunAwardMapper.selectByExample(new MiniProgramActivityWeeklyFunAwardExample().createCriteria().andActivityIdEqualTo(activityId).andIdEqualTo(awardId).example()).get(0);
                        //判断奖品是否还有剩余，以及今日是否还有剩余
                        //计算剩余总量
                        List<MiniProgramActivityUserAward> miniProgramActivityUserAwards = miniProgramActivityUserAwardMapper.selectByExample(new MiniProgramActivityUserAwardExample().createCriteria().andActivityIdEqualTo(activityId).andAwardIdEqualTo(awardId).example());
                        if (miniProgramActivityUserAwards != null && miniProgramActivityUserAwards.size() == miniProgramActivityWeeklyFunAward.getMaxAwards()) {
                            log.info("奖品总量已抽完,活动id:{},奖品id:{},最大总量:{}，已抽:{}", miniProgramActivityWeeklyFunAward.getMaxAwards(), miniProgramActivityUserAwards.size(), activityId, awardId);
                            return null;
                        }
                        //计算今日剩余量
                        LocalDateTime todayStart = LocalDateTime.of(LocalDateTime.now().toLocalDate(), LocalTime.MIN);
                        LocalDateTime todayEnd = LocalDateTime.of(LocalDateTime.now().toLocalDate(), LocalTime.MAX);
                        // 将LocalDateTime转换为ZonedDateTime，使用系统默认时区
                        ZonedDateTime zonedDateTimeStart = todayStart.atZone(ZoneId.systemDefault());
                        ZonedDateTime zonedDateTimeEnd = todayEnd.atZone(ZoneId.systemDefault());
                        // 将ZonedDateTime转换为Date
                        Date todayStartDate = Date.from(zonedDateTimeStart.toInstant());
                        Date todayEndDate = Date.from(zonedDateTimeEnd.toInstant());
                        List<MiniProgramActivityUserAward> miniProgramActivityUserAwardsToday = miniProgramActivityUserAwardMapper.selectByExample(new MiniProgramActivityUserAwardExample().createCriteria()
                                .andActivityIdEqualTo(activityId)
                                .andAwardIdEqualTo(awardId)
                                .andCreateTimeGreaterThanOrEqualTo(todayStartDate)
                                .andCreateTimeLessThanOrEqualTo(todayEndDate)
                                .example());
                        if (miniProgramActivityUserAwardsToday != null && miniProgramActivityUserAwardsToday.size() == miniProgramActivityWeeklyFunAward.getMaxAwardsDaily()) {
                            log.info("奖品今日总量已抽完,活动id:{},奖品id:{},今日最大总量:{}，已抽:{}", miniProgramActivityWeeklyFunAward.getMaxAwardsDaily(), miniProgramActivityUserAwardsToday.size(), activityId, awardId);
                            return null;
                        }

                        MiniProgramActivityUserAward miniProgramActivityUserAward = new MiniProgramActivityUserAward();
                        miniProgramActivityUserAward.setId(BaseServiceUtils.getId());
                        miniProgramActivityUserAward.setActivityId(activityId);
                        miniProgramActivityUserAward.setAwardId(awardId);
                        miniProgramActivityUserAward.setUserId(userMiniProgram.getUserId());
                        miniProgramActivityUserAward.setCreateTime(new Date());
                        miniProgramActivityUserAward.setUpdateTime(new Date());
                        miniProgramActivityUserAward.setStatus(0);
                        miniProgramActivityUserAwardMapper.insert(miniProgramActivityUserAward);
                        return miniProgramActivityWeeklyFunAward;
                    }
                }
        );

    }

    @Override
    @DS("query")
    public BaseAnswer<PageData<ActivityAwardListVO>> getOwnAward(Integer pageNum, Integer pageSize, String userId) {

        if (pageNum < 0 || pageSize < 0) {
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "页码和每页数量必须大于0");
        }

        PageData<ActivityAwardListVO> pageData = new PageData<>();
        pageData.setPage(pageNum);

        //判断用户是否存在
        UserMiniProgram userMiniProgram = userMiniProgramMapper.selectByPrimaryKey(userId);
        if (userMiniProgram == null || userMiniProgram.getUserId() == null) {
            pageData.setCount(0);
            pageData.setData(null);
            return BaseAnswer.success(pageData);
        }
        PageHelper.startPage(pageNum, pageSize);
        List<ActivityAwardListVO> activityAwardListVOS = miniProgramActivityMapperExt.selectActivityAwardList(userMiniProgram.getUserId());
        PageInfo<ActivityAwardListVO> pageInfo = new PageInfo<>(activityAwardListVOS);
        pageData.setCount(pageInfo.getTotal());
        pageData.setData(activityAwardListVOS);
        return BaseAnswer.success(pageData);
    }

    @Override
    @DS("query")
    public BaseAnswer<Integer> getOwnAwardNotReceived(String userId) {

        List<MiniProgramActivityUserAward> miniProgramActivityUserAwards = miniProgramActivityUserAwardMapper.selectByExample(new MiniProgramActivityUserAwardExample().createCriteria().andUserIdEqualTo(userId).andStatusEqualTo(ActivityAwardStatusEnum.NOT_RECEIVED.getStatus()).example());
        return miniProgramActivityUserAwards != null ? BaseAnswer.success(miniProgramActivityUserAwards.size()) : BaseAnswer.success(0);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @DS("save")
    public BaseAnswer<Void> choiceAwardAddress(String activityId, String awardId, String addressId, String userId) {
        //判断用户是否存在
        UserMiniProgram userMiniProgram = userMiniProgramMapper.selectByPrimaryKey(userId);

        if (userMiniProgram == null) {
            throw new BusinessException(StatusContant.MINI_PROGRAM_ACTIVITY_USER_NOT_EXIST);
        }
        //判断奖品是否存在
        List<MiniProgramActivityUserAward> miniProgramActivityUserAwards = miniProgramActivityUserAwardMapper.selectByExample(new MiniProgramActivityUserAwardExample().createCriteria().andActivityIdEqualTo(activityId).andUserIdEqualTo(userMiniProgram.getUserId()).andAwardIdEqualTo(awardId).example());
        if (miniProgramActivityUserAwards == null || miniProgramActivityUserAwards.size() == 0) {
            throw new BusinessException(StatusContant.MINI_PROGRAM_ACTIVITY_AWARD_NOT_EXIST);
        }
        MiniProgramActivityUserAward miniProgramActivityUserAward = miniProgramActivityUserAwards.get(0);
        //判断用户地址是否存在
        List<UserMiniProgramAddress> userMiniProgramAddresses = userMiniProgramAddressMapper.selectByExample(new UserMiniProgramAddressExample().createCriteria().andIdEqualTo(addressId).example());
        if (userMiniProgramAddresses == null || userMiniProgramAddresses.size() == 0) {
            throw new BusinessException(StatusContant.MINI_PROGRAM_ACTIVITY_USER_ADDRESS_NOT_EXIST);
        }

        //判断之前奖品是否有收货地址，且这个地址是不是被软删除，而且也没有其他奖品关联这个地址，则删除此地址
        if (miniProgramActivityUserAward.getAddressId() != null) {
            List<UserMiniProgramAddress> userMiniProgramAddresseDel = userMiniProgramAddressMapper.selectByExample(new UserMiniProgramAddressExample().createCriteria().andIdEqualTo(miniProgramActivityUserAward.getAddressId()).andIsDeleteEqualTo(1).example());
            if (userMiniProgramAddresseDel != null && userMiniProgramAddresseDel.size() > 0) {
                //判断是否还有除了本身还有关联的奖品
                if (miniProgramActivityUserAwards.size() == 1) {
                    userMiniProgramAddressMapper.deleteByPrimaryKey(miniProgramActivityUserAward.getAddressId());
                }
            }
        }
        miniProgramActivityUserAward.setAddressId(addressId);
        miniProgramActivityUserAward.setStatus(1);
        miniProgramActivityUserAwardMapper.updateByPrimaryKeySelective(miniProgramActivityUserAward);

        return BaseAnswer.success(null);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @DS("save")
    public BaseAnswer<Void> deliveryAward(String activityId, String awardId, String logisticsCode, String userId) {
        List<MiniProgramActivityUserAward> miniProgramActivityUserAwards = miniProgramActivityUserAwardMapper.selectByExample(new MiniProgramActivityUserAwardExample().createCriteria().andActivityIdEqualTo(activityId).andUserIdEqualTo(userId).andAwardIdEqualTo(awardId).example());
        if (miniProgramActivityUserAwards == null || miniProgramActivityUserAwards.size() == 0) {
            logService.recordOperateLog(ModuleEnum.MINI_PROGRAM.code, MiniProgramOperateEnum.ACTIVITY.code,
                    "-", userId, 0, LogResultEnum.LOG_FAIL.code, StatusContant.MINI_PROGRAM_ACTIVITY_AWARD_NOT_EXIST.getMessage());
            throw new BusinessException(StatusContant.MINI_PROGRAM_ACTIVITY_AWARD_NOT_EXIST);
        }
        MiniProgramActivity miniProgramActivity = miniProgramActivityMapper.selectByPrimaryKey(activityId);
        if (miniProgramActivity == null || miniProgramActivity.getIsDelete() == 1) {
            logService.recordOperateLog(ModuleEnum.MINI_PROGRAM.code, MiniProgramOperateEnum.ACTIVITY.code,
                    "-", userId, 0, LogResultEnum.LOG_FAIL.code, StatusContant.MINI_PROGRAM_ACTIVITY_NOT_EXIST.getMessage());
            throw new BusinessException(StatusContant.MINI_PROGRAM_ACTIVITY_NOT_EXIST);
        }
        //没有收货地址，不允许进行单号配置
        List<UserMiniProgramAddress> userMiniProgramAddressAll = userMiniProgramAddressMapper.selectByExample(new UserMiniProgramAddressExample().createCriteria().andUserIdEqualTo(userId).example());
        if (userMiniProgramAddressAll == null || userMiniProgramAddressAll.size() == 0) {
            logService.recordOperateLog(ModuleEnum.MINI_PROGRAM.code, MiniProgramOperateEnum.ACTIVITY.code,
                    "-", userId, 0, LogResultEnum.LOG_FAIL.code, StatusContant.MINI_PROGRAM_ACTIVITY_USER_ADDRESS_NOT_EXIST.getMessage());
            throw new BusinessException(StatusContant.MINI_PROGRAM_ACTIVITY_USER_ADDRESS_NOT_EXIST);
        }
        MiniProgramActivityUserAward miniProgramActivityUserAward = miniProgramActivityUserAwards.get(0);
        miniProgramActivityUserAward.setLogisticsCode(logisticsCode);
        miniProgramActivityUserAward.setStatus(2);
        miniProgramActivityUserAwardMapper.updateByPrimaryKeySelective(miniProgramActivityUserAward);
        SingleNumberConfigLogVO singleNumberConfigLogVO = new SingleNumberConfigLogVO();
        //记录日志
        // 获取用户信息
        List<UserMiniProgram> userMiniPrograms = userMiniProgramMapper.selectByExample(new UserMiniProgramExample().createCriteria().andUserIdEqualTo(userId).example());

        if (userMiniPrograms == null || userMiniPrograms.size() == 0) {
            logService.recordOperateLog(ModuleEnum.MINI_PROGRAM.code, MiniProgramOperateEnum.ACTIVITY.code,
                    "-", userId, 0, LogResultEnum.LOG_FAIL.code, StatusContant.MINI_PROGRAM_ACTIVITY_USER_NOT_EXIST.getMessage());
            throw new BusinessException(StatusContant.MINI_PROGRAM_ACTIVITY_USER_NOT_EXIST);
        }
        UserMiniProgram userMiniProgram = userMiniPrograms.get(0);
        singleNumberConfigLogVO.setName(userMiniProgram.getName());
        //获取奖品信息
        String headerName = "";
        if (miniProgramActivity.getActivityType() == 1) {
            headerName = "【活动排名-单号配置】";
            MiniProgramActivityRankAward miniProgramActivityRankAward = miniProgramActivityRankAwardMapper.selectByPrimaryKey(miniProgramActivityUserAward.getAwardId());
            if (miniProgramActivityRankAward != null) {
                singleNumberConfigLogVO.setAwardName(miniProgramActivityRankAward.getAwardName());
            }

        } else if (miniProgramActivity.getActivityType() == 2) {
            headerName = "【查看中奖名单-单号配置】";
            MiniProgramActivityWeeklyFunAward miniProgramActivityWeeklyFunAward = miniProgramActivityWeeklyFunAwardMapper.selectByPrimaryKey(miniProgramActivityUserAward.getAwardId());
            if (miniProgramActivityWeeklyFunAward != null) {
                singleNumberConfigLogVO.setAwardName(miniProgramActivityWeeklyFunAward.getAwardName());
            }

        } else {
            logService.recordOperateLog(ModuleEnum.MINI_PROGRAM.code, MiniProgramOperateEnum.ACTIVITY.code,
                    "-", userId, 0, LogResultEnum.LOG_FAIL.code, StatusContant.MINI_PROGRAM_ACTIVITY_TYPR_ERROR.getMessage());
            throw new BusinessException(StatusContant.MINI_PROGRAM_ACTIVITY_TYPR_ERROR);
        }

        singleNumberConfigLogVO.setLogisticsCode(logisticsCode);


        logService.recordOperateLog(ModuleEnum.MINI_PROGRAM.code, MiniProgramOperateEnum.ACTIVITY.code,
                getLogContent(headerName, miniProgramActivity.getName(), null, null, singleNumberConfigLogVO), userId, 0, LogResultEnum.LOG_SUCESS.code, null);
        return BaseAnswer.success(null);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @DS("save")
    public BaseAnswer<Void> deleteDataRank(String id, String userId) {
        MiniProgramActivityUserAward miniProgramActivityUserAward = miniProgramActivityUserAwardMapper.selectByPrimaryKey(id);
        if (miniProgramActivityUserAward == null) {
            logService.recordOperateLog(ModuleEnum.MINI_PROGRAM.code, MiniProgramOperateEnum.ACTIVITY.code,
                    "-", userId, 0, LogResultEnum.LOG_FAIL.code, StatusContant.MINI_PROGRAM_ACTIVITY_USER_AWARD_NOT_EXIST.getMessage());
            throw new BusinessException(StatusContant.MINI_PROGRAM_ACTIVITY_USER_AWARD_NOT_EXIST);
        }
        MiniProgramActivity miniProgramActivity = miniProgramActivityMapper.selectByPrimaryKey(miniProgramActivityUserAward.getActivityId());
        if (miniProgramActivity == null || miniProgramActivity.getIsDelete() == 1) {
            logService.recordOperateLog(ModuleEnum.MINI_PROGRAM.code, MiniProgramOperateEnum.ACTIVITY.code,
                    "-", userId, 0, LogResultEnum.LOG_FAIL.code, StatusContant.MINI_PROGRAM_ACTIVITY_NOT_EXIST.getMessage());
            throw new BusinessException(StatusContant.MINI_PROGRAM_ACTIVITY_NOT_EXIST);
        }
        //判断奖品是否已发放 物流单号已配置 积分处于兑换中或已兑换
        if (miniProgramActivityUserAward.getLogisticsCode() != null && !Objects.equals(miniProgramActivityUserAward.getLogisticsCode(), "")) {
            logService.recordOperateLog(ModuleEnum.MINI_PROGRAM.code, MiniProgramOperateEnum.ACTIVITY.code,
                    "-", userId, 0, LogResultEnum.LOG_FAIL.code, StatusContant.MINI_PROGRAM_USER_DELETE_WRONG.getMessage());
            throw new BusinessException(StatusContant.MINI_PROGRAM_USER_DELETE_WRONG);
        }

        PointExchangePartnerExample pointExchangePartnerExample = new PointExchangePartnerExample();
        pointExchangePartnerExample.createCriteria()
                .andPartnerIdEqualTo(miniProgramActivityUserAward.getUserId())
                .andActivityIdEqualTo(miniProgramActivityUserAward.getActivityId())
                .andChannelEqualTo(1)
                .andStatusIn(Arrays.asList(1, 2));
        List<PointExchangePartner> pointExchangePartners = pointExchangePartnerMapper.selectByExample(pointExchangePartnerExample);
        if (pointExchangePartners != null && pointExchangePartners.size() > 0) {
            logService.recordOperateLog(ModuleEnum.MINI_PROGRAM.code, MiniProgramOperateEnum.ACTIVITY.code,
                    "-", userId, 0, LogResultEnum.LOG_FAIL.code, StatusContant.MINI_PROGRAM_USER_DELETE_WRONG.getMessage());
            throw new BusinessException(StatusContant.MINI_PROGRAM_USER_DELETE_WRONG);
        }

        //判断积分是否发放，并扣除
        PointOperateExample pointOperateExample = new PointOperateExample();
        pointOperateExample.createCriteria()
                .andUserIdEqualTo(miniProgramActivityUserAward.getUserId())
                .andActivityIdEqualTo(miniProgramActivityUserAward.getActivityId())
                .andChannelEqualTo(1);
        List<PointOperate> pointOperates = pointOperateMapper.selectByExample(pointOperateExample);

        if (pointOperates.size() > 0) {
            MiniProgramActivityRankAward miniProgramActivityRankAward = miniProgramActivityRankAwardMapper.selectByPrimaryKey(miniProgramActivityUserAward.getAwardId());
            if (miniProgramActivityRankAward == null) {
                logService.recordOperateLog(ModuleEnum.MINI_PROGRAM.code, MiniProgramOperateEnum.ACTIVITY.code,
                        "-", userId, 0, LogResultEnum.LOG_FAIL.code, StatusContant.MINI_PROGRAM_ACTIVITY_AWARD_NOT_EXIST.getMessage());
                throw new BusinessException(StatusContant.MINI_PROGRAM_ACTIVITY_AWARD_NOT_EXIST);
            }
            //删除用户积分记录
            for (PointOperate pointOperate : pointOperates) {
                pointOperateMapper.deleteByPrimaryKey(pointOperate.getId());
            }
            //扣除用户积分
            PartnerPointExample partnerPointExample = new PartnerPointExample();
            PartnerPointExample.Criteria partnerCriteria = partnerPointExample.createCriteria();
            partnerCriteria.andSupplierIdEqualTo(miniProgramActivityRankAward.getSupplierId());
            partnerCriteria.andPartnerIdEqualTo(miniProgramActivityUserAward.getUserId());
            partnerCriteria.andChannelEqualTo(1);
            List<PartnerPoint> partnerPoints = partnerPointMapper.selectByExample(partnerPointExample);
            PartnerPoint partnerPoint = partnerPoints.get(0);
            partnerPoint.setAvailable(partnerPoint.getAvailable() - pointOperates.get(0).getPoint());
            partnerPoint.setTotal(partnerPoint.getTotal() - pointOperates.get(0).getPoint());
            partnerPointMapper.updateByPrimaryKeySelective(partnerPoint);
        }


        //删除用户中将记录
        miniProgramActivityUserAwardMapper.deleteByPrimaryKey(id);
        //删除活动用户
        List<MiniProgramActivityUser> miniProgramActivityUsers = miniProgramActivityUserMapper.selectByExample(new MiniProgramActivityUserExample().createCriteria().andUserIdEqualTo(miniProgramActivityUserAward.getUserId()).andActivityIdEqualTo(miniProgramActivityUserAward.getActivityId()).example());
        if (miniProgramActivityUsers != null && miniProgramActivityUsers.size() > 0) {
            miniProgramActivityUserMapper.deleteByPrimaryKey(miniProgramActivityUsers.get(0).getId());
        }
        SingleNumberConfigLogVO singleNumberConfigLogVO = new SingleNumberConfigLogVO();
        //记录日志
        // 获取用户信息

        List<UserMiniProgram> userMiniPrograms = userMiniProgramMapper.selectByExample(new UserMiniProgramExample().createCriteria().andUserIdEqualTo(miniProgramActivityUserAward.getUserId()).example());
        if (userMiniPrograms == null || userMiniPrograms.size() == 0) {
            logService.recordOperateLog(ModuleEnum.MINI_PROGRAM.code, MiniProgramOperateEnum.ACTIVITY.code,
                    "-", userId, 0, LogResultEnum.LOG_FAIL.code, StatusContant.MINI_PROGRAM_ACTIVITY_USER_NOT_EXIST.getMessage());
            throw new BusinessException(StatusContant.MINI_PROGRAM_ACTIVITY_USER_NOT_EXIST);
        }
        UserMiniProgram userMiniProgram = userMiniPrograms.get(0);
        singleNumberConfigLogVO.setName(userMiniProgram.getName());
        //获取奖品信息
        String headerName = "";
        if (miniProgramActivity.getActivityType() == 1) {
            headerName = "【活动排名-删除】";
            MiniProgramActivityRankAward miniProgramActivityRankAward = miniProgramActivityRankAwardMapper.selectByPrimaryKey(miniProgramActivityUserAward.getAwardId());
            if (miniProgramActivityRankAward != null) {
                singleNumberConfigLogVO.setAwardName(miniProgramActivityRankAward.getAwardName());
            }

        } else if (miniProgramActivity.getActivityType() == 2) {
            headerName = "【查看中奖名单-删除】";
            MiniProgramActivityWeeklyFunAward miniProgramActivityWeeklyFunAward = miniProgramActivityWeeklyFunAwardMapper.selectByPrimaryKey(miniProgramActivityUserAward.getAwardId());
            if (miniProgramActivityWeeklyFunAward != null) {
                singleNumberConfigLogVO.setAwardName(miniProgramActivityWeeklyFunAward.getAwardName());
            }

        } else {
            logService.recordOperateLog(ModuleEnum.MINI_PROGRAM.code, MiniProgramOperateEnum.ACTIVITY.code,
                    "-", userId, 0, LogResultEnum.LOG_FAIL.code, StatusContant.MINI_PROGRAM_ACTIVITY_TYPR_ERROR.getMessage());
            throw new BusinessException(StatusContant.MINI_PROGRAM_ACTIVITY_TYPR_ERROR);
        }

        logService.recordOperateLog(ModuleEnum.MINI_PROGRAM.code, MiniProgramOperateEnum.ACTIVITY.code,
                getLogContent(headerName, miniProgramActivity.getName(), null, null, singleNumberConfigLogVO), userId, 0, LogResultEnum.LOG_SUCESS.code, null);
        return BaseAnswer.success(null);
    }

    @Override
    @DS("query")
    public void exportDataRank(String activityId, String userId) {
        MiniProgramActivity miniProgramActivity = miniProgramActivityMapper.selectByPrimaryKey(activityId);
        if (miniProgramActivity == null || miniProgramActivity.getIsDelete() == 1) {
            throw new BusinessException(StatusContant.MINI_PROGRAM_ACTIVITY_NOT_EXIST);
        }
        List<ActivityDataRankDTO> list = miniProgramActivityMapperExt.getDataRankList(activityId, null);
        List<ActivityDataRankVO> vo = null;
        ServletRequestAttributes requestAttr = (ServletRequestAttributes) RequestContextHolder.currentRequestAttributes();
        HttpServletResponse response = requestAttr.getResponse();
        if (!CollectionUtils.isEmpty(list)) {
            vo = list.stream().map(item -> {
                ActivityDataRankVO activityDataRankVO = new ActivityDataRankVO();
                BeanUtils.copyProperties(item, activityDataRankVO);
                activityDataRankVO.setLocation(item.getProvinceName() + item.getCityName());
                String addr = item.getAddr1() == null ? "" : item.getAddr1() + item.getAddr2() == null ? "" : item.getAddr2()
                        + item.getAddr3() == null ? "" : item.getAddr3() + item.getUsaddr() == null ? "" : item.getUsaddr();

                activityDataRankVO.setDeliveryAddress(addr);

                return activityDataRankVO;
            }).collect(Collectors.toList());
        }
        try {
            String excelName = "中奖记录导出";
            // excelName = URLEncoder.encode(excelName, "UTF-8");

            ClassPathResource classPathResource;
            if (vo.get(0).getActivityType() == 1) {
                classPathResource = new ClassPathResource("template/activity_ranking_export_template.xlsx");
            } else if (vo.get(0).getActivityType() == 2) {
                classPathResource = new ClassPathResource("template/activity_weekly_export_template.xlsx");
            } else {


                throw new BusinessException(StatusContant.MINI_PROGRAM_ACTIVITY_TYPR_ERROR);
            }

            InputStream templateFileName = classPathResource.getInputStream();
            //构建填充excel参数
            Map<String, Object> map = new HashMap<String, Object>();
            EasyExcelUtils.exportExcel(response, "list", vo, map, excelName, templateFileName,
                    0, "中奖记录", BaseErrorConstant.SUCCESS.getStateCode(), BaseErrorConstant.SUCCESS.getMessage());
            String headerName = miniProgramActivity.getActivityType() == 1 ? "【排位赛-导出数据看板】" : "【即客周周乐-导出列表】";

            logService.recordOperateLog(ModuleEnum.MINI_PROGRAM.code, MiniProgramOperateEnum.ACTIVITY.code,
                    getLogContent(headerName, miniProgramActivity.getName(), miniProgramActivity.getStartTime(), miniProgramActivity.getStopTime(), null), userId, 0, LogResultEnum.LOG_SUCESS.code, null);

        } catch (Exception e) {
            logService.recordOperateLog(ModuleEnum.MINI_PROGRAM.code, MiniProgramOperateEnum.ACTIVITY.code,
                    "-", userId, 0, LogResultEnum.LOG_FAIL.code, e.getMessage());
            //便于前端拿到异常，将异常信息放入header
            try {
                if (e instanceof BusinessException) {
                    BusinessException businessException = (BusinessException) e;
                    response.addHeader("stateCode", businessException.getStatus().getStateCode());
                    response.addHeader("message", URLEncoder.encode(businessException.getStatus().getMessage(), "UTF-8"));
                } else {
                    log.error("导出活动中奖记录发生异常,", e);
                    response.addHeader("stateCode", BaseErrorConstant.INTERNAL_ERROR.getStateCode());
                    response.addHeader("message", URLEncoder.encode(BaseErrorConstant.INTERNAL_ERROR.getMessage(), "UTF-8"));
                }
            } catch (UnsupportedEncodingException ex) {
                ex.printStackTrace();
            }
        }
    }

    ;

    public String getLogContent(
            String headName,
            String activityName,
            Date startTime,
            Date endTime,
            SingleNumberConfigLogVO singleNumberConfigLogVO
    ) {
        List<String> logList = new ArrayList<>();
        logList.add(headName);
        logList.add("活动名称 " + activityName);
        if (startTime != null && endTime != null) {
            logList.add("活动时间 " + DateUtils.dateToStr(startTime, DateUtils.DEFAULT_DATETIME_FORMAT) + "至"
                    + DateUtils.dateToStr(endTime, DateUtils.DEFAULT_DATETIME_FORMAT)
            );
        }
        if (singleNumberConfigLogVO != null) {
            String str = "";
            if (singleNumberConfigLogVO.getName() != null) {
                str = str + singleNumberConfigLogVO.getName();
            }
            if (singleNumberConfigLogVO.getAwardName() != null) {
                str = str + "," + singleNumberConfigLogVO.getAwardName();
            }
            if (singleNumberConfigLogVO.getLogisticsCode() != null) {
                str = str + "," + singleNumberConfigLogVO.getLogisticsCode();
            }
            logList.add(str);
        }
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(logList)) {
            return String.join(System.getProperty("line.separator"), logList);
        }
        return null;
    }

    @Override
    @DS("query")
    public PageData<Order2CInfoDTO> getDataRankOrderInfo(ActivityDataRankParam param, Boolean isLog) {
        Integer pageNum = param.getPageNum();
        Integer pageSize = param.getPageSize();
        if (pageNum < 0 || pageSize < 0) {
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "页码和每页数量必须大于0");
        }

        PageData<Order2CInfoDTO> pageData = new PageData<>();
        pageData.setPage(pageNum);
        PageHelper.startPage(pageNum, pageSize);
        // 产品范式
        List<String> offeringClassList = new ArrayList<>();
        List<MiniProgramActivityOfferingClass> offeringClasses = miniProgramActivityOfferingClassMapper.selectByExample(
                new MiniProgramActivityOfferingClassExample().createCriteria().andActivityIdEqualTo(param.getId()).example()
        );
        if (!CollectionUtils.isEmpty(offeringClasses)) {
            offeringClassList = offeringClasses.stream().map(MiniProgramActivityOfferingClass::getSpuOfferingClass).collect(Collectors.toList());

        }
        //获取活动选定的spu列表
        List<MiniProgramActivitySpuCode> miniProgramActivitySpuCodes = miniProgramActivitySpuCodeMapper.selectByExample(new MiniProgramActivitySpuCodeExample().createCriteria().andActivityIdEqualTo(param.getId()).example());
        List<String> spuCodeList = new ArrayList<>();
        if (miniProgramActivitySpuCodes != null && miniProgramActivitySpuCodes.size() > 0) {
            for (MiniProgramActivitySpuCode miniProgramActivitySpuCode : miniProgramActivitySpuCodes) {
                if (!spuCodeList.contains(miniProgramActivitySpuCode.getSpuOfferingCode())) {
                    spuCodeList.add(miniProgramActivitySpuCode.getSpuOfferingCode());
                }

            }
        }
        String orderType = null;
        //获取活动订单类型
        List<MiniProgramActivityOrderType> miniProgramActivityOrderType = miniProgramActivityOrderTypeMapper.selectByExample(new MiniProgramActivityOrderTypeExample().createCriteria().andActivityIdEqualTo(param.getId()).example());
        if (miniProgramActivityOrderType == null || miniProgramActivityOrderType.size() == 0) {

            orderType = null;
        } else {
            orderType = miniProgramActivityOrderType.get(0).getOrderType();
        }
        // 客户类型
        List<MiniProgramActivityBusinessCode> businessCodes = miniProgramActivityBusinessCodeMapper.selectByExample(
                new MiniProgramActivityBusinessCodeExample().createCriteria().andActivityIdEqualTo(param.getId()).example()
        );
        List<String> businessCodeList = null;
        if (!CollectionUtils.isEmpty(businessCodes)) {
            businessCodeList = businessCodes.stream().map(MiniProgramActivityBusinessCode::getBusinessCode).collect(Collectors.toList());

        }
        MiniProgramActivity miniProgramActivity = miniProgramActivityMapper.selectByPrimaryKey(param.getId());
        if (miniProgramActivity == null || miniProgramActivity.getIsDelete() == 1) {
            if (isLog) {
                logService.recordOperateLog(ModuleEnum.MINI_PROGRAM.code, MiniProgramOperateEnum.ACTIVITY.code,
                        "-", param.getUserId(), 0, LogResultEnum.LOG_FAIL.code, StatusContant.MINI_PROGRAM_ACTIVITY_USER_NOT_EXIST.getMessage());
            }
            throw new BusinessException(StatusContant.MINI_PROGRAM_ACTIVITY_NOT_EXIST);
        }
        List<OrderInfoHandle> list = miniProgramActivityMapperExt.getDataRankOrderInfo(DateUtils.dateToStr(miniProgramActivity.getStartTime(), DateUtils.DATETIME_FORMAT_NO_SYMBOL),
                DateUtils.dateToStr(miniProgramActivity.getStopTime(), DateUtils.DATETIME_FORMAT_NO_SYMBOL), param.getId(), param.getUserId(), offeringClassList, spuCodeList, orderType, businessCodeList);

        List<Order2CInfoDTO> listVo = list.stream().map(x -> {
            Order2CInfoDTO order2CInfoDTO = new Order2CInfoDTO();
            BeanUtils.copyProperties(x, order2CInfoDTO);
            boolean isKaX = AtomOfferingClassEnum.X.name().equals(x.getAtomOfferingClass());
            String spuOfferingClassX = x.getSpuOfferingClass();
            order2CInfoDTO.setSpuOfferingClass(SPUOfferingClassEnum.getDisplay(spuOfferingClassX));
            order2CInfoDTO.setOrderStatusDescribe(OrderStatusInnerEnum.getDescribe(x.getOrderStatus()));
            if (AtomOfferingClassEnum.S.name().equals(x.getAtomOfferingClass())) {
                if (SPUOfferingClassEnum.A13.getSpuOfferingClass().equals(spuOfferingClassX)) {
                    order2CInfoDTO.setAtomOfferingClass("软件功能费");
                } else {
                    order2CInfoDTO.setAtomOfferingClass("软件");
                }

            } else if (AtomOfferingClassEnum.H.name().equals(x.getAtomOfferingClass())) {
                if (SPUOfferingClassEnum.A07.getSpuOfferingClass().equals(spuOfferingClassX)) {
                    order2CInfoDTO.setAtomOfferingClass("合同履约类硬件");
                    String addr1 = x.getAddr1();
                } else {
                    order2CInfoDTO.setAtomOfferingClass("代销类硬件");
                }
            } else if (AtomOfferingClassEnum.O.name().equals(x.getAtomOfferingClass())) {
//                order2CInfoDTO.setPartnerName(x.getSupplierName());
                order2CInfoDTO.setAtomOfferingClass(AtomOfferingClassEnum.O.getDescribe());
            } else if (AtomOfferingClassEnum.D.name().equals(x.getAtomOfferingClass())) {
//                order2CInfoDTO.setPartnerName(x.getSupplierName());
                order2CInfoDTO.setAtomOfferingClass(AtomOfferingClassEnum.D.getDescribe());
            } else if (AtomOfferingClassEnum.P.name().equals(x.getAtomOfferingClass())) {
//                order2CInfoDTO.setPartnerName(x.getSupplierName());
                order2CInfoDTO.setAtomOfferingClass(AtomOfferingClassEnum.P.getDescribe());
            } else if (AtomOfferingClassEnum.F.name().equals(x.getAtomOfferingClass())) {
//                order2CInfoDTO.setPartnerName(x.getSupplierName());
                order2CInfoDTO.setAtomOfferingClass(AtomOfferingClassEnum.F.getDescribe());
            } else if (AtomOfferingClassEnum.K.name().equals(x.getAtomOfferingClass())) {
//                order2CInfoDTO.setPartnerName(x.getSupplierName());
                order2CInfoDTO.setAtomOfferingClass(AtomOfferingClassEnum.K.getDescribe());
            } else if (AtomOfferingClassEnum.C.name().equals(x.getAtomOfferingClass())) {
                order2CInfoDTO.setAtomOfferingClass(AtomOfferingClassEnum.C.getDescribe());
            } else if (isKaX) {
                order2CInfoDTO.setAtomOfferingClass(AtomOfferingClassEnum.X.getDescribe());
            } else if (AtomOfferingClassEnum.A.name().equals(x.getAtomOfferingClass())) {
                order2CInfoDTO.setAtomOfferingClass(AtomOfferingClassEnum.A.getDescribe());
            }
            return order2CInfoDTO;
        }).collect(Collectors.toList());

        SingleNumberConfigLogVO singleNumberConfigLogVO = new SingleNumberConfigLogVO();
        //记录日志
        // 获取用户信息
        singleNumberConfigLogVO.setName(param.getName());

        //获取奖品名称
        List<MiniProgramActivityUserAward> miniProgramActivityUserAwards = miniProgramActivityUserAwardMapper.selectByExample(new MiniProgramActivityUserAwardExample().createCriteria()
                .andActivityIdEqualTo(param.getId())
                .andUserIdEqualTo(param.getUserId())
                .example());
        //获取奖品信息
        String headerName = miniProgramActivity.getActivityType() == 1 ? "【活动排名-订单明细】" : "【查看中奖名单-订单明细】";
        if (miniProgramActivityUserAwards != null && miniProgramActivityUserAwards.size() > 0) {
            MiniProgramActivityUserAward miniProgramActivityUserAward = miniProgramActivityUserAwards.get(0);
            if (miniProgramActivity.getActivityType() == 1) {
                MiniProgramActivityRankAward miniProgramActivityRankAward = miniProgramActivityRankAwardMapper.selectByPrimaryKey(miniProgramActivityUserAward.getAwardId());
                if (miniProgramActivityRankAward != null) {
                    singleNumberConfigLogVO.setAwardName(miniProgramActivityRankAward.getAwardName());
                }
            } else if (miniProgramActivity.getActivityType() == 2) {
                MiniProgramActivityWeeklyFunAward miniProgramActivityWeeklyFunAward = miniProgramActivityWeeklyFunAwardMapper.selectByPrimaryKey(miniProgramActivityUserAward.getAwardId());
                if (miniProgramActivityWeeklyFunAward != null) {
                    singleNumberConfigLogVO.setAwardName(miniProgramActivityWeeklyFunAward.getAwardName());
                }

            } else {
                if (isLog) {
                    logService.recordOperateLog(ModuleEnum.MINI_PROGRAM.code, MiniProgramOperateEnum.ACTIVITY.code,
                            "-", param.getUserId(), 0, LogResultEnum.LOG_FAIL.code, StatusContant.MINI_PROGRAM_ACTIVITY_TYPR_ERROR.getMessage());
                }
                throw new BusinessException(StatusContant.MINI_PROGRAM_ACTIVITY_TYPR_ERROR);
            }
        }

        if (isLog) {
            logService.recordOperateLog(ModuleEnum.MINI_PROGRAM.code, MiniProgramOperateEnum.ACTIVITY.code,
                    getLogContent(headerName, miniProgramActivity.getName(), null, null, singleNumberConfigLogVO), param.getUserId(), 0, LogResultEnum.LOG_FAIL.code, StatusContant.MINI_PROGRAM_ACTIVITY_USER_NOT_EXIST.getMessage());
        }
        PageInfo<Order2CInfoDTO> pageInfo = new PageInfo<>(listVo);
        pageData.setCount(pageInfo.getTotal());
        pageData.setData(listVo);
        return pageData;

    }

    @Override
    @DS("query")
    public void exportDataRankOrderInfo(String activityId, String userId) {
        ServletRequestAttributes requestAttr = (ServletRequestAttributes) RequestContextHolder.currentRequestAttributes();
        HttpServletResponse response = requestAttr.getResponse();
        // 产品范式
        List<String> offeringClassList = new ArrayList<>();
        List<MiniProgramActivityOfferingClass> offeringClasses = miniProgramActivityOfferingClassMapper.selectByExample(
                new MiniProgramActivityOfferingClassExample().createCriteria().andActivityIdEqualTo(activityId).example()
        );
        if (!CollectionUtils.isEmpty(offeringClasses)) {
            offeringClassList = offeringClasses.stream().map(MiniProgramActivityOfferingClass::getSpuOfferingClass).collect(Collectors.toList());

        }
        //获取活动选定的spu列表
        List<MiniProgramActivitySpuCode> miniProgramActivitySpuCodes = miniProgramActivitySpuCodeMapper.selectByExample(new MiniProgramActivitySpuCodeExample().createCriteria().andActivityIdEqualTo(activityId).example());
        List<String> spuCodeList = new ArrayList<>();
        if (miniProgramActivitySpuCodes != null && miniProgramActivitySpuCodes.size() > 0) {
            for (MiniProgramActivitySpuCode miniProgramActivitySpuCode : miniProgramActivitySpuCodes) {
                if (!spuCodeList.contains(miniProgramActivitySpuCode.getSpuOfferingCode())) {
                    spuCodeList.add(miniProgramActivitySpuCode.getSpuOfferingCode());
                }

            }
        }
        String orderType = null;
        //获取活动订单类型
        List<MiniProgramActivityOrderType> miniProgramActivityOrderType = miniProgramActivityOrderTypeMapper.selectByExample(new MiniProgramActivityOrderTypeExample().createCriteria().andActivityIdEqualTo(activityId).example());
        if (miniProgramActivityOrderType == null || miniProgramActivityOrderType.size() == 0) {

            orderType = null;
        } else {
            orderType = miniProgramActivityOrderType.get(0).getOrderType();
        }
        // 客户类型
        List<MiniProgramActivityBusinessCode> businessCodes = miniProgramActivityBusinessCodeMapper.selectByExample(
                new MiniProgramActivityBusinessCodeExample().createCriteria().andActivityIdEqualTo(activityId).example()
        );
        List<String> businessCodeList = null;
        if (!CollectionUtils.isEmpty(businessCodes)) {
            businessCodeList = businessCodes.stream().map(MiniProgramActivityBusinessCode::getBusinessCode).collect(Collectors.toList());

        }
        MiniProgramActivity miniProgramActivity = miniProgramActivityMapper.selectByPrimaryKey(activityId);
        if (miniProgramActivity == null || miniProgramActivity.getIsDelete() == 1) {
            throw new BusinessException(StatusContant.MINI_PROGRAM_ACTIVITY_NOT_EXIST);
        }
        List<OrderInfoHandle> list = miniProgramActivityMapperExt.getDataRankOrderInfo(DateUtils.dateToStr(miniProgramActivity.getStartTime(), DateUtils.DATETIME_FORMAT_NO_SYMBOL),
                DateUtils.dateToStr(miniProgramActivity.getStopTime(), DateUtils.DATETIME_FORMAT_NO_SYMBOL), activityId, userId, offeringClassList, spuCodeList, orderType, businessCodeList);

        List<Order2CInfoDTO> listVo = list.stream().map(x -> {
            Order2CInfoDTO order2CInfoDTO = new Order2CInfoDTO();
            BeanUtils.copyProperties(x, order2CInfoDTO);
            boolean isKaX = AtomOfferingClassEnum.X.name().equals(x.getAtomOfferingClass());
            String spuOfferingClassX = x.getSpuOfferingClass();
            order2CInfoDTO.setSpuOfferingClass(SPUOfferingClassEnum.getDisplay(spuOfferingClassX));
            order2CInfoDTO.setOrderStatusDescribe(OrderStatusInnerEnum.getDescribe(x.getOrderStatus()));
            if (AtomOfferingClassEnum.S.name().equals(x.getAtomOfferingClass())) {
                if (SPUOfferingClassEnum.A13.getSpuOfferingClass().equals(spuOfferingClassX)) {
                    order2CInfoDTO.setAtomOfferingClass("软件功能费");
                } else {
                    order2CInfoDTO.setAtomOfferingClass("软件");
                }

            } else if (AtomOfferingClassEnum.H.name().equals(x.getAtomOfferingClass())) {
                if (SPUOfferingClassEnum.A07.getSpuOfferingClass().equals(spuOfferingClassX)) {
                    order2CInfoDTO.setAtomOfferingClass("合同履约类硬件");
                    String addr1 = x.getAddr1();
                } else {
                    order2CInfoDTO.setAtomOfferingClass("代销类硬件");
                }
            } else if (AtomOfferingClassEnum.O.name().equals(x.getAtomOfferingClass())) {
//                order2CInfoDTO.setPartnerName(x.getSupplierName());
                order2CInfoDTO.setAtomOfferingClass(AtomOfferingClassEnum.O.getDescribe());
            } else if (AtomOfferingClassEnum.D.name().equals(x.getAtomOfferingClass())) {
//                order2CInfoDTO.setPartnerName(x.getSupplierName());
                order2CInfoDTO.setAtomOfferingClass(AtomOfferingClassEnum.D.getDescribe());
            } else if (AtomOfferingClassEnum.P.name().equals(x.getAtomOfferingClass())) {
//                order2CInfoDTO.setPartnerName(x.getSupplierName());
                order2CInfoDTO.setAtomOfferingClass(AtomOfferingClassEnum.P.getDescribe());
            } else if (AtomOfferingClassEnum.F.name().equals(x.getAtomOfferingClass())) {
//                order2CInfoDTO.setPartnerName(x.getSupplierName());
                order2CInfoDTO.setAtomOfferingClass(AtomOfferingClassEnum.F.getDescribe());
            } else if (AtomOfferingClassEnum.K.name().equals(x.getAtomOfferingClass())) {
//                order2CInfoDTO.setPartnerName(x.getSupplierName());
                order2CInfoDTO.setAtomOfferingClass(AtomOfferingClassEnum.K.getDescribe());
            } else if (AtomOfferingClassEnum.C.name().equals(x.getAtomOfferingClass())) {
                order2CInfoDTO.setAtomOfferingClass(AtomOfferingClassEnum.C.getDescribe());
            } else if (isKaX) {
                order2CInfoDTO.setAtomOfferingClass(AtomOfferingClassEnum.X.getDescribe());
            } else if (AtomOfferingClassEnum.A.name().equals(x.getAtomOfferingClass())) {
                order2CInfoDTO.setAtomOfferingClass(AtomOfferingClassEnum.A.getDescribe());
            }

            order2CInfoDTO.setAtomPriceYuan(new BigDecimal(x.getAtomPrice()).divide(new BigDecimal(1000)).setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue());

            order2CInfoDTO.setAtomOrderPrice(order2CInfoDTO.getAtomPriceYuan() * order2CInfoDTO.getQuantity());
            if (x.getOrderType().equals("01")) {
                order2CInfoDTO.setOrderTypeDescribe("自主下单");
            } else if (x.getOrderType().equals("00")) {
                order2CInfoDTO.setOrderTypeDescribe("代客下单");
            } else if (x.getOrderType().equals("02")) {
                order2CInfoDTO.setOrderTypeDescribe("代客下单");
            } else {
                order2CInfoDTO.setOrderTypeDescribe("-");
            }

            order2CInfoDTO.setTotalPriceYuan(new BigDecimal(x.getTotalPrice()).divide(new BigDecimal(1000)).setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue());

            return order2CInfoDTO;
        }).collect(Collectors.toList());
        //导出
        try {
            String excelName = "订单明细导出";
            // excelName = URLEncoder.encode(excelName, "UTF-8");

            ClassPathResource classPathResource = new ClassPathResource("template/activity_ranking_export_order_template.xlsx");


            InputStream templateFileName = classPathResource.getInputStream();
            //构建填充excel参数
            Map<String, Object> map = new HashMap<String, Object>();
            EasyExcelUtils.exportExcel(response, "list", listVo, map, excelName, templateFileName,
                    0, "订单明细导出", BaseErrorConstant.SUCCESS.getStateCode(), BaseErrorConstant.SUCCESS.getMessage());


        } catch (Exception e) {
            //便于前端拿到异常，将异常信息放入header
            try {
                if (e instanceof BusinessException) {
                    BusinessException businessException = (BusinessException) e;
                    response.addHeader("stateCode", businessException.getStatus().getStateCode());
                    response.addHeader("message", URLEncoder.encode(businessException.getStatus().getMessage(), "UTF-8"));
                } else {
                    log.error("导出活动中奖记录发生异常,", e);
                    response.addHeader("stateCode", BaseErrorConstant.INTERNAL_ERROR.getStateCode());
                    response.addHeader("message", URLEncoder.encode(BaseErrorConstant.INTERNAL_ERROR.getMessage(), "UTF-8"));
                }
            } catch (UnsupportedEncodingException ex) {
                ex.printStackTrace();
            }
        }

    }


    public String draw(List<MiniProgramActivityWeeklyFunAward> prizes) {
        if (prizes != null) {
            ActivityWeeklyFunAwardCountParam param = new ActivityWeeklyFunAwardCountParam();
            param.setAwardIds(prizes.stream().map(MiniProgramActivityWeeklyFunAward::getId).collect(Collectors.toList()));
            param.setBegin(getStartOfDay());
            param.setEnd(getEndOfDay());
            List<ActivityWeeklyFunAwardCountDTO> countDTOS = miniProgramActivityMapperExt.getActivityWeeklyFunAwardCount(param);

            ActivityWeeklyFunAwardCountParam paramTotal = new ActivityWeeklyFunAwardCountParam();
            paramTotal.setAwardIds(prizes.stream().map(MiniProgramActivityWeeklyFunAward::getId).collect(Collectors.toList()));
            List<ActivityWeeklyFunAwardCountDTO> total = miniProgramActivityMapperExt.getActivityWeeklyFunAwardCount(paramTotal);
            Map<String, ActivityWeeklyFunAwardCountDTO> countMap = new LinkedHashMap<>();
            countDTOS.forEach(x -> countMap.put(x.getAwardId(), x));
            total.forEach(x -> {
                ActivityWeeklyFunAwardCountDTO dto = countMap.get(x.getAwardId());
                if (dto != null) {
                    dto.setTotal(x.getToday());
                }
            });

            // 所有奖品的中奖概率
            int totalProbability = prizes.stream().mapToInt(MiniProgramActivityWeeklyFunAward::getProbability).sum();
            // 不中奖的概率
            int delta = Math.max(10000 - totalProbability, 0);
            prizes = prizes.stream()
                    // 过滤掉已达到上限的奖品
                    .filter(p -> !isAwardLimitReached(p, countMap))
                    // 将奖品按抽中概率从小到大排序
                    .sorted(Comparator.comparingInt(MiniProgramActivityWeeklyFunAward::getProbability))
                    .collect(Collectors.toList());
            // 重新计算过滤后的所有奖品中奖概率
            totalProbability = prizes.stream().mapToInt(MiniProgramActivityWeeklyFunAward::getProbability).sum();
            int randomNum = new Random().nextInt(totalProbability + delta);
            int currentProbability = 0;
            for (MiniProgramActivityWeeklyFunAward prize : prizes) {
                currentProbability += prize.getProbability();
                if (randomNum <= currentProbability) {
                    return prize.getId();
                }
            }
        }
        return "谢谢惠顾";
    }

    /**
     * 判断奖品是否达到上限
     */
    private boolean isAwardLimitReached(MiniProgramActivityWeeklyFunAward miniProgramActivityWeeklyFunAward,
                                        Map<String, ActivityWeeklyFunAwardCountDTO> countMap) {
        // 判断当天抽奖次数是否达到上限
        Integer daily = miniProgramActivityWeeklyFunAward.getMaxAwardsDaily();
        daily = daily != null ? daily : 0;
        ActivityWeeklyFunAwardCountDTO dto = countMap.get(miniProgramActivityWeeklyFunAward.getId());
        if (dto == null) {
            return false;
        }
        long today = dto.getToday() != null ? dto.getToday() : 0;
        if (today >= daily) {
            return true;
        }

        // 判断奖品是否抽完
        Integer max = miniProgramActivityWeeklyFunAward.getMaxAwards();
        max = max != null ? max : 0;
        long total = dto.getTotal() != null ? dto.getTotal() : 0;
        return total >= max;
    }

    private Date getStartOfDay() {
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTime();
    }

    private Date getEndOfDay() {
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        calendar.set(Calendar.MILLISECOND, 999);
        return calendar.getTime();
    }

    /**
     * 保存活动基本信息
     */
    private MiniProgramActivity saveActivityBaseInfo(AbsPublishActivityParam param, String userId) {
        boolean newActivity = ObjectUtils.isEmpty(param.getId());
        MiniProgramActivity miniProgramActivity;
        if (newActivity) {
            miniProgramActivity = new MiniProgramActivity();
            miniProgramActivity.setId(BaseServiceUtils.getId());
            miniProgramActivity.setActivityType(param.getActivityType());
            miniProgramActivity.setActive(true);
            miniProgramActivity.setCreateTime(new Date());
            miniProgramActivity.setUpdateTime(new Date());
        } else {
            miniProgramActivity = miniProgramActivityMapper.selectByPrimaryKey(param.getId());
            if (
                    ActivityAuditStatusEnum.PASSED.getStatus().equals(miniProgramActivity.getAuditStatus()) ||
                            ActivityAuditStatusEnum.IN_PROGRESS.getStatus().equals(miniProgramActivity.getAuditStatus()) ||
                            ActivityStatusEnum.OFFLINE.getStatus().equals(miniProgramActivity.getStatus()) ||
                            miniProgramActivity.getIsDelete().equals(1)
            ) {
                throw new BusinessException(StatusContant.MINI_PROGRAM_ACTIVITY_CANNOT_EDIT);
            }
            miniProgramActivity.setUpdateTime(new Date());
        }
        miniProgramActivity.setName(param.getName());
        // 设置活动对象，使用二进制按位或的方式组合
        List<Integer> targetList = param.getTargetList();
        int target = 0;
        for (Integer t : targetList) {
            target |= t;
        }
        miniProgramActivity.setTarget(target);
        miniProgramActivity.setStartTime(param.getStartTime());
        miniProgramActivity.setStopTime(param.getStopTime());
        miniProgramActivity.setConfirmTime(param.getConfirmTime());
        miniProgramActivity.setSettlementTime(param.getSettlementTime());
        miniProgramActivity.setCreateUid(userId);
        miniProgramActivity.setIsDelete(0);
        miniProgramActivity.setShareImg(param.getShareImg());
        miniProgramActivity.setListImg(param.getListImg());
        miniProgramActivity.setStatus(Boolean.TRUE.equals(param.getDraft()) ? ActivityStatusEnum.DRAFT.getStatus() : ActivityStatusEnum.WAITING_FOR_AUDITING.getStatus());
        miniProgramActivity.setAuditStatus(ActivityAuditStatusEnum.DRAFT.getStatus());
        // 保存基本信息
        if (newActivity) {
            miniProgramActivityMapper.insertSelective(miniProgramActivity);
        } else {
            miniProgramActivityMapper.updateByPrimaryKeySelective(miniProgramActivity);
        }
        return miniProgramActivity;
    }

    /**
     * 保存排位赛活动信息
     *
     * @param param
     * @param activityId
     */
    private MiniProgramActivityRank saveActivityRank(PublishRankActivityParam param, String activityId) {
        miniProgramActivityRankMapper.deleteByExample(
                new MiniProgramActivityRankExample().createCriteria()
                        .andActivityIdEqualTo(activityId).example()
        );
        MiniProgramActivityRank miniProgramActivityRank = new MiniProgramActivityRank();
        miniProgramActivityRank.setId(BaseServiceUtils.getId());
        miniProgramActivityRank.setActivityId(activityId);
        miniProgramActivityRank.setDetailImg(param.getDetailImg());
        miniProgramActivityRank.setDescription(param.getDescription());
        miniProgramActivityRank.setParticipation(param.getParticipation());
        miniProgramActivityRank.setAward(param.getAward());
        miniProgramActivityRank.setRule(param.getRule());
        miniProgramActivityRank.setSortType(param.getSortType());
        miniProgramActivityRank.setAdditional(param.getAdditional());
        miniProgramActivityRank.setJoinDefault(param.getJoinDefault() != null ? param.getJoinDefault() : true);
        miniProgramActivityRankMapper.insertSelective(miniProgramActivityRank);
        return miniProgramActivityRank;
    }

    /**
     * 保存周周乐活动信息
     *
     * @param param
     * @param activityId
     */
    private MiniProgramActivityWeeklyFun saveActivityWeeklyFun(PublishWeeklyActivityParam param, String activityId) {
        miniProgramActivityWeeklyFunMapper.deleteByExample(
                new MiniProgramActivityWeeklyFunExample().createCriteria()
                        .andActivityIdEqualTo(activityId).example()
        );
        MiniProgramActivityWeeklyFun miniProgramActivityWeeklyFun = new MiniProgramActivityWeeklyFun();
        miniProgramActivityWeeklyFun.setId(BaseServiceUtils.getId());
        miniProgramActivityWeeklyFun.setActivityId(activityId);
        miniProgramActivityWeeklyFun.setDescription(param.getDescription());
        miniProgramActivityWeeklyFun.setRule(param.getRule());
        miniProgramActivityWeeklyFun.setMaxPlayer(param.getMaxPlayer());
        miniProgramActivityWeeklyFun.setOrderCount(param.getOrderCount());
        miniProgramActivityWeeklyFun.setOrderStart(param.getOrderStart());
        miniProgramActivityWeeklyFun.setOrderStop(param.getOrderStop());
        miniProgramActivityWeeklyFun.setRegisterStart(param.getRegisterStart());
        miniProgramActivityWeeklyFun.setRegisterStop(param.getRegisterStop());
        miniProgramActivityWeeklyFun.setWheelParts(param.getWheelParts());
        miniProgramActivityWeeklyFun.setSlogan(param.getSlogan());
        miniProgramActivityWeeklyFunMapper.insertSelective(miniProgramActivityWeeklyFun);
        return miniProgramActivityWeeklyFun;
    }

    /**
     * 保存活动省份
     *
     * @param param
     * @param activityId
     */
    private void saveActivityRegions(AbsPublishActivityParam param, String activityId) {
        List<MiniProgramActivityRegion> oldRegion = miniProgramActivityRegionMapper.selectByExample(
                new MiniProgramActivityRegionExample().createCriteria()
                        .andActivityIdEqualTo(activityId).example());
        miniProgramActivityRegionMapper.deleteByExample(
                new MiniProgramActivityRegionExample().createCriteria()
                        .andActivityIdEqualTo(activityId).example()
        );
        List<MiniProgramActivityRegion> regions = new ArrayList<>();
        for (RegionParam regionParam : param.getRegions()) {
            MiniProgramActivityRegion region = new MiniProgramActivityRegion();
            region.setId(BaseServiceUtils.getId());
            region.setActivityId(activityId);
            region.setProvinceCode(regionParam.getProvinceCode());
            region.setCityCode(regionParam.getCityCode());
            regions.add(region);
        }
        miniProgramActivityRegionMapper.batchInsert(regions);
//        TransactionUtil.afterCommit(() -> syncUpdateRegionActivity2Redis(activityId, regions, oldRegion));
    }

    /**
     * 保存产品范式
     *
     * @param param
     * @param activityId
     */
    private void saveActivityOfferingClasses(AbsPublishActivityParam param, String activityId) {
        miniProgramActivityOfferingClassMapper.deleteByExample(
                new MiniProgramActivityOfferingClassExample().createCriteria()
                        .andActivityIdEqualTo(activityId).example()
        );
        if (!CollectionUtils.isEmpty(param.getOfferingClasses())) {
            List<MiniProgramActivityOfferingClass> offeringClasses = new ArrayList<>();
            for (String name : param.getOfferingClasses()) {
                MiniProgramActivityOfferingClass offeringClass = new MiniProgramActivityOfferingClass();
                offeringClass.setId(BaseServiceUtils.getId());
                offeringClass.setActivityId(activityId);
                offeringClass.setSpuOfferingClass(name);
                offeringClasses.add(offeringClass);
            }
            miniProgramActivityOfferingClassMapper.batchInsert(offeringClasses);
        }
    }

    /**
     * 保存SPU编码
     *
     * @param param
     * @param activityId
     */
    private void saveActivitySpuCodes(AbsPublishActivityParam param, String activityId) {
        miniProgramActivitySpuCodeMapper.deleteByExample(
                new MiniProgramActivitySpuCodeExample().createCriteria()
                        .andActivityIdEqualTo(activityId).example()
        );
        if (!CollectionUtils.isEmpty(param.getSpuCodes())) {
            List<MiniProgramActivitySpuCode> spuCodes = new ArrayList<>();
            for (String spuCode : param.getSpuCodes()) {
                MiniProgramActivitySpuCode miniProgramActivitySpuCode = new MiniProgramActivitySpuCode();
                miniProgramActivitySpuCode.setId(BaseServiceUtils.getId());
                miniProgramActivitySpuCode.setActivityId(activityId);
                miniProgramActivitySpuCode.setSpuOfferingCode(spuCode);
                spuCodes.add(miniProgramActivitySpuCode);
            }
            miniProgramActivitySpuCodeMapper.batchInsert(spuCodes);
        }
    }

    /**
     * 保存订单类型
     *
     * @param param
     * @param activityId
     */
    private void saveActivityOrderTypes(PublishRankActivityParam param, String activityId) {
        miniProgramActivityOrderTypeMapper.deleteByExample(
                new MiniProgramActivityOrderTypeExample().createCriteria()
                        .andActivityIdEqualTo(activityId).example()
        );
        if (!CollectionUtils.isEmpty(param.getOrderTypes())) {
            List<MiniProgramActivityOrderType> orderTypes = new ArrayList<>();
            for (String type : param.getOrderTypes()) {
                MiniProgramActivityOrderType orderType = new MiniProgramActivityOrderType();
                orderType.setId(BaseServiceUtils.getId());
                orderType.setActivityId(activityId);
                orderType.setOrderType(type);
                orderTypes.add(orderType);
            }
            miniProgramActivityOrderTypeMapper.batchInsert(orderTypes);
        }
    }

    /**
     * 保存客户类型
     *
     * @param param
     * @param activityId
     */
    private void saveActivityBusinessCodes(PublishRankActivityParam param, String activityId) {
        miniProgramActivityBusinessCodeMapper.deleteByExample(
                new MiniProgramActivityBusinessCodeExample().createCriteria()
                        .andActivityIdEqualTo(activityId).example()
        );
        if (!CollectionUtils.isEmpty(param.getBusinessCodes())) {
            List<MiniProgramActivityBusinessCode> businessCodes = new ArrayList<>();
            for (String code : param.getBusinessCodes()) {
                MiniProgramActivityBusinessCode businessCode = new MiniProgramActivityBusinessCode();
                businessCode.setId(BaseServiceUtils.getId());
                businessCode.setActivityId(activityId);
                businessCode.setBusinessCode(code);
                businessCodes.add(businessCode);
            }
            miniProgramActivityBusinessCodeMapper.batchInsert(businessCodes);
        }
    }

    /**
     * 保存周周乐活动参与人员
     *
     * @param param
     * @param activityId
     */
    private void saveWeeklyActivityUsers(PublishWeeklyActivityParam param, String activityId) {
        miniProgramActivityUserMapper.deleteByExample(
                new MiniProgramActivityUserExample().createCriteria()
                        .andActivityIdEqualTo(activityId).example()
        );
        if (!CollectionUtils.isEmpty(param.getActivityUsers())) {
            List<MiniProgramActivityUser> activityUsers = new ArrayList<>();
            for (ActivityUserParam userParam : param.getActivityUsers()) {
                MiniProgramActivityUser activityUser = new MiniProgramActivityUser();
                activityUser.setId(BaseServiceUtils.getId());
                activityUser.setActivityId(activityId);
                activityUser.setUserId(userParam.getUserId());
                activityUser.setIsAdd(1);
                activityUser.setIsDraw(0);
                activityUsers.add(activityUser);
            }
            miniProgramActivityUserMapper.batchInsert(activityUsers);
        }
    }

    /**
     * 保存排位赛奖品
     *
     * @param param
     * @param activityId
     */
    private void saveRankActivityAwards(PublishRankActivityParam param, String activityId) {
        List<RankActivityAwardsParam> awards = param.getAwards();
        if (CollectionUtils.isEmpty(awards)) {
            return;
        }
        List<MiniProgramActivityRankAward> miniProgramActivityRankAwards = new ArrayList<>();
        for (RankActivityAwardsParam award : awards) {
            MiniProgramActivityRankAward miniProgramActivityRankAward = new MiniProgramActivityRankAward();
            BeanUtils.copyProperties(award, miniProgramActivityRankAward);
            miniProgramActivityRankAward.setId(BaseServiceUtils.getId());
            miniProgramActivityRankAward.setActivityId(activityId);
            miniProgramActivityRankAward.setCreateTime(new Date());
            miniProgramActivityRankAward.setUpdateTime(new Date());
            miniProgramActivityRankAwards.add(miniProgramActivityRankAward);
        }
        miniProgramActivityRankAwardMapper.deleteByExample(
                new MiniProgramActivityRankAwardExample().createCriteria().andActivityIdEqualTo(activityId).example()
        );
        miniProgramActivityRankAwardMapper.batchInsert(miniProgramActivityRankAwards);
    }

    /**
     * 保存周周乐活动奖品
     *
     * @param param
     * @param activityId
     */
    private void saveWeeklyActivityAwards(PublishWeeklyActivityParam param, String activityId) {
        List<WeeklyActivityAwardsParam> awards = param.getAwards();
        if (CollectionUtils.isEmpty(awards)) {
            return;
        }
        List<MiniProgramActivityWeeklyFunAward> miniProgramActivityWeeklyFunAwards = new ArrayList<>();
        for (WeeklyActivityAwardsParam award : awards) {
            MiniProgramActivityWeeklyFunAward miniProgramActivityWeeklyFunAward = new MiniProgramActivityWeeklyFunAward();
            BeanUtils.copyProperties(award, miniProgramActivityWeeklyFunAward);
            miniProgramActivityWeeklyFunAward.setId(BaseServiceUtils.getId());
            miniProgramActivityWeeklyFunAward.setActivityId(activityId);
            miniProgramActivityWeeklyFunAward.setCreateTime(new Date());
            miniProgramActivityWeeklyFunAward.setUpdateTime(new Date());
            miniProgramActivityWeeklyFunAwards.add(miniProgramActivityWeeklyFunAward);
        }
        miniProgramActivityWeeklyFunAwardMapper.deleteByExample(
                new MiniProgramActivityWeeklyFunAwardExample().createCriteria().andActivityIdEqualTo(activityId).example()
        );
        miniProgramActivityWeeklyFunAwardMapper.batchInsert(miniProgramActivityWeeklyFunAwards);
    }

    /**
     * 保存活动开始状态的定时任务
     *
     * @param activity
     */
    private void saveActivityStartJob(MiniProgramActivity activity) {
        GeneralJobData<MiniProgramActivity> jobData = new GeneralJobData<>();
        jobData.setName("JobName_" + activity.getId());
        jobData.setGroup("JobGroup_" + QUARTZ_ACTIVITY_START);
        jobData.setJob(MiniProgramActivityStartJob.class);
        jobData.setData(activity);
        jobData.setCronExpression(formatCronExpression(activity.getStartTime()));
        jobManager.saveQuartzJob(jobData);
        log.info("保存活动开始的定时任务，jobData：{}", JSON.toJSONString(jobData));
    }

    /**
     * 保存活动结算状态的定时任务
     *
     * @param activity
     */
    private void saveActivitySettlementJob(MiniProgramActivity activity) {
        GeneralJobData<MiniProgramActivity> jobData = new GeneralJobData<>();
        jobData.setName("JobName_" + activity.getId());
        jobData.setGroup("JobGroup_" + QUARTZ_ACTIVITY_SETTLEMENT);
        jobData.setJob(MiniProgramActivitySettlementJob.class);
        jobData.setData(activity);
        jobData.setCronExpression(formatCronExpression(activity.getConfirmTime()));
        jobManager.saveQuartzJob(jobData);
        log.info("保存活动结算完成的定时任务，jobData：{}", JSON.toJSONString(jobData));
    }


    /**
     * 保存用户奖品排名的定时任务
     *
     * @param activity
     */
    private void saveActivityConfirmJob(MiniProgramActivity activity) {
        if (null == activity.getConfirmTime()) {
            throw new BusinessException("-1", "奖品确认时间不能为空");
        }
        GeneralJobData<MiniProgramActivity> jobData = new GeneralJobData<>();
        jobData.setName("JobName_" + activity.getId());
        jobData.setGroup("JobGroup_" + QUARTZ_ACTIVITY_CONFIRM);
        jobData.setJob(MiniProgramActivityConfirmJob.class);
        jobData.setData(activity);
        jobData.setCronExpression(formatCronExpression(activity.getStopTime()));
        jobManager.saveQuartzJob(jobData);
        log.info("保存用户奖品确认的定时任务，jobData：{}", JSON.toJSONString(jobData));
    }

    /**
     * 保存活动结束状态的定时任务
     *
     * @param activity
     */
    private void saveActivityFinishJob(MiniProgramActivity activity) {
        GeneralJobData<MiniProgramActivity> jobData = new GeneralJobData<>();
        jobData.setName("JobName_" + activity.getId());
        jobData.setGroup("JobGroup_" + QUARTZ_ACTIVITY_FINISH);
        jobData.setJob(MiniProgramActivityFinishJob.class);
        jobData.setData(activity);
        jobData.setCronExpression(formatCronExpression(activity.getSettlementTime()));
        jobManager.saveQuartzJob(jobData);
        log.info("保存活动结束开始结算的定时任务，jobData：{}", JSON.toJSONString(jobData));
    }

    /**
     * 格式化cron表达式。定时任务在指定时间开始，每5分钟执行一次。
     *
     * @param date
     * @return
     */
    private String formatCronExpression(Date date) {
        String dateFormat = "s m/5 H d M ? yyyy";
        SimpleDateFormat sdf = new SimpleDateFormat(dateFormat);
        return sdf.format(date);
    }

    /**
     * 保存参加排位赛的用户
     *
     * @param activity
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    @DS("save")
    public void saveRankActivityUsers(MiniProgramActivity activity) {
        if (activity.getActivityType() != 1) {
            return;
        }
        long begin = System.currentTimeMillis();
        // 判断是否默认参加
        List<MiniProgramActivityRank> activityRanks = miniProgramActivityRankMapper.selectByExample(
                new MiniProgramActivityRankExample().createCriteria()
                        .andActivityIdEqualTo(activity.getId())
                        .example()
        );
        if (!CollectionUtils.isEmpty(activityRanks)) {
            MiniProgramActivityRank activityRank = activityRanks.get(0);
            Boolean joinDefault = activityRank.getJoinDefault();
            joinDefault = joinDefault != null ? joinDefault : false;
            log.info("默认" + (joinDefault ? "参加" : "不参加") + "，排位赛活动id：{}，名称：{}", activity.getId(), activity.getName());
            if (!joinDefault) {
                // 目前默认参加标志没有启用，不管是否选默认参加，都是全部参加
                return;
            }
        } else {
            log.info("排位赛不存在，活动id：{}，名称：{}", activity.getId(), activity.getName());
            return;
        }
        log.info("保存参加排位赛的用户");
        // 根据活动对象和活动身份筛选用户
        List<String> roleTypes = new ArrayList<>();
        Integer target = activity.getTarget();
        if ((target & TARGET_MASK_MANAGER) != 0) {
            roleTypes.add("4");
        }
        if ((target & TARGET_MASK_DISTRIBUTOR) != 0) {
            roleTypes.add("1");
            roleTypes.add("2");
        }
        if ((target & TARGET_MASK_CHANNEL) != 0) {
            roleTypes.add("3");
        }
        List<MiniProgramActivityRegion> regions = miniProgramActivityRegionMapper.selectByExample(
                new MiniProgramActivityRegionExample().createCriteria()
                        .andActivityIdEqualTo(activity.getId()).example()
        );
        List<RegionParam> regionParams = null;
        if (!CollectionUtils.isEmpty(regions)) {
            regionParams = regions.stream().map(region -> {
                RegionParam regionParam = new RegionParam();
                regionParam.setProvinceCode(region.getProvinceCode());
                regionParam.setCityCode(region.getCityCode());
                return regionParam;
            }).collect(Collectors.toList());
        }
        SelectActivityUsersParam param = new SelectActivityUsersParam();
        List<MiniProgramActivityOfferingClass> miniProgramActivityOfferingClasses = miniProgramActivityOfferingClassMapper
                .selectByExample(new MiniProgramActivityOfferingClassExample().createCriteria().andActivityIdEqualTo(activity.getId()).example());
        if (!CollectionUtils.isEmpty(miniProgramActivityOfferingClasses)) {
            List<String> offeringClasses = miniProgramActivityOfferingClasses.stream().map(MiniProgramActivityOfferingClass::getSpuOfferingClass).collect(Collectors.toList());
            param.setOfferingClasses(offeringClasses);
        }
        List<MiniProgramActivitySpuCode> miniProgramActivitySpuCodes = miniProgramActivitySpuCodeMapper
                .selectByExample(new MiniProgramActivitySpuCodeExample().createCriteria().andActivityIdEqualTo(activity.getId()).example());
        if (!CollectionUtils.isEmpty(miniProgramActivitySpuCodes)) {
            List<String> spuCodes = miniProgramActivitySpuCodes.stream().map(MiniProgramActivitySpuCode::getSpuOfferingCode).collect(Collectors.toList());
            param.setSpuCodes(spuCodes);
        }
        param.setRoleTypes(roleTypes);
        param.setRegions(regionParams);

        miniProgramActivityUserMapper.deleteByExample(
                new MiniProgramActivityUserExample().createCriteria()
                        .andActivityIdEqualTo(activity.getId()).example()
        );

        List<MiniProgramActivityUser> users = miniProgramActivityMapperExt.selectActivityUsers(param);
        log.info("users: {}", JSON.toJSONString(users));
        //users = users.stream().filter(Objects::nonNull).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(users)) {
            for (MiniProgramActivityUser user : users) {
                user.setId(BaseServiceUtils.getId());
                user.setActivityId(activity.getId());
            }
            int batchSize = 2000;
            int totalUsers = users.size();

            for (int i = 0; i < totalUsers; i += batchSize) {
                // 计算当前批次的结束索引
                int end = Math.min(i + batchSize, totalUsers);
                // 获取当前批次的子列表
                List<MiniProgramActivityUser> batchList = users.subList(i, end);
                miniProgramActivityUserMapper.batchInsert(batchList);
                log.info("已插入第{}到第{}个用户", i, end - 1);
            }
            log.info("排位赛“{}”参加人数：{}", activity.getName(), users.size());
        } else {
            log.info("排位赛“{}”参加人数：0", activity.getName());
        }
        long end = System.currentTimeMillis();
        log.info("保存参加排位赛的用户耗时：{}ms", end - begin);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @DS("query")
    public BaseAnswer<ActivityStatisticsVO> getRankActivityStatistics(String activityId, LoginIfo4Redis loginIfo4Redis) {

        MiniProgramActivity activity = miniProgramActivityMapper.selectByPrimaryKey(activityId);
        if (activity == null || activity.getIsDelete() == 1) {
            throw new BusinessException(BaseErrorConstant.MINI_PROGRAM_ACTIVITY_NOT_FORUND);
        }
        ActivityStatisticsVO rankActivityStatisticsVO = new ActivityStatisticsVO();
        Long TotalView = 0L;
        Long totalUser = 0L;
        //活动参与人数
        List<String> roleTypeList = new ArrayList<>();
        if ((activity.getTarget() & TARGET_MASK_MANAGER) != 0) {
            roleTypeList.add("4");
        }
        if ((activity.getTarget() & TARGET_MASK_DISTRIBUTOR) != 0) {
            roleTypeList.add("1");
            roleTypeList.add("2");
        }
        if ((activity.getTarget() & TARGET_MASK_CHANNEL) != 0) {
            roleTypeList.add("3");
        }
        List<MiniProgramActivityRegion> miniProgramActivityRegions = miniProgramActivityRegionMapper.selectByExample(new MiniProgramActivityRegionExample().createCriteria().andActivityIdEqualTo(activityId).example());
        List<RegionParam> regionParams = null;
        AtomicReference<String> str = new AtomicReference<>("");
        if (!CollectionUtils.isEmpty(miniProgramActivityRegions)) {
            regionParams = miniProgramActivityRegions.stream().map(region -> {
                RegionParam regionParam = new RegionParam();
                regionParam.setProvinceCode(region.getProvinceCode());
                regionParam.setCityCode(region.getCityCode());
                str.set(str + ",(" + region.getProvinceCode() + "," + region.getCityCode() + ")");

                return regionParam;
            }).collect(Collectors.toList());
        }
        log.info("活动地区：" + str);

        TotalView = activity.getViews() == null ? 0L : (long) activity.getViews();
        rankActivityStatisticsVO.setTotalView(TotalView);
        if (activity.getActivityType() == 1) {
            totalUser = totalUser + miniProgramActivityMapperExt.getActivityPersonNum(activityId);
            rankActivityStatisticsVO.setTotalUser(totalUser);
            Long allPersonNum = miniProgramActivityMapperExt.getPersonNum(roleTypeList, regionParams);
            double percentage = (totalUser / (double) allPersonNum) * 100;
            DecimalFormat df = new DecimalFormat("#.####");
            df.setMinimumFractionDigits(1);
            rankActivityStatisticsVO.setTotalCoverage(df.format(percentage));
            Long signUpNum = miniProgramActivityMapperExt.getActivityPersonNumSignUp(activityId);
            rankActivityStatisticsVO.setTotalEnroll(signUpNum);
            //获取销售总额,成交总量
//            RankActivityStatisticsParam param = new RankActivityStatisticsParam();
//            param.setStartTime(DateUtils.dateToStr(activity.getStartTime(), DateUtils.DATETIME_FORMAT_NO_SYMBOL));
//            param.setEndTime(DateUtils.dateToStr(activity.getStopTime(), DateUtils.DATETIME_FORMAT_NO_SYMBOL));
//            param.setRegionParamList(regionParams);
            ActivityStatisticsVO activityStatisticsVO = miniProgramActivityMapperExt.getTotalNum(activityId, null);
            if (activityStatisticsVO != null) {
                rankActivityStatisticsVO.setAtomOrderAmount(activityStatisticsVO.getAtomOrderAmount());
                rankActivityStatisticsVO.setAtomOrderCount(activityStatisticsVO.getAtomOrderCount());
            }

        } else if (activity.getActivityType() == 2) {
            //获取中将人数
            List<MiniProgramActivityUserAward> miniProgramActivityUserAwards = miniProgramActivityUserAwardMapper.selectByExample(new MiniProgramActivityUserAwardExample().createCriteria().andActivityIdEqualTo(activityId).example());
            if (miniProgramActivityUserAwards != null && miniProgramActivityUserAwards.size() > 0) {
                rankActivityStatisticsVO.setPersonNum(miniProgramActivityUserAwards.size());
            } else {
                rankActivityStatisticsVO.setPersonNum(0);
            }
            //获取参与人数
            List<MiniProgramActivityUser> activityUsers = miniProgramActivityUserMapper.selectByExample(new MiniProgramActivityUserExample().createCriteria().andActivityIdEqualTo(activityId).example());
            if (activityUsers != null && activityUsers.size() > 0) {
                totalUser = totalUser + activityUsers.size();
            }

            rankActivityStatisticsVO.setTotalUser(totalUser);

            Long allPersonNum = miniProgramActivityMapperExt.getPersonNum(roleTypeList, regionParams);
            List<MiniProgramActivityUser> users = miniProgramActivityUserMapper.selectByExample(new MiniProgramActivityUserExample().createCriteria().andActivityIdEqualTo(activityId).andIsDrawNotEqualTo(1).example());
            allPersonNum = users == null ? allPersonNum : allPersonNum + users.size();
            double percentage = (totalUser / (double) allPersonNum) * 100;
            DecimalFormat df = new DecimalFormat("#.####");
            df.setMinimumFractionDigits(1);
            rankActivityStatisticsVO.setTotalCoverage(df.format(percentage));


        } else {
            throw new BusinessException(StatusContant.MINI_PROGRAM_ACTIVITY_TYPR_ERROR);
        }


        return BaseAnswer.success(rankActivityStatisticsVO);
    }

    /**
     * 保存排位赛用户奖品
     *
     * @param activity
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    @DS("save")
    public void saveRankActivityUserAwards(MiniProgramActivity activity) {
        log.info("保存排位赛用户排名和奖品，活动id：{}，名称：{}", activity.getId(), activity.getName());
        long begin = System.currentTimeMillis();
        if (activity.getActivityType() != 1 || activity.getStatus() < 2) {
            return;
        }
        Date startTime = activity.getStartTime();
        Date stopTime = activity.getStopTime();
        SelectRankActivityUserAwardDTO selectRankActivityUserAwardDTO = new SelectRankActivityUserAwardDTO();
        selectRankActivityUserAwardDTO.setActivityId(activity.getId());
        selectRankActivityUserAwardDTO.setStartTime(startTime);
        selectRankActivityUserAwardDTO.setStopTime(stopTime);

        // 产品范式
        List<MiniProgramActivityOfferingClass> offeringClasses = miniProgramActivityOfferingClassMapper.selectByExample(
                new MiniProgramActivityOfferingClassExample().createCriteria().andActivityIdEqualTo(activity.getId()).example()
        );
        if (!CollectionUtils.isEmpty(offeringClasses)) {
            selectRankActivityUserAwardDTO.setOfferingClasses(
                    offeringClasses.stream().map(MiniProgramActivityOfferingClass::getSpuOfferingClass).collect(Collectors.toList())
            );
        }
        // SPU编码
        List<MiniProgramActivitySpuCode> spuCodes = miniProgramActivitySpuCodeMapper.selectByExample(
                new MiniProgramActivitySpuCodeExample().createCriteria().andActivityIdEqualTo(activity.getId()).example()
        );
        if (!CollectionUtils.isEmpty(spuCodes)) {
            selectRankActivityUserAwardDTO.setSpuCodes(
                    spuCodes.stream().map(MiniProgramActivitySpuCode::getSpuOfferingCode).collect(Collectors.toList())
            );
        }
        // 订单类型
        List<MiniProgramActivityOrderType> orderTypes = miniProgramActivityOrderTypeMapper.selectByExample(
                new MiniProgramActivityOrderTypeExample().createCriteria().andActivityIdEqualTo(activity.getId()).example()
        );
        if (!CollectionUtils.isEmpty(orderTypes)) {
            selectRankActivityUserAwardDTO.setOrderTypes(
                    orderTypes.stream().map(MiniProgramActivityOrderType::getOrderType).collect(Collectors.toList())
            );
        }
        // 客户类型
        List<MiniProgramActivityBusinessCode> businessCodes = miniProgramActivityBusinessCodeMapper.selectByExample(
                new MiniProgramActivityBusinessCodeExample().createCriteria().andActivityIdEqualTo(activity.getId()).example()
        );
        if (!CollectionUtils.isEmpty(businessCodes)) {
            selectRankActivityUserAwardDTO.setBusinessCodes(
                    businessCodes.stream().map(MiniProgramActivityBusinessCode::getBusinessCode).collect(Collectors.toList())
            );
        }

        // 查询实时排名,需要判断按销售额还是订单量排名
        List<MiniProgramActivityRank> miniProgramActivityRanks = miniProgramActivityRankMapper.selectByExample(
                new MiniProgramActivityRankExample().createCriteria().andActivityIdEqualTo(activity.getId()).example()
        );
        if (miniProgramActivityRanks == null) {
            return;
        }
        MiniProgramActivityRank miniProgramActivityRank = miniProgramActivityRanks.get(0);
        selectRankActivityUserAwardDTO.setSortType(miniProgramActivityRank.getSortType());
        List<MiniProgramActivityUserRankingDTO> rankings = miniProgramActivityMapperExt.selectRankActivityUserAwards(selectRankActivityUserAwardDTO);


        Map<Integer, String> awardMap = new HashMap<>();
        log.info("查询排位赛用户排名结束，活动id：{}，名称：{}", activity.getId(), activity.getName());
        // 查询本次活动奖品
        List<MiniProgramActivityRankAward> activityAwards = miniProgramActivityRankAwardMapper.selectByExample(
                new MiniProgramActivityRankAwardExample().createCriteria()
                        .andActivityIdEqualTo(activity.getId()).example()
        );

        if (!CollectionUtils.isEmpty(activityAwards)) {
            for (MiniProgramActivityRankAward activityAward : activityAwards) {
                int rankingFrom = activityAward.getRankingFrom();
                int rankingTo = activityAward.getRankingTo();
                for (int i = rankingFrom; i <= rankingTo; i++) {
                    awardMap.put(i, activityAward.getId());
                }
            }
        }


        List<MiniProgramActivityUserAward> userAwards = new ArrayList<>();
        if (!CollectionUtils.isEmpty(rankings)) {
            for (MiniProgramActivityUserRankingDTO ranking : rankings) {
                MiniProgramActivityUserAward userAward = new MiniProgramActivityUserAward();
                userAward.setId(BaseServiceUtils.getId());
                userAward.setActivityId(activity.getId());
                userAward.setRanking(ranking.getRanking());
                userAward.setUserId(ranking.getUserId());
                userAward.setAmount(ranking.getAmount());
                userAward.setTotal(ranking.getTotal());
                userAward.setStatus(0);
                if (ranking.getAmount() != 0) {
                    userAward.setAwardId(awardMap.get(ranking.getRanking()));
                }
                userAward.setCreateTime(new Date());
                userAward.setUpdateTime(new Date());
                userAwards.add(userAward);
            }
            log.info("删除排位赛用户排名和奖品结束，活动id：{}，名称：{}", activity.getId(), activity.getName());
            miniProgramActivityUserAwardMapper.deleteByExample(
                    new MiniProgramActivityUserAwardExample().createCriteria()
                            .andActivityIdEqualTo(activity.getId())
                            .example()
            );

            int batchSize = 2000;
            int totalUsers = userAwards.size();

            for (int i = 0; i < totalUsers; i += batchSize) {
                // 计算当前批次的结束索引
                int end = Math.min(i + batchSize, totalUsers);
                // 获取当前批次的子列表
                List<MiniProgramActivityUserAward> batchList = userAwards.subList(i, end);
                miniProgramActivityUserAwardMapper.batchInsert(batchList);
                log.info("已插入第{}到第{}个奖品", i, end - 1);
            }
            log.info("保存排位赛用户排名和奖品，活动id：{}，名称：{}，保存成功", activity.getId(), activity.getName());
        }
        long end = System.currentTimeMillis();
        log.info("保存排位赛用户奖品耗时：{}ms", end - begin);
    }

    @Override
    @DS("query")
    public List<ActivityAccessVO> getDataRankVisitCount(String activityId, String userId) {
        MiniProgramActivity miniProgramActivity = miniProgramActivityMapper.selectByPrimaryKey(activityId);
        Date startTime = miniProgramActivity.getStartTime();
        Date stopTime = miniProgramActivity.getStopTime();
        // 将 Date 转换为 LocalDate
        LocalDate startLocalDate = startTime.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        LocalDate stopLocalDate = stopTime.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        Map<LocalDate, Long> dateMap = new TreeMap<>();
        // 初始化 Map，设置所有日期的初始值为0
        LocalDate currentDate = startLocalDate;
        List<ActivityAccessVO> re = new ArrayList<>();
        while (!currentDate.isAfter(stopLocalDate)) {
            ActivityAccessVO activityAccessVO = new ActivityAccessVO();
            activityAccessVO.setAccessTime(Date.from(currentDate.atStartOfDay(ZoneId.systemDefault()).toInstant()));
            SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
            String dateString = formatter.format(Date.from(currentDate.atStartOfDay(ZoneId.systemDefault()).toInstant()));
            activityAccessVO.setAccessTimeString(dateString);
            activityAccessVO.setCount(0L);
            re.add(activityAccessVO);
//            dateMap.put(currentDate, 0L);
            currentDate = currentDate.plusDays(1);
        }

        List<ActivityAccessVO> list = miniProgramActivityMapperExt.getDataRankVisitCount(activityId);

        for (ActivityAccessVO accessItem : list) {
//            LocalDate date = accessItem.getAccessTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
//            dateMap.put(date, accessItem.getCount());
            for (ActivityAccessVO activityAccessVOItem : re) {
                if (activityAccessVOItem.getAccessTime().equals(accessItem.getAccessTime())) {
                    activityAccessVOItem.setCount(accessItem.getCount());
                }
            }
        }
        String headerName = "";
        if (miniProgramActivity.getActivityType() == 1) {
            headerName = "【查看排位赛数据看板】";
        } else if (miniProgramActivity.getActivityType() == 2) {
            headerName = "【即客周周乐-查看中奖名单】";
        }
        logService.recordOperateLog(ModuleEnum.MINI_PROGRAM.code, MiniProgramOperateEnum.ACTIVITY.code,
                getLogContent(headerName, miniProgramActivity.getName(), null, null, null), userId, 0, LogResultEnum.LOG_SUCESS.code, null);

        return re;
    }

    public static Object[][] convertMapToArray(Map<LocalDate, Long> map) {
        List<Map.Entry<LocalDate, Long>> entryList = new ArrayList<>(map.entrySet());
        Object[][] resultArray = new Object[entryList.size()][2];

        int index = 0;
        for (Map.Entry<LocalDate, Long> entry : entryList) {
            resultArray[index][0] = entry.getKey();
            resultArray[index][1] = entry.getValue();
            index++;
        }

        return resultArray;
    }

    @Override
    @DS("query")
    public List<ActivityPointSupplierVO> getPointSupplierList() {
        List<PointSupplier> pointSupplierList = pointSupplierMapper.selectByExample(new PointSupplierExample());
        if (!CollectionUtils.isEmpty(pointSupplierList)) {
            return pointSupplierList.stream().map(pointSupplier -> {
                ActivityPointSupplierVO activityPointSupplierVO = new ActivityPointSupplierVO();
                activityPointSupplierVO.setSupplierId(pointSupplier.getId());
                activityPointSupplierVO.setSupplierName(pointSupplier.getFullName());
                return activityPointSupplierVO;
            }).collect(Collectors.toList());
        } else {
            return Collections.emptyList();
        }
    }

    @Override
    @DS("query")
    public List<ActivitySearchSpuVO> searchSpuByCode(String spuCode) {
//        SpuOfferingInfoExample spuOfferingInfoExample = new SpuOfferingInfoExample();
//        spuOfferingInfoExample.createCriteria().andOfferingCodeLike(spuCode + "%").andOfferingStatusEqualTo("1").andDeleteTimeIsNull();
//        spuOfferingInfoExample.or().andOfferingNameLike(spuCode + "%").andOfferingStatusEqualTo("1");
//        List<SpuOfferingInfo> spuOfferingInfos = spuOfferingInfoMapper.selectByExample(spuOfferingInfoExample);
        List<SpuOfferingInfo> spuOfferingInfos = miniProgramActivityMapperExt.searchSpuByCode(spuCode);
        if (!CollectionUtils.isEmpty(spuOfferingInfos)) {
            return spuOfferingInfos.stream().map(spuOfferingInfo -> {
                ActivitySearchSpuVO activitySearchSpuVO = new ActivitySearchSpuVO();
                activitySearchSpuVO.setSpuCode(spuOfferingInfo.getOfferingCode());
                activitySearchSpuVO.setSpuName(spuOfferingInfo.getOfferingName());
                activitySearchSpuVO.setOfferingStatus(spuOfferingInfo.getOfferingStatus());
                return activitySearchSpuVO;
            }).collect(Collectors.toList());
        } else {
            return Collections.emptyList();
        }
    }

    @Override
    @DS("query")
    public List<ActivitySearchSpuVO> searchSpuAllByCode(String spuCode) {
//        SpuOfferingInfoExample spuOfferingInfoExample = new SpuOfferingInfoExample();
//        spuOfferingInfoExample.createCriteria().andOfferingCodeLike(spuCode + "%").andOfferingStatusEqualTo("1").andDeleteTimeIsNull();
//        spuOfferingInfoExample.or().andOfferingNameLike(spuCode + "%").andOfferingStatusEqualTo("1");
//        List<SpuOfferingInfo> spuOfferingInfos = spuOfferingInfoMapper.selectByExample(spuOfferingInfoExample);
        List<SpuOfferingInfo> spuOfferingInfos = miniProgramActivityMapperExt.searchSpuAllByCode(spuCode);
        if (!CollectionUtils.isEmpty(spuOfferingInfos)) {
            return spuOfferingInfos.stream().map(spuOfferingInfo -> {
                ActivitySearchSpuVO activitySearchSpuVO = new ActivitySearchSpuVO();
                activitySearchSpuVO.setSpuCode(spuOfferingInfo.getOfferingCode());
                activitySearchSpuVO.setSpuName(spuOfferingInfo.getOfferingName());
                activitySearchSpuVO.setOfferingStatus(spuOfferingInfo.getOfferingStatus());
                return activitySearchSpuVO;
            }).collect(Collectors.toList());
        } else {
            return Collections.emptyList();
        }
    }

    @Override
    @DS("query")
    public List<ActivitySearchUserVO> searchUserByPhone(String phone) {
        List<UserMiniProgram> userMiniPrograms = userMiniProgramMapper.selectByExample(
                new UserMiniProgramExample().createCriteria()
                        .andPhoneLike(phone + "%")
                        .andRoleTypeNotEqualTo("0")
                        .example()
        );
        if (!CollectionUtils.isEmpty(userMiniPrograms)) {
            return userMiniPrograms.stream().map(userMiniProgram -> {
                ActivitySearchUserVO activitySearchUserVO = new ActivitySearchUserVO();
                activitySearchUserVO.setUserId(userMiniProgram.getUserId());
                activitySearchUserVO.setPhone(userMiniProgram.getPhone());
                activitySearchUserVO.setUserName(userMiniProgram.getName());
                return activitySearchUserVO;
            }).collect(Collectors.toList());
        }
        return Collections.emptyList();
    }

    @Override
    @DS("query")
    public List<ActivityProvinceVO> getAllRegions() {
        return redisCacheUtil.loadAndCache(
                Constant.REDIS_KEY_PROVINCE_AND_CITY,
                RedisLockConstant.PRODUCT_MINI_PROVINCE_AND_CITY,
                () -> {
                    List<ActivityProvinceVO> provinces = miniProgramActivityMapperExt.getAllRegions();
                    provinces = provinces.stream()
                            .peek(activityProvinceVO -> {
                                activityProvinceVO.setPinyin(PinyinUtils.getUpperCase(activityProvinceVO.getProvinceName(), true));
                                if ("ZHONGQING".equals(activityProvinceVO.getPinyin())) {
                                    activityProvinceVO.setPinyin("CHONGQING");
                                }
                            })
                            .filter(activityProvinceVO -> !"000".equals(activityProvinceVO.getProvinceCode()) && !"001".equals(activityProvinceVO.getProvinceCode()) && !"002".equals(activityProvinceVO.getProvinceCode()))
                            .sorted(Comparator.comparing(ActivityProvinceVO::getPinyin)).collect(Collectors.toList());
                    return provinces;
                }
        );
    }

    private String generatePublishRankActivityLogContent(PublishRankActivityParam param) {
        StringBuilder sb = new StringBuilder();
        if (null == param.getId()) {
            sb.append("【创建活动】\n活动名称：").append(param.getName());
        } else {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            MiniProgramActivity activity = miniProgramActivityMapper.selectByPrimaryKey(param.getId());
            MiniProgramActivityRank rank = miniProgramActivityRankMapper.selectByExample(
                    new MiniProgramActivityRankExample().createCriteria().andActivityIdEqualTo(param.getId()).example()
            ).get(0);
            List<MiniProgramActivityRegion> miniProgramActivityRegions = miniProgramActivityRegionMapper.selectByExample(
                    new MiniProgramActivityRegionExample().createCriteria().andActivityIdEqualTo(param.getId()).example()
            );
            List<MiniProgramActivityOfferingClass> miniProgramActivityOfferingClasses = miniProgramActivityOfferingClassMapper.selectByExample(
                    new MiniProgramActivityOfferingClassExample().createCriteria().andActivityIdEqualTo(param.getId()).example()
            );
            List<MiniProgramActivitySpuCode> miniProgramActivitySpuCodes = miniProgramActivitySpuCodeMapper.selectByExample(
                    new MiniProgramActivitySpuCodeExample().createCriteria().andActivityIdEqualTo(param.getId()).example()
            );
            List<MiniProgramActivityOrderType> miniProgramActivityOrderTypes = miniProgramActivityOrderTypeMapper.selectByExample(
                    new MiniProgramActivityOrderTypeExample().createCriteria().andActivityIdEqualTo(param.getId()).example()
            );
            List<MiniProgramActivityBusinessCode> miniProgramActivityBusinessCodes = miniProgramActivityBusinessCodeMapper.selectByExample(
                    new MiniProgramActivityBusinessCodeExample().createCriteria().andActivityIdEqualTo(param.getId()).example()
            );
            List<MiniProgramActivityRankAward> miniProgramActivityRankAwards = miniProgramActivityRankAwardMapper.selectByExample(
                    new MiniProgramActivityRankAwardExample().createCriteria().andActivityIdEqualTo(param.getId()).example()
            );
            sb.append("【编辑活动】\n");
            if (!ObjectUtils.nullSafeEquals(param.getName(), activity.getName())) {
                sb.append("活动名称由 ").append(activity.getName()).append(" 修改为 ").append(param.getName()).append("。\n");
            }
            if (!ObjectUtils.nullSafeEquals(listToTarget(param.getTargetList()), activity.getTarget())) {
                sb.append("活动对象由 ").append(targetToStr(activity.getTarget())).append(" 修改为 ").append(targetToStr(listToTarget(param.getTargetList()))).append("。\n");
            }
            String region = getRegionLogStr(param.getRegions());

            List<RegionParam> oldRegionParams = new ArrayList<>();
            if (!CollectionUtils.isEmpty(miniProgramActivityRegions)) {
                oldRegionParams = miniProgramActivityRegions.stream().map(miniProgramActivityRegion -> {
                    RegionParam regionParam = new RegionParam();
                    BeanUtils.copyProperties(miniProgramActivityRegion, regionParam);
                    return regionParam;
                }).collect(Collectors.toList());
            }
            String oldRegion = getRegionLogStr(oldRegionParams);
            if (!ObjectUtils.nullSafeEquals(region, oldRegion)) {
                sb.append("活动省市由 ").append(oldRegion).append(" 修改为 ").append(region).append("。\n");
            }
            if (!ObjectUtils.nullSafeEquals(param.getStartTime(), activity.getStartTime()) || !ObjectUtils.nullSafeEquals(param.getStopTime(), activity.getStopTime())) {
                sb.append("活动时间由 ").append(sdf.format(activity.getStartTime())).append("-").append(sdf.format(activity.getStopTime()))
                        .append(" 修改为 ").append(sdf.format(param.getStartTime())).append("-").append(sdf.format(param.getStopTime())).append("。\n");
            }
            if (!ObjectUtils.nullSafeEquals(param.getConfirmTime(), activity.getConfirmTime())) {
                sb.append("订单确认时间由 ").append(sdf.format(activity.getConfirmTime())).append(" 修改为 ").append(sdf.format(param.getConfirmTime())).append("。\n");
            }
            if (!ObjectUtils.nullSafeEquals(param.getSettlementTime(), activity.getSettlementTime())) {
                sb.append("结算时间由 ").append(sdf.format(activity.getSettlementTime())).append(" 修改为 ").append(sdf.format(param.getSettlementTime())).append("。\n");
            }
            if (!ObjectUtils.nullSafeEquals(param.getJoinDefault(), rank.getJoinDefault())) {
                sb.append("默认报名参与由 ").append(rank.getJoinDefault() ? "是" : "否").append(" 修改为 ").append(param.getJoinDefault() ? "是" : "否").append("。\n");
            }
            String offeringClass = getOfferingClassLogStr(param.getOfferingClasses());
            if (!CollectionUtils.isEmpty(miniProgramActivityOfferingClasses)) {
                List<String> list = miniProgramActivityOfferingClasses.stream().map(MiniProgramActivityOfferingClass::getSpuOfferingClass).collect(Collectors.toList());
                String oldOfferingClass = getOfferingClassLogStr(list);
                if (!ObjectUtils.nullSafeEquals(oldOfferingClass, offeringClass)) {
                    sb.append("产品范式由 ").append(oldOfferingClass).append(" 修改为 ").append(offeringClass).append("。\n");
                }
            }
            String spuCode = getSpuCodeLogStr(param.getSpuCodes());
            if (!CollectionUtils.isEmpty(miniProgramActivitySpuCodes)) {
                List<String> list = miniProgramActivitySpuCodes.stream().map(MiniProgramActivitySpuCode::getSpuOfferingCode).collect(Collectors.toList());
                String oldSpuCode = getSpuCodeLogStr(list);
                if (!ObjectUtils.nullSafeEquals(oldSpuCode, spuCode)) {
                    sb.append("SPU编码由 ").append(oldSpuCode).append(" 修改为 ").append(spuCode).append("。\n");
                }
            }
            String orderType = getOrderTypeLogStr(param.getOrderTypes());
            if (!CollectionUtils.isEmpty(miniProgramActivityOrderTypes)) {
                List<String> list = miniProgramActivityOrderTypes.stream().map(MiniProgramActivityOrderType::getOrderType).collect(Collectors.toList());
                String oldOrderType = getOrderTypeLogStr(list);
                if (!ObjectUtils.nullSafeEquals(oldOrderType, orderType)) {
                    sb.append("订单类型由 ").append(oldOrderType).append(" 修改为 ").append(orderType).append("。\n");
                }
            }
            String businessCode = getBusinessCodeLogStr(param.getBusinessCodes());
            if (!CollectionUtils.isEmpty(miniProgramActivityBusinessCodes)) {
                List<String> list = miniProgramActivityBusinessCodes.stream().map(MiniProgramActivityBusinessCode::getBusinessCode).collect(Collectors.toList());
                String oldBusinessCode = getBusinessCodeLogStr(list);
                if (!ObjectUtils.nullSafeEquals(oldBusinessCode, businessCode)) {
                    sb.append("客户类型由 ").append(oldBusinessCode).append(" 修改为 ").append(businessCode).append("。\n");
                }
            }
            if (!ObjectUtils.nullSafeEquals(param.getListImg(), activity.getListImg())) {
                sb.append("活动聚合页图片由 ").append(activity.getListImg()).append(" 修改为 ").append(param.getListImg()).append("。\n");
            }
            if (!ObjectUtils.nullSafeEquals(param.getDetailImg(), rank.getDetailImg())) {
                sb.append("活动头图由 ").append(rank.getDetailImg()).append(" 修改为 ").append(param.getDetailImg()).append("。\n");
            }
            if (!ObjectUtils.nullSafeEquals(param.getShareImg(), activity.getShareImg())) {
                sb.append("卡片宣传图由 ").append(activity.getShareImg()).append(" 修改为 ").append(param.getShareImg()).append("。\n");
            }
            if (!ObjectUtils.nullSafeEquals(param.getDescription(), rank.getDescription())) {
                sb.append("活动说明由 ").append(rank.getDescription()).append(" 修改为 ").append(param.getDescription()).append("。\n");
            }
            if (!ObjectUtils.nullSafeEquals(param.getParticipation(), rank.getParticipation())) {
                sb.append("参与方式由 ").append(rank.getParticipation()).append(" 修改为 ").append(param.getParticipation()).append("。\n");
            }
            if (!ObjectUtils.nullSafeEquals(param.getAward(), rank.getAward())) {
                sb.append("达标奖励由 ").append(rank.getAward()).append(" 修改为 ").append(param.getAward()).append("。\n");
            }
            if (!ObjectUtils.nullSafeEquals(param.getRule(), rank.getRule())) {
                sb.append("活动规则由 ").append(rank.getRule()).append(" 修改为 ").append(param.getRule()).append("。\n");
            }
            if (!ObjectUtils.nullSafeEquals(param.getAdditional(), rank.getAdditional())) {
                sb.append("补充说明由 ").append(rank.getAdditional()).append(" 修改为 ").append(param.getAdditional()).append("。\n");
            }
            String awardsLogStr = getRankActivityAwardsLogStr(miniProgramActivityRankAwards, param.getAwards());
            sb.append(awardsLogStr);
        }
        return sb.toString();
    }

    private String generatePublishWeeklyActivityLogContent(PublishWeeklyActivityParam param) {
        StringBuilder sb = new StringBuilder();
        if (null == param.getId()) {
            sb.append("【创建活动】\n活动名称：").append(param.getName());
        } else {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            MiniProgramActivity activity = miniProgramActivityMapper.selectByPrimaryKey(param.getId());
            MiniProgramActivityWeeklyFun weeklyFun = miniProgramActivityWeeklyFunMapper.selectByExample(
                    new MiniProgramActivityWeeklyFunExample().createCriteria().andActivityIdEqualTo(param.getId()).example()
            ).get(0);
            List<MiniProgramActivityRegion> miniProgramActivityRegions = miniProgramActivityRegionMapper.selectByExample(
                    new MiniProgramActivityRegionExample().createCriteria().andActivityIdEqualTo(param.getId()).example()
            );
            List<MiniProgramActivityOfferingClass> miniProgramActivityOfferingClasses = miniProgramActivityOfferingClassMapper.selectByExample(
                    new MiniProgramActivityOfferingClassExample().createCriteria().andActivityIdEqualTo(param.getId()).example()
            );
            List<MiniProgramActivitySpuCode> miniProgramActivitySpuCodes = miniProgramActivitySpuCodeMapper.selectByExample(
                    new MiniProgramActivitySpuCodeExample().createCriteria().andActivityIdEqualTo(param.getId()).example()
            );
            List<MiniProgramActivityUser> miniProgramActivityUsers = miniProgramActivityUserMapper.selectByExample(
                    new MiniProgramActivityUserExample().createCriteria().andActivityIdEqualTo(param.getId()).example()
            );
            List<MiniProgramActivityWeeklyFunAward> miniProgramActivityWeeklyFunAwards = miniProgramActivityWeeklyFunAwardMapper.selectByExample(
                    new MiniProgramActivityWeeklyFunAwardExample().createCriteria().andActivityIdEqualTo(param.getId()).example()
            );
            sb.append("【编辑活动】\n");
            if (!ObjectUtils.nullSafeEquals(param.getName(), activity.getName())) {
                sb.append("活动名称由 ").append(activity.getName()).append(" 修改为 ").append(param.getName()).append("。\n");
            }
            if (!ObjectUtils.nullSafeEquals(listToTarget(param.getTargetList()), activity.getTarget())) {
                sb.append("活动对象由 ").append(targetToStr(activity.getTarget())).append(" 修改为 ").append(targetToStr(listToTarget(param.getTargetList()))).append("。\n");
            }
            String region = getRegionLogStr(param.getRegions());

            List<RegionParam> oldRegionParams = new ArrayList<>();
            if (!CollectionUtils.isEmpty(miniProgramActivityRegions)) {
                oldRegionParams = miniProgramActivityRegions.stream().map(miniProgramActivityRegion -> {
                    RegionParam regionParam = new RegionParam();
                    BeanUtils.copyProperties(miniProgramActivityRegion, regionParam);
                    return regionParam;
                }).collect(Collectors.toList());
            }
            String oldRegion = getRegionLogStr(oldRegionParams);
            if (!ObjectUtils.nullSafeEquals(region, oldRegion)) {
                sb.append("活动省市由 ").append(oldRegion).append(" 修改为 ").append(region).append("。\n");
            }
            if (!ObjectUtils.nullSafeEquals(param.getStartTime(), activity.getStartTime()) || !ObjectUtils.nullSafeEquals(param.getStopTime(), activity.getStopTime())) {
                sb.append("活动时间由 ").append(sdf.format(activity.getStartTime())).append("-").append(sdf.format(activity.getStopTime()))
                        .append(" 修改为 ").append(sdf.format(param.getStartTime())).append("-").append(sdf.format(param.getStopTime())).append("。\n");
            }
            if (!ObjectUtils.nullSafeEquals(param.getConfirmTime(), activity.getConfirmTime())) {
                sb.append("订单确认时间由 ").append(sdf.format(activity.getConfirmTime())).append(" 修改为 ").append(sdf.format(param.getConfirmTime())).append("。\n");
            }
            if (!ObjectUtils.nullSafeEquals(param.getSettlementTime(), activity.getSettlementTime())) {
                sb.append("结算时间由 ").append(sdf.format(activity.getSettlementTime())).append(" 修改为 ").append(sdf.format(param.getSettlementTime())).append("。\n");
            }
            if (!ObjectUtils.nullSafeEquals(param.getMaxPlayer(), weeklyFun.getMaxPlayer())) {
                sb.append("最大参与人数由 ").append(weeklyFun.getMaxPlayer()).append(" 修改为 ").append(param.getMaxPlayer()).append("。\n");
            }
            if (!ObjectUtils.nullSafeEquals(param.getOrderStart(), weeklyFun.getOrderStart()) || !ObjectUtils.nullSafeEquals(param.getOrderStop(), weeklyFun.getOrderStop())) {
                sb.append("订单时间由 ").append(sdf.format(weeklyFun.getOrderStart())).append("-").append(sdf.format(weeklyFun.getOrderStop()))
                        .append(" 修改为 ").append(sdf.format(param.getOrderStart())).append("-").append(sdf.format(param.getOrderStop())).append("。\n");
            }
            if (!ObjectUtils.nullSafeEquals(param.getOrderCount(), weeklyFun.getOrderCount())) {
                sb.append("订单数量由 ").append(weeklyFun.getOrderCount()).append(" 修改为 ").append(param.getOrderCount()).append("。\n");
            }
            if (!ObjectUtils.nullSafeEquals(param.getRegisterStart(), weeklyFun.getRegisterStart()) || !ObjectUtils.nullSafeEquals(param.getRegisterStop(), weeklyFun.getRegisterStop())) {
                sb.append("销售人员注册时间由 ").append(sdf.format(weeklyFun.getRegisterStart())).append("-").append(sdf.format(weeklyFun.getRegisterStop()))
                        .append(" 修改为 ").append(sdf.format(param.getRegisterStart())).append("-").append(sdf.format(param.getRegisterStop())).append("。\n");
            }
            String offeringClass = getOfferingClassLogStr(param.getOfferingClasses());
            if (!CollectionUtils.isEmpty(miniProgramActivityOfferingClasses)) {
                List<String> list = miniProgramActivityOfferingClasses.stream().map(MiniProgramActivityOfferingClass::getSpuOfferingClass).collect(Collectors.toList());
                String oldOfferingClass = getOfferingClassLogStr(list);
                if (!ObjectUtils.nullSafeEquals(oldOfferingClass, offeringClass)) {
                    sb.append("产品范式由 ").append(oldOfferingClass).append(" 修改为 ").append(offeringClass).append("。\n");
                }
            }
            String spuCode = getSpuCodeLogStr(param.getSpuCodes());
            if (!CollectionUtils.isEmpty(miniProgramActivitySpuCodes)) {
                List<String> list = miniProgramActivitySpuCodes.stream().map(MiniProgramActivitySpuCode::getSpuOfferingCode).collect(Collectors.toList());
                String oldSpuCode = getSpuCodeLogStr(list);
                if (!ObjectUtils.nullSafeEquals(oldSpuCode, spuCode)) {
                    sb.append("SPU编码由 ").append(oldSpuCode).append(" 修改为 ").append(spuCode).append("。\n");
                }
            }
            List<String> userIds = null;
            String user = null;
            if (!CollectionUtils.isEmpty(param.getActivityUsers())) {
                userIds = param.getActivityUsers().stream().map(ActivityUserParam::getUserId).collect(Collectors.toList());
                user = getActivityUserLogStr(userIds);
            }
            List<String> oldUserIds = null;
            String oldUser = null;
            if (!CollectionUtils.isEmpty(miniProgramActivityUsers)) {
                oldUserIds = miniProgramActivityUsers.stream().map(MiniProgramActivityUser::getUserId).collect(Collectors.toList());
                oldUser = getActivityUserLogStr(oldUserIds);
            }
            if (!ObjectUtils.nullSafeEquals(oldUser, user)) {
                sb.append("手动添加参与人员由 ").append(oldUser).append(" 修改为 ").append(user).append("。\n");
            }
            if (!ObjectUtils.nullSafeEquals(param.getListImg(), activity.getListImg())) {
                sb.append("活动聚合页图片由 ").append(activity.getListImg()).append(" 修改为 ").append(param.getListImg()).append("。\n");
            }
            if (!ObjectUtils.nullSafeEquals(param.getDescription(), weeklyFun.getDescription())) {
                sb.append("活动说明由 ").append(weeklyFun.getDescription()).append(" 修改为 ").append(param.getDescription()).append("。\n");
            }
            if (!ObjectUtils.nullSafeEquals(param.getRule(), weeklyFun.getRule())) {
                sb.append("活动规则由 ").append(weeklyFun.getRule()).append(" 修改为 ").append(param.getRule()).append("。\n");
            }
            if (!ObjectUtils.nullSafeEquals(param.getWheelParts(), weeklyFun.getWheelParts())) {
                sb.append("轮盘等分数量由 ").append(weeklyFun.getWheelParts()).append(" 修改为 ").append(param.getWheelParts()).append("。\n");
            }
            String awardsLogStr = getWeeklyActivityAwardsLogStr(miniProgramActivityWeeklyFunAwards, param.getAwards());
            sb.append(awardsLogStr);
        }
        return sb.toString();
    }

    private List<Integer> targetToList(int target) {
        List<Integer> targetList = new ArrayList<>();
        if ((TARGET_MASK_MANAGER & target) != 0) {
            targetList.add(TARGET_MASK_MANAGER);
        }
        if ((TARGET_MASK_DISTRIBUTOR & target) != 0) {
            targetList.add(TARGET_MASK_DISTRIBUTOR);
        }
        if ((TARGET_MASK_CHANNEL & target) != 0) {
            targetList.add(TARGET_MASK_CHANNEL);
        }
        return targetList;
    }

    private int listToTarget(List<Integer> targetList) {
        int target = 0;
        for (Integer t : targetList) {
            target |= t;
        }
        return target;
    }

    private String targetToStr(int target) {
        List<String> list = new ArrayList<>();
        if ((TARGET_MASK_MANAGER & target) != 0) {
            list.add("客户经理");
        }
        if ((TARGET_MASK_DISTRIBUTOR & target) != 0) {
            list.add("分销商");
        }
        if ((TARGET_MASK_CHANNEL & target) != 0) {
            list.add("渠道商");
        }
        String result = StringUtils.join(list, ",");
        return "【" + result + "】";
    }

    private String getRegionLogStr(List<RegionParam> params) {
        if (CollectionUtils.isEmpty(params)) {
            return "";
        }
        List<String> regionList = params.stream().sorted(
                Comparator.comparing(RegionParam::getProvinceCode, Comparator.nullsLast(String::compareTo))
                        .thenComparing(RegionParam::getCityCode, Comparator.nullsLast(String::compareTo))
        ).map(regionParam -> {
            if (regionParam.getCityCode() != null) {
                List<ContractCityInfo> cityInfos = contractCityInfoMapper.selectByExample(
                        new ContractCityInfoExample().createCriteria()
                                .andProvinceMallCodeEqualTo(regionParam.getProvinceCode())
                                .andMallCodeEqualTo(regionParam.getCityCode())
                                .example()
                );
                if (!CollectionUtils.isEmpty(cityInfos)) {
                    ContractCityInfo cityInfo = cityInfos.get(0);
                    return cityInfo.getProvinceMallName() + " " + cityInfo.getMallName();
                } else {
                    return "";
                }
            } else {
                List<ContractProvinceInfo> provinceInfos = contractProvinceInfoMapper.selectByExample(
                        new ContractProvinceInfoExample().createCriteria()
                                .andMallCodeEqualTo(regionParam.getProvinceCode())
                                .example()
                );
                if (!CollectionUtils.isEmpty(provinceInfos)) {
                    ContractProvinceInfo provinceInfo = provinceInfos.get(0);
                    return provinceInfo.getMallName();
                } else {
                    return "";
                }
            }
        }).collect(Collectors.toList());
        return String.join("，", regionList);
    }

    private String getOfferingClassLogStr(List<String> offeringClasses) {
        if (CollectionUtils.isEmpty(offeringClasses)) {
            return "";
        }
        List<String> list = offeringClasses.stream().sorted().map(ActivityOfferingClassEnum::getDisplay).collect(Collectors.toList());
        return String.join("，", list);
    }

    private String getSpuCodeLogStr(List<String> spuCodes) {
        if (CollectionUtils.isEmpty(spuCodes)) {
            return "";
        }
        List<String> list = spuCodes.stream().sorted().collect(Collectors.toList());
        return String.join("，", list);
    }

    private String getOrderTypeLogStr(List<String> orderTypes) {
        if (CollectionUtils.isEmpty(orderTypes)) {
            return "";
        }
        List<String> list = orderTypes.stream().sorted().map(s -> {
            if ("00".equals(s)) {
                return "代客下单";
            } else if (toCustomerOrderType.contains(s)) {
                return "自主下单";
            }  /*else if ("01".equals(s)) {
                return "自主下单";
            } else if ("02".equals(s)) {
                return "代客下单";
            } */ else {
                return "";
            }
        }).collect(Collectors.toList());
        return String.join("，", list);
    }

    private String getBusinessCodeLogStr(List<String> businessCodes) {
        if (CollectionUtils.isEmpty(businessCodes)) {
            return "";
        }
        List<String> list = businessCodes.stream().sorted().map(s -> {
            if ("SyncGrpOrderInfo".equals(s)) {
                return "集团客户";
            } else if ("SyncIndividualOrderInfo".equals(s)) {
                return "个人客户";
            } else {
                return "";
            }
        }).collect(Collectors.toList());
        return String.join("，", list);
    }

    private String getActivityUserLogStr(List<String> userId) {
        if (CollectionUtils.isEmpty(userId)) {
            return "";
        }
        List<UserMiniProgram> users = userMiniProgramMapper.selectByExample(
                new UserMiniProgramExample().createCriteria().andUserIdIn(userId).example()
        );
        if (!CollectionUtils.isEmpty(users)) {
            List<String> list = users.stream().map(userMiniProgram -> "(" + userMiniProgram.getName() + "," + userMiniProgram.getPhone() + ")").collect(Collectors.toList());
            return String.join(" ", list);
        } else {
            return "";
        }
    }

    private String getRankActivityAwardsLogStr(List<MiniProgramActivityRankAward> oldAwards, List<RankActivityAwardsParam> awards) {
        StringBuilder sb = new StringBuilder();
        if (!isRankActivityAwardsEqual(oldAwards, awards)) {
            sb.append("活动奖品由：\n");
            if (!CollectionUtils.isEmpty(oldAwards)) {
                for (MiniProgramActivityRankAward oldAward : oldAwards) {
                    if (1 == oldAward.getType()) {
                        PointSupplier pointSupplier = pointSupplierMapper.selectByPrimaryKey(oldAward.getSupplierId());
                        sb.append("积分；")
                                .append("积分额度：").append(oldAward.getPoints()).append("，")
                                .append("积分供应商：").append(pointSupplier != null ? pointSupplier.getFullName() : " ").append("，")
                                .append("名次：").append(oldAward.getRankingFrom()).append("至").append(oldAward.getRankingTo()).append("，")
                                .append("奖项：").append(oldAward.getAwardName())
                                .append("\n");
                    } else if (2 == oldAward.getType()) {
                        sb.append("产品：").append(oldAward.getProduct()).append("，")
                                .append("图片：").append(oldAward.getProductImg()).append("，")
                                .append("排名：").append(oldAward.getRankingFrom()).append("-").append(oldAward.getRankingTo()).append("，")
                                .append("奖项：").append(oldAward.getAwardName())
                                .append("\n");
                    }
                }
            } else {
                sb.append("空");
            }
            sb.append("修改为：\n");
            if (!CollectionUtils.isEmpty(awards)) {
                for (RankActivityAwardsParam award : awards) {
                    if (1 == award.getType()) {
                        PointSupplier pointSupplier = pointSupplierMapper.selectByPrimaryKey(award.getSupplierId());
                        sb.append("积分；")
                                .append("积分额度：").append(award.getPoints()).append("，")
                                .append("积分供应商：").append(pointSupplier != null ? pointSupplier.getFullName() : " ").append("，")
                                .append("名次：").append(award.getRankingFrom()).append("至").append(award.getRankingTo()).append("，")
                                .append("奖项：").append(award.getAwardName())
                                .append("\n");
                    } else if (2 == award.getType()) {
                        sb.append("产品；")
                                .append("产品名称：").append(award.getProduct()).append("，")
                                .append("图片：").append(award.getProductImg()).append("，")
                                .append("名次：").append(award.getRankingFrom()).append("-").append(award.getRankingTo()).append("，")
                                .append("奖项：").append(award.getAwardName())
                                .append("\n");
                    }
                }
            } else {
                sb.append("空");
            }
        }
        return sb.toString();
    }

    private String getWeeklyActivityAwardsLogStr(List<MiniProgramActivityWeeklyFunAward> oldAwards, List<WeeklyActivityAwardsParam> awards) {
        StringBuilder sb = new StringBuilder();
        if (!isWeeklyAwardsEqual(oldAwards, awards)) {
            sb.append("达标奖励由：\n");
            if (!CollectionUtils.isEmpty(oldAwards)) {
                for (MiniProgramActivityWeeklyFunAward oldAward : oldAwards) {
                    if (1 == oldAward.getType()) {
                        PointSupplier pointSupplier = pointSupplierMapper.selectByPrimaryKey(oldAward.getSupplierId());
                        sb.append("积分；")
                                .append("积分额度：").append(oldAward.getPoints()).append("，")
                                .append("积分供应商：").append(pointSupplier.getFullName()).append("，")
                                .append("中奖概率：").append(oldAward.getProbability()).append("，")
                                .append("奖项：").append(oldAward.getAwardName())
                                .append("最大产品数量：").append(oldAward.getMaxAwards()).append("，")
                                .append("每日最大中奖次数：").append(oldAward.getMaxAwardsDaily()).append("，")
                                .append("\n");
                    } else if (2 == oldAward.getType()) {
                        sb.append("产品；")
                                .append("产品名称：").append(oldAward.getProduct()).append("，")
                                .append("图片：").append(oldAward.getProductImg()).append("，")
                                .append("中奖概率：").append(oldAward.getProbability()).append("，")
                                .append("奖项：").append(oldAward.getAwardName())
                                .append("最大产品数量：").append(oldAward.getMaxAwards()).append("，")
                                .append("每日最大中奖次数：").append(oldAward.getMaxAwardsDaily()).append("，")
                                .append("\n");
                    }
                }
            } else {
                sb.append("空");
            }
            sb.append("更改为：\n");
            if (!CollectionUtils.isEmpty(awards)) {
                for (WeeklyActivityAwardsParam award : awards) {
                    if (1 == award.getType()) {
                        PointSupplier pointSupplier = pointSupplierMapper.selectByPrimaryKey(award.getSupplierId());
                        sb.append("积分；")
                                .append("积分额度：").append(award.getPoints()).append("，")
                                .append("积分供应商：").append(pointSupplier.getFullName()).append("，")
                                .append("中奖概率：").append(award.getProbability()).append("，")
                                .append("奖项：").append(award.getAwardName())
                                .append("最大产品数量：").append(award.getMaxAwards()).append("，")
                                .append("每日最大中奖次数：").append(award.getMaxAwardsDaily()).append("，")
                                .append("\n");
                    } else if (2 == award.getType()) {
                        sb.append("产品：").append(award.getProduct()).append("，")
                                .append("图片：").append(award.getProductImg()).append("，")
                                .append("中奖概率：").append(award.getProbability()).append("，")
                                .append("奖项：").append(award.getAwardName())
                                .append("最大产品数量：").append(award.getMaxAwards()).append("，")
                                .append("每日最大中奖次数：").append(award.getMaxAwardsDaily()).append("，")
                                .append("\n");
                    }
                }
            } else {
                sb.append("空");
            }
        }
        return sb.toString();
    }

    private boolean isRankActivityAwardsEqual(List<MiniProgramActivityRankAward> oldAwards, List<RankActivityAwardsParam> awards) {
        if (CollectionUtils.isEmpty(oldAwards) && CollectionUtils.isEmpty(awards)) {
            return false;
        } else if (CollectionUtils.isEmpty(oldAwards)) {
            return false;
        } else if (CollectionUtils.isEmpty(awards)) {
            return false;
        } else {
            if (oldAwards.size() != awards.size()) {
                return false;
            } else {
                oldAwards.sort(Comparator.comparing(MiniProgramActivityRankAward::getAwardName));
                awards.sort(Comparator.comparing(RankActivityAwardsParam::getAwardName));
                int size = oldAwards.size();
                for (int i = 0; i < size; i++) {
                    MiniProgramActivityRankAward oldAward = oldAwards.get(i);
                    RankActivityAwardsParam award = awards.get(i);
                    if (!ObjectUtils.nullSafeEquals(oldAward.getAwardName(), award.getAwardName())) {
                        return false;
                    }
                    if (!ObjectUtils.nullSafeEquals(oldAward.getRankingFrom(), award.getRankingFrom())) {
                        return false;
                    }
                    if (!ObjectUtils.nullSafeEquals(oldAward.getRankingTo(), award.getRankingTo())) {
                        return false;
                    }
                    if (!ObjectUtils.nullSafeEquals(oldAward.getSupplierId(), award.getSupplierId())) {
                        return false;
                    }
                    if (!ObjectUtils.nullSafeEquals(oldAward.getPoints(), award.getPoints())) {
                        return false;
                    }
                    if (!ObjectUtils.nullSafeEquals(oldAward.getType(), award.getType())) {
                        return false;
                    }
                    if (!ObjectUtils.nullSafeEquals(oldAward.getProductImg(), award.getProductImg())) {
                        return false;
                    }
                    if (!ObjectUtils.nullSafeEquals(oldAward.getProduct(), award.getProduct())) {
                        return false;
                    }
                }
                return true;
            }
        }
    }

    private boolean isWeeklyAwardsEqual(List<MiniProgramActivityWeeklyFunAward> oldAwards, List<WeeklyActivityAwardsParam> awards) {
        if (CollectionUtils.isEmpty(oldAwards) && CollectionUtils.isEmpty(awards)) {
            return false;
        } else if (CollectionUtils.isEmpty(oldAwards)) {
            return false;
        } else if (CollectionUtils.isEmpty(awards)) {
            return false;
        } else {
            if (oldAwards.size() != awards.size()) {
                return false;
            } else {
                oldAwards.sort(Comparator.comparing(MiniProgramActivityWeeklyFunAward::getAwardName));
                awards.sort(Comparator.comparing(WeeklyActivityAwardsParam::getAwardName));
                int size = oldAwards.size();
                for (int i = 0; i < size; i++) {
                    MiniProgramActivityWeeklyFunAward oldAward = oldAwards.get(i);
                    WeeklyActivityAwardsParam award = awards.get(i);
                    if (!ObjectUtils.nullSafeEquals(oldAward.getAwardName(), award.getAwardName())) {
                        return false;
                    }
                    if (!ObjectUtils.nullSafeEquals(oldAward.getMaxAwards(), award.getMaxAwards())) {
                        return false;
                    }
                    if (!ObjectUtils.nullSafeEquals(oldAward.getMaxAwardsDaily(), award.getMaxAwardsDaily())) {
                        return false;
                    }
                    if (!ObjectUtils.nullSafeEquals(oldAward.getSupplierId(), award.getSupplierId())) {
                        return false;
                    }
                    if (!ObjectUtils.nullSafeEquals(oldAward.getPoints(), award.getPoints())) {
                        return false;
                    }
                    if (!ObjectUtils.nullSafeEquals(oldAward.getType(), award.getType())) {
                        return false;
                    }
                    if (!ObjectUtils.nullSafeEquals(oldAward.getProductImg(), award.getProductImg())) {
                        return false;
                    }
                    if (!ObjectUtils.nullSafeEquals(oldAward.getProduct(), award.getProduct())) {
                        return false;
                    }
                    if (!ObjectUtils.nullSafeEquals(oldAward.getProbability(), award.getProbability())) {
                        return false;
                    }
                }
                return true;
            }
        }
    }

    @Override
    @DS("query")
    public List<PageActivityVO> searchMiniActivity(String keyWord, String provinceCode) {
        //为全国的时候不限制
        if (StringUtils.equals("000", provinceCode)) {
            provinceCode = null;
        }
        return miniProgramActivityMapperExt.searchMiniActivity(keyWord, provinceCode);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    @DS("save")
    public void enrollRank(ActivityEnrollParam enrollParam, LoginIfo4Redis loginIfo4Redis) {
        MiniProgramActivity activity = miniProgramActivityMapper.selectByPrimaryKey(enrollParam.getActivityId());
        if (activity == null || activity.getIsDelete() == 1) {
            throw new BusinessException(PARAM_ERROR, "活动不存在");
        }
        if (activity.getActivityType() != 1) {
            throw new BusinessException(PARAM_ERROR, "不是排名赛");
        }

        UserMiniProgram userMiniProgram = userMiniProgramMapper.selectByPrimaryKey(loginIfo4Redis.getUserId());
        if (userMiniProgram == null || StringUtils.isEmpty(loginIfo4Redis.getMallUserId())) {
            throw new BusinessException(PARAM_ERROR, "用户异常");
        }

        // 判断是否默认参加
        List<MiniProgramActivityRank> activityRanks = miniProgramActivityRankMapper.selectByExample(
                new MiniProgramActivityRankExample().createCriteria()
                        .andActivityIdEqualTo(activity.getId())
                        .example()
        );
        if (!CollectionUtils.isEmpty(activityRanks)) {
            MiniProgramActivityRank activityRank = activityRanks.get(0);
            Boolean joinDefault = activityRank.getJoinDefault();
            joinDefault = joinDefault != null ? joinDefault : false;
            log.info("默认" + (joinDefault ? "参加" : "不参加") + "，排位赛活动id：{}，名称：{}", activity.getId(), activity.getName());
            if (joinDefault) {
                throw new BusinessException(PARAM_ERROR, "排名赛已开启默认参加，不需要手动报名");
            }
        } else {
            throw new BusinessException(PARAM_ERROR, "排位赛不存在，活动id：" + activity.getId() + "，名称：" + activity.getName());
        }
        log.info("报名参加排位赛的用户");
        // 根据活动对象和活动身份筛选用户
        List<String> roleTypes = new ArrayList<>();
        Integer target = activity.getTarget();
        if ((target & TARGET_MASK_MANAGER) != 0) {
            roleTypes.add("4");
        }
        if ((target & TARGET_MASK_DISTRIBUTOR) != 0) {
            roleTypes.add("1");
            roleTypes.add("2");
        }
        if ((target & TARGET_MASK_CHANNEL) != 0) {
            roleTypes.add("3");
        }

        if (!roleTypes.contains(userMiniProgram.getRoleType())) {
            throw new BusinessException(PARAM_ERROR, "当前用户不能参与排名赛：" + activity.getName());
        }
        List<MiniProgramActivityRegion> regions = miniProgramActivityRegionMapper.selectByExample(
                new MiniProgramActivityRegionExample().createCriteria()
                        .andActivityIdEqualTo(activity.getId()).example()
        );
        List<RegionParam> regionParams = null;
        if (!CollectionUtils.isEmpty(regions)) {
            regionParams = regions.stream().map(region -> {
                RegionParam regionParam = new RegionParam();
                regionParam.setProvinceCode(region.getProvinceCode());
                regionParam.setCityCode(region.getCityCode());
                return regionParam;
            }).collect(Collectors.toList());


        }
        SelectActivityUsersParam param = new SelectActivityUsersParam();
//        List<MiniProgramActivityOfferingClass> miniProgramActivityOfferingClasses = miniProgramActivityOfferingClassMapper
//                .selectByExample(new MiniProgramActivityOfferingClassExample().createCriteria().andActivityIdEqualTo(activity.getId()).example());
//        if (!CollectionUtils.isEmpty(miniProgramActivityOfferingClasses)) {
//            List<String> offeringClasses = miniProgramActivityOfferingClasses.stream().map(MiniProgramActivityOfferingClass::getSpuOfferingClass).collect(Collectors.toList());
//            param.setOfferingClasses(offeringClasses);
//        }
//        List<MiniProgramActivitySpuCode> miniProgramActivitySpuCodes = miniProgramActivitySpuCodeMapper
//                .selectByExample(new MiniProgramActivitySpuCodeExample().createCriteria().andActivityIdEqualTo(activity.getId()).example());
//        if (!CollectionUtils.isEmpty(miniProgramActivitySpuCodes)) {
//            List<String> spuCodes = miniProgramActivitySpuCodes.stream().map(MiniProgramActivitySpuCode::getSpuOfferingCode).collect(Collectors.toList());
//            param.setSpuCodes(spuCodes);
//        }
        param.setRoleTypes(roleTypes);
        param.setRegions(regionParams);
        List<String> userIds = new ArrayList<>();
        userIds.add(userMiniProgram.getUserId());
        param.setUserIds(userIds);

        List<MiniProgramActivityUser> users = miniProgramActivityMapperExt.selectActivityUsers(param);
        log.info("users: {}", JSON.toJSONString(users));
        //users = users.stream().filter(Objects::nonNull).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(users)) {
            for (MiniProgramActivityUser user : users) {
                user.setId(BaseServiceUtils.getId());
                user.setActivityId(activity.getId());
            }

            miniProgramActivityUserMapper.batchInsert(users);
            log.info("用户：{}报名排位赛{}成功", userMiniProgram.getName(), activity.getName());
        } else {
            throw new BusinessException(PARAM_ERROR, "当前用户不能报名，活动id：" + activity.getId() + "，名称：" + activity.getName());
        }
//        TransactionUtil.afterCommit(() -> syncLoadUser2Redis(activity.getId(), userMiniProgram.getUserId()));
    }

    private boolean isCanJoinForRank(String activityId, String userId) {
        MiniProgramActivity activity = miniProgramActivityMapper.selectByPrimaryKey(activityId);
        UserMiniProgram userMiniProgram = userMiniProgramMapper.selectByPrimaryKey(userId);
        // 根据活动对象和活动身份筛选用户
        List<String> roleTypes = new ArrayList<>();
        Integer target = activity.getTarget();
        if ((target & TARGET_MASK_MANAGER) != 0) {
            roleTypes.add("4");
        }
        if ((target & TARGET_MASK_DISTRIBUTOR) != 0) {
            roleTypes.add("1");
            roleTypes.add("2");
        }
        if ((target & TARGET_MASK_CHANNEL) != 0) {
            roleTypes.add("3");
        }

        if (!roleTypes.contains(userMiniProgram.getRoleType())) {
            return false;
        }
        List<MiniProgramActivityRegion> regions = miniProgramActivityRegionMapper.selectByExample(
                new MiniProgramActivityRegionExample().createCriteria()
                        .andActivityIdEqualTo(activity.getId()).example()
        );
        List<RegionParam> regionParams = null;
        if (!CollectionUtils.isEmpty(regions)) {
            regionParams = regions.stream().map(region -> {
                RegionParam regionParam = new RegionParam();
                regionParam.setProvinceCode(region.getProvinceCode());
                regionParam.setCityCode(region.getCityCode());
                return regionParam;
            }).collect(Collectors.toList());
        }
        SelectActivityUsersParam param = new SelectActivityUsersParam();
//        List<MiniProgramActivityOfferingClass> miniProgramActivityOfferingClasses = miniProgramActivityOfferingClassMapper
//                .selectByExample(new MiniProgramActivityOfferingClassExample().createCriteria().andActivityIdEqualTo(activity.getId()).example());
//        if (!CollectionUtils.isEmpty(miniProgramActivityOfferingClasses)) {
//            List<String> offeringClasses = miniProgramActivityOfferingClasses.stream().map(MiniProgramActivityOfferingClass::getSpuOfferingClass).collect(Collectors.toList());
//            param.setOfferingClasses(offeringClasses);
//        }
//        List<MiniProgramActivitySpuCode> miniProgramActivitySpuCodes = miniProgramActivitySpuCodeMapper
//                .selectByExample(new MiniProgramActivitySpuCodeExample().createCriteria().andActivityIdEqualTo(activity.getId()).example());
//        if (!CollectionUtils.isEmpty(miniProgramActivitySpuCodes)) {
//            List<String> spuCodes = miniProgramActivitySpuCodes.stream().map(MiniProgramActivitySpuCode::getSpuOfferingCode).collect(Collectors.toList());
//            param.setSpuCodes(spuCodes);
//        }
        param.setRoleTypes(roleTypes);
        param.setRegions(regionParams);
        List<String> userIds = new ArrayList<>();
        if (userMiniProgram.getUserId() != null) {
            userIds.add(userMiniProgram.getUserId());
        }
        param.setUserIds(userIds);


        List<MiniProgramActivityUser> users = miniProgramActivityMapperExt.selectActivityUsers(param);
        return !CollectionUtils.isEmpty(users);
    }

    /**
     * 添加活动进缓存(异步)
     */
    private void asyncLoadActivity2Redis(String activityId) {
        //异步加载活动进缓存
        executorService.execute(() -> syncLoadActivity2Redis(activityId));
    }

    /**
     * 添加活动进缓存(同步)
     */
    @Override
    public void syncLoadActivity2Redis(String activityId) {
        String key = Constant.REDIS_KEY_MINI_ACTIVITY_ID + activityId;
        String lock = RedisLockConstant.LOCK_ACTIVITY_ID + activityId;
        redisUtil.smartLock(lock, () -> {
            PageActivityVO activityVO = new PageActivityVO();
            MiniProgramActivity activity = miniProgramActivityMapper.selectByPrimaryKey(activityId);
            BeanUtils.copyProperties(activity, activityVO);
            activityVO.setParticipatedUserIds(miniProgramActivityUserMapper.selectByExample(
                            new MiniProgramActivityUserExample().createCriteria().andActivityIdEqualTo(activityId).example())
                    .stream().map(MiniProgramActivityUser::getUserId).collect(Collectors.toList()));
            redisTemplate.opsForValue().set(key, activityVO);
            return null;
        });
    }

    /**
     * 删除活动缓存(同步)
     */
    public void syncDelActivity2Redis(String activityId) {

        String key = Constant.REDIS_KEY_MINI_ACTIVITY_ID + activityId;
        String lock = RedisLockConstant.LOCK_ACTIVITY_ID + activityId;
        redisUtil.smartLock(lock, () -> {
            redisTemplate.delete(key);
            return null;
        });
    }

    /**
     * 删除活动缓存(异步)
     */
    public void delActivity2Redis(String activityId) {
        executorService.execute(() -> syncDelActivity2Redis(activityId));
    }

    /**
     * 删除活动区域缓存(异步)
     */
    public void delActivityRegion2Redis(String activityId) {
        List<MiniProgramActivityRegion> oldRegion = miniProgramActivityRegionMapper.selectByExample(
                new MiniProgramActivityRegionExample().createCriteria()
                        .andActivityIdEqualTo(activityId).example());
        syncUpdateRegionActivity2Redis(activityId, new ArrayList<>(), oldRegion);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(String id) {
        MiniProgramActivity activity = miniProgramActivityMapper.selectByPrimaryKey(id);
        if (activity == null || activity.getIsDelete() == 1) {
            throw new BusinessException(PARAM_ERROR, "活动不存在");
        }
        Set<Integer> validStatuses = new HashSet<>(Arrays.asList(
                ActivityStatusEnum.DRAFT.getStatus(),
                ActivityStatusEnum.FINISH.getStatus(),
                ActivityStatusEnum.OFFLINE.getStatus()
        ));
        if (!validStatuses.contains(activity.getStatus())) {
            throw new BusinessException(StatusContant.MINI_PROGRAM_ACTIVITY_DEACTIVATE_STATUS_WRONG, "非待发布,或已下线状态，不能删除");
        }
        activity.setIsDelete(1);
        activity.setUpdateTime(new Date());
        miniProgramActivityMapper.updateByPrimaryKeySelective(activity);
        delActivity2Redis(id);
        delActivityRegion2Redis(id);
        StringBuilder sb = new StringBuilder();
        sb.append("【删除活动】").append("/n").append("活动主题").append(activity.getName());
        logService.recordOperateLog(ModuleEnum.MINI_PROGRAM.code, MiniProgramOperateEnum.ACTIVITY.code,
                sb.toString(), null, 0, LogResultEnum.LOG_SUCESS.code, null);
    }

    /**
     * 添加参加用户的userId进活动缓存
     */
    private void asyncLoadUser2Redis(String activityId, String userId) {
        //异步添加用户进活动缓存
        executorService.execute(() -> syncLoadUser2Redis(activityId, userId));
    }

    /**
     * 添加参加用户的userId进活动缓存(同步)
     */
    @Override
    public void syncLoadUser2Redis(String activityId, String userId) {
        String key = Constant.REDIS_KEY_MINI_ACTIVITY_ID + activityId;
        String lock = RedisLockConstant.LOCK_ACTIVITY_ID + activityId;
        redisUtil.smartLock(lock, () -> {
            PageActivityVO activityVO = (PageActivityVO) redisTemplate.opsForValue().get(key);
            if (activityVO != null) {
                if (CollectionUtils.isEmpty(activityVO.getParticipatedUserIds())) {
                    activityVO.setParticipatedUserIds(new ArrayList<>());
                }
                activityVO.getParticipatedUserIds().add(userId);
                redisTemplate.opsForValue().set(key, activityVO);
            }
            return null;
        });
    }

    /**
     * 加活动添加对应区域
     */
    private void asyncUpdateRegionActivity2Redis(String activityId, List<MiniProgramActivityRegion> newRegion, List<MiniProgramActivityRegion> oldRegion) {
        //异步加载区域的活动
        executorService.execute(() -> syncUpdateRegionActivity2Redis(activityId, newRegion, oldRegion));
    }

    /**
     * 加活动添加对应区域(同步)
     */
    public void syncUpdateRegionActivity2Redis(String activityId, List<MiniProgramActivityRegion> newRegion, List<MiniProgramActivityRegion> oldRegion) {
        List<MiniProgramActivityRegion> added = new ArrayList<>(CollectionUtils.subtract(newRegion, oldRegion));
        if (CollectionUtils.isNotEmpty(added)) {
            //新增的区域
            added.forEach(region -> {
                String lock = RedisLockConstant.LOCK_ACTIVITY_REGION + region.getProvinceCode() + ":" + region.getCityCode();
                String key = Constant.REDIS_KEY_MINI_ACTIVITY_REGION + region.getProvinceCode() + ":" + region.getCityCode();
                redisUtil.smartLock(lock, () -> {
                    List<String> activityIds = (List<String>) redisTemplate.opsForValue().get(key);
                    if (CollectionUtils.isEmpty(activityIds)) {
                        activityIds = new ArrayList<>();
                    }
                    activityIds.add(activityId);
                    redisTemplate.opsForValue().set(key, activityIds);
                    return null;
                });
            });
        }

        List<MiniProgramActivityRegion> decred = new ArrayList<>(CollectionUtils.subtract(oldRegion, newRegion));
        if (CollectionUtils.isNotEmpty(decred)) {
            //减少的区域
            added.forEach(region -> {
                String lock = RedisLockConstant.LOCK_ACTIVITY_REGION + region.getProvinceCode() + ":" + region.getCityCode();
                String key = Constant.REDIS_KEY_MINI_ACTIVITY_REGION + region.getProvinceCode() + ":" + region.getCityCode();
                redisUtil.smartLock(lock, () -> {
                    List<String> activityIds = (List<String>) redisTemplate.opsForValue().get(key);
                    if (CollectionUtils.isNotEmpty(activityIds)) {
                        activityIds.remove(activityId);
                        redisTemplate.opsForValue().set(key, activityIds);
                    }
                    return null;
                });
            });
        }

    }


    @Override
    @DS("query")
    public void loadAllActivity2Redis() {
        List<MiniProgramActivity> activities = miniProgramActivityMapper.selectByExample(new MiniProgramActivityExample());
        List<MiniProgramActivityRegion> regions = miniProgramActivityRegionMapper.selectByExample(new MiniProgramActivityRegionExample());
        List<MiniProgramActivityUser> users = miniProgramActivityUserMapper.selectByExample(new MiniProgramActivityUserExample());
        Map<String, List<MiniProgramActivityRegion>> regionMap = regions.stream().collect(Collectors.groupingBy(x -> x.getProvinceCode() + ":" + x.getCityCode()));
        Map<String, List<MiniProgramActivityUser>> userMap = users.stream().collect(Collectors.groupingBy(MiniProgramActivityUser::getActivityId));
        Map<String, PageActivityVO> activityRedisMap = new LinkedHashMap<>();
        Map<String, List<String>> regionRedisMap = new LinkedHashMap<>();
        Map<String, List<String>> userRedisMap = new LinkedHashMap<>();
        activities.forEach(x -> {
            PageActivityVO vo = new PageActivityVO();
            BeanUtils.copyProperties(x, vo);
//            List<MiniProgramActivityUser> activityUsers = userMap.get(x.getId());
//            if (CollectionUtils.isNotEmpty(activityUsers)) {
//                vo.setParticipatedUserIds(activityUsers.stream().map(MiniProgramActivityUser::getUserId).distinct().collect(Collectors.toList()));
//            }
            activityRedisMap.put(Constant.REDIS_KEY_MINI_ACTIVITY_ID + x.getId(), vo);
        });
        regionMap.forEach((key, list) -> {
            regionRedisMap.put(Constant.REDIS_KEY_MINI_ACTIVITY_REGION + key,
                    list.stream().map(MiniProgramActivityRegion::getActivityId).distinct().collect(Collectors.toList()));
        });

        userMap.forEach((key, list) -> {
            userRedisMap.put(Constant.REDIS_KEY_MINI_ACTIVITY_USER + key,
                    list.stream().map(MiniProgramActivityUser::getUserId).distinct().collect(Collectors.toList()));
        });

        if (!activityRedisMap.isEmpty()) {
            redisTemplate.opsForValue().multiSet(activityRedisMap);
        }
        if (!regionRedisMap.isEmpty()) {
            redisTemplate.opsForValue().multiSet(regionRedisMap);
        }
    }

    @KafkaListener(topics = {"supply_chain.mini_program_activity"})
    public void listenerActivity(ConsumerRecord<String, byte[]> record) {
        try {
            log.info("小程序活动kafka消息：{}", JSON.toJSONString(new String(record.value())));
            //更新小程序活动缓存
            JSONObject jsonObject = JSON.parseObject(new String(record.value()));
            String op = jsonObject.getString("op");
            if (StringUtils.equals(op, "u")) {
                //小程序只需要处理更新操作的
                MiniProgramActivity activity = jsonObject.getObject("after", MiniProgramActivity.class);
                if (activity.getIsDelete() != null && activity.getIsDelete() == 1) {
                    //活动删除，清除缓存
                    redisCacheUtil.delete(Constant.REDIS_KEY_MINI_ACTIVITY_ID + activity.getId());
                    redisCacheUtil.delete(Constant.REDIS_KEY_MINI_ACTIVITY_USER + activity.getId());
                    List<MiniProgramActivityRegion> regions = miniProgramActivityRegionMapper.selectByExample(new MiniProgramActivityRegionExample()
                            .createCriteria().andActivityIdEqualTo(activity.getId()).example());
                    regions.forEach(x -> {
                        String key = Constant.REDIS_KEY_MINI_ACTIVITY_REGION + x.getProvinceCode() + ":" + x.getCityCode();
                        String lock = RedisLockConstant.LOCK_ACTIVITY_REGION + x.getProvinceCode() + ":" + x.getCityCode();
                        redisUtil.smartLock(lock, () -> {
                            List<String> activityIds = (List<String>) redisTemplate.opsForValue().get(key);
                            if (CollectionUtils.isNotEmpty(activityIds)) {
                                activityIds.remove(x.getActivityId());
                                redisTemplate.opsForValue().set(key, activityIds);
                            }
                            return null;
                        });
                    });
                } else {
                    //更新活动
                    String key = Constant.REDIS_KEY_MINI_ACTIVITY_ID + activity.getId();
                    String lock = RedisLockConstant.LOCK_ACTIVITY_ID + activity.getId();
                    redisUtil.smartLock(lock, () -> {
                        PageActivityVO activityVO = redisCacheUtil.get(key);
                        if (activityVO == null) {
                            activityVO = new PageActivityVO();
                        }
                        BeanUtils.copyProperties(activity, activityVO);
                        redisTemplate.opsForValue().set(key, activityVO);
                        return null;
                    });
                }

            }
        } catch (Exception e) {
            log.info("小程序活动数据解析失败【record】:{}", new String(record.value()), e);
        }
    }

    @KafkaListener(topics = {"supply_chain.mini_program_activity_user"})
    public void listenerActivityUser(ConsumerRecord<String, byte[]> record) {
        try {
            log.info("小程序活动用户参与kafka消息：{}", JSON.toJSONString(new String(record.value())));
            //更新小程序活动参与用户缓存
            JSONObject jsonObject = JSON.parseObject(new String(record.value()));
            String op = jsonObject.getString("op");
            if (StringUtils.equals(op, "c")) {
                //小程序活动用户增加
                MiniProgramActivityUser activityUser = jsonObject.getObject("after", MiniProgramActivityUser.class);
                String key = Constant.REDIS_KEY_MINI_ACTIVITY_USER + activityUser.getActivityId();
                String lock = RedisLockConstant.LOCK_ACTIVITY_USER + activityUser.getActivityId();
                redisUtil.smartLock(lock, () -> {
                    List<String> userIds = (List<String>) redisTemplate.opsForValue().get(key);
                    if (userIds == null) {
                        userIds = new ArrayList<>();
                    }
                    userIds.add(activityUser.getUserId());
                    redisTemplate.opsForValue().set(key, userIds);
                    return null;
                });

            } else if (StringUtils.equals(op, "d")) {
                //小程序活动用户减少
                MiniProgramActivityUser activityUser = jsonObject.getObject("before", MiniProgramActivityUser.class);
                String key = Constant.REDIS_KEY_MINI_ACTIVITY_USER + activityUser.getActivityId();
                String lock = RedisLockConstant.LOCK_ACTIVITY_USER + activityUser.getActivityId();
                redisUtil.smartLock(lock, () -> {
                    List<String> userIds = (List<String>) redisTemplate.opsForValue().get(key);
                    if (CollectionUtils.isNotEmpty(userIds)) {
                        userIds.remove(activityUser.getUserId());
                        redisTemplate.opsForValue().set(key, userIds);
                    }
                    return null;
                });
            }
        } catch (Exception e) {
            log.info("小程序活动参与用户数据解析失败【record】:{}", new String(record.value()), e);
        }
    }

    @KafkaListener(topics = {"supply_chain.mini_program_activity_region"})
    public void listenerActivityRegion(ConsumerRecord<String, byte[]> record) {
        try {
            log.info("小程序活动发布区域kafka消息：{}", JSON.toJSONString(new String(record.value())));
            //更新小程序活动参与用户缓存
            JSONObject jsonObject = JSON.parseObject(new String(record.value()));
            String op = jsonObject.getString("op");
            if (StringUtils.equals(op, "c")) {
                //小程序活动用户增加
                MiniProgramActivityRegion activityRegion = jsonObject.getObject("after", MiniProgramActivityRegion.class);
                String key = Constant.REDIS_KEY_MINI_ACTIVITY_REGION + activityRegion.getProvinceCode() + ":" + activityRegion.getCityCode();
                String lock = RedisLockConstant.LOCK_ACTIVITY_REGION + activityRegion.getProvinceCode() + ":" + activityRegion.getCityCode();
                redisUtil.smartLock(lock, () -> {
                    List<String> activityIds = (List<String>) redisTemplate.opsForValue().get(key);
                    if (activityIds == null) {
                        activityIds = new ArrayList<>();
                    }
                    activityIds.add(activityRegion.getActivityId());
                    redisTemplate.opsForValue().set(key, activityIds);
                    return null;
                });

            } else if (StringUtils.equals(op, "d")) {
                //小程序活动用户减少
                MiniProgramActivityRegion activityRegion = jsonObject.getObject("before", MiniProgramActivityRegion.class);
                String key = Constant.REDIS_KEY_MINI_ACTIVITY_REGION + activityRegion.getProvinceCode() + ":" + activityRegion.getCityCode();
                String lock = RedisLockConstant.LOCK_ACTIVITY_REGION + activityRegion.getProvinceCode() + ":" + activityRegion.getCityCode();
                redisUtil.smartLock(lock, () -> {
                    List<String> activityIds = (List<String>) redisTemplate.opsForValue().get(key);
                    if (CollectionUtils.isNotEmpty(activityIds)) {
                        activityIds.remove(activityRegion.getActivityId());
                        redisTemplate.opsForValue().set(key, activityIds);
                    }
                    return null;
                });
            }
        } catch (Exception e) {
            log.info("小程序活动发布区域数据解析失败【record】:{}", new String(record.value()), e);
        }
    }
}
