package com.chinamobile.retail.service;

import com.chinamobile.iot.sc.common.Constant;
import com.chinamobile.iot.sc.mode.LoginIfo4Redis;
import com.chinamobile.iot.sc.mode.PageData;
import com.chinamobile.retail.pojo.param.miniprogram.*;
import com.chinamobile.retail.pojo.vo.miniprogram.HomeVO;
import com.chinamobile.retail.pojo.vo.miniprogram.SkuCoreComponentVO;
import com.chinamobile.retail.pojo.vo.miniprogram.SpuCoreComponentVO;
import org.springframework.web.bind.annotation.RequestAttribute;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/2/25 09:43
 * @description TODO
 */
public interface ICoreComponentService {

    SpuCoreComponentVO getCoreComponentDetail(String spuCode, LoginIfo4Redis loginIfo4Redis);

    PageData<SpuCoreComponentVO> pageCoreComponentList(PageCoreComponentListParam param, LoginIfo4Redis loginIfo4Redis);

    void create(SpuCoreComponentParam param, String userId);
    void edit(SpuCoreComponentParam param, String userId);

    void audit(InfoAuditParam param, LoginIfo4Redis loginIfo4Redis);

    void offline(InfoOfflineParam param, LoginIfo4Redis loginIfo4Redis);

    void publish(HomePublishParam param, LoginIfo4Redis loginIfo4Redis);

    void delete(InfoOfflineParam param,LoginIfo4Redis loginIfo4Redis);

    List<SpuCoreComponentVO> searchSpu(String keyword, LoginIfo4Redis loginIfo4Redis);
    List<SkuCoreComponentVO> searchSku(String keyword, LoginIfo4Redis loginIfo4Redis);


}
