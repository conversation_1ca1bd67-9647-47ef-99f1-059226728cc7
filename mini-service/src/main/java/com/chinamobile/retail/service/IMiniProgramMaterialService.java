package com.chinamobile.retail.service;

import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.retail.pojo.param.miniprogram.*;
import com.chinamobile.retail.pojo.vo.MaterialListVO;
import com.chinamobile.retail.pojo.vo.MaterialVO;

import java.util.List;

public interface IMiniProgramMaterialService {

    /**
     * 搜索查询素材或文件夹
     * @param param
     * @return
     */
    BaseAnswer<List<MaterialVO>> searchMaterials(SearchMaterialParam param);

    /**
     * 查询素材列表
     * @param param
     * @return
     */
    BaseAnswer<List<MaterialListVO>> searchMaterialList(SearchMaterialListParam param);

    /**
     * 新建文件或文件夹
     * @param param
     */
    BaseAnswer createFolder(CreateFolderParam param);

    /**
     * 重命名文件夹或素材
     * @param param
     */
    BaseAnswer renameMaterial(RenameMaterialParam param);

    /**
     * 删除文件夹或素材
     * @param param
     */
    BaseAnswer deleteMaterial(DeleteMaterialParam param);

    /**
     * 批量删除文件夹或素材
     * @param param
     */
    BaseAnswer batchDeleteMaterial(BatchDeleteMaterialParam param);
}