package com.chinamobile.retail.service;

import com.chinamobile.iot.sc.mode.LoginIfo4Redis;
import com.chinamobile.iot.sc.mode.PageData;
import com.chinamobile.retail.pojo.dto.SceneDirectoryDTO;
import com.chinamobile.retail.pojo.param.miniprogram.*;
import com.chinamobile.retail.pojo.vo.miniprogram.*;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/7/16 09:43
 * @description TODO
 */
public interface IMiniSceneService {

    /*********************************** 客户端接口 START ***********************************/
    /**
     * 小程序前端获取场景目录
     */
    List<SceneDirectoryVO> listSceneDirectory();

    /**
     * 小程序前端分页查询场景列表
     */
    PageData<SceneFrontendItemVO> pageSceneFrontend(PageSceneFrontendParam param);

    /**
     * 小程序前端获取场景详情
     */
    SceneDetailFrontendVO getSceneDetailFrontend(SceneDetailFrontendParam param);

    /**
     * 小程序前端获取场景需求模板
     */
    SceneRequirementTemplateFrontendVO getSceneRequirementTemplate(String sceneId);

    /**
     * 小程序提交场景方案设计
     */
    void submitSceneRequirement(SceneRequirementSubmitParam param, LoginIfo4Redis loginIfo4Redis);
    /*********************************** 客户端接口 STOP ***********************************/

    SceneVO getSceneDetail(String homeId, LoginIfo4Redis loginIfo4Redis);

    PageData<SceneVO> pageSceneList(PageSceneListParam param, LoginIfo4Redis loginIfo4Redis);

//    SceneVO getSceneMini(String userId);

    void create(SceneParam param, String userId);
    void edit(SceneParam param, String userId);

    void delete(InfoOfflineParam param, LoginIfo4Redis loginIfo4Redis);

    void audit(InfoAuditParam param, LoginIfo4Redis loginIfo4Redis);

    void offline(InfoOfflineParam param, LoginIfo4Redis loginIfo4Redis);

    void publish(HomePublishParam param, LoginIfo4Redis loginIfo4Redis);

    List<SceneDirectoryDTO> getSceneDirectoryList(LoginIfo4Redis loginIfo4Redis);

    void createDir(SceneDirectoryParam param,String userId);
    void editDir(SceneDirectoryParam param,String userId);

    void deleteDir(InfoOfflineParam param, LoginIfo4Redis loginIfo4Redis);

    void sortDir(List<SortNavigationParam> params);

    PageData<SceneRequirementVO> pageRequirementList(PageRequirementListParam param, LoginIfo4Redis loginIfo4Redis);

    SceneRequirementVO getRequirementDetail(String requirementId, LoginIfo4Redis loginIfo4Redis);

    void deleteRequirement(InfoOfflineParam param, LoginIfo4Redis loginIfo4Redis);

    void dispatchRequirement(RequirementDispatchParam param,LoginIfo4Redis loginIfo4Redis);

    void rejectRequirement(InfoOfflineParam param, LoginIfo4Redis loginIfo4Redis);

    TemplateVO getTemplateDetail(String templateId, LoginIfo4Redis loginIfo4Redis);

    PageData<TemplateVO> pageTemplateList(PageTemplateListParam param, LoginIfo4Redis loginIfo4Redis);


    void createTemplate(TemplateParam param, String userId);
    void editTemplate(TemplateParam param, String userId);

    void deleteTemplate(InfoOfflineParam param, LoginIfo4Redis loginIfo4Redis);

    List<TemplateVO> searchTemplate(PageTemplateListParam param, LoginIfo4Redis loginIfo4Redis);

}
