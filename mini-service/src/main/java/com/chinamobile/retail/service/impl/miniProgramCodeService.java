package com.chinamobile.retail.service.impl;


import com.alibaba.fastjson.JSONObject;
import com.chinamobile.retail.config.WeixinConfig;
import com.chinamobile.retail.pojo.vo.miniprogram.WeixinShareUrlVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.Base64;

@Component
public class miniProgramCodeService {
    @Autowired
    private WeixinConfig weixinConfig;
    public static final String urlFormat = "https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=%s&secret=%s";

    public  void main(String[] args) {
        try {
            String appId = "your_app_id";
            String appSecret = "your_app_secret";
            String accessToken = getAccessToken();
            String scene = "your_scene";
            String page = "your_page";
            String width = "430";

            String url = ""+ accessToken;
            String params = "{\"scene\":\"" + scene + "\",\"page\":\"" + page + "\",\"width\":" + width + "}";
//            String result = doPost(url, params);

            // 处理返回结果
//            System.out.println(result);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public  String getAccessToken() throws Exception {
        String url = String.format(urlFormat,weixinConfig.getAppId(),weixinConfig.getAppSecret());
        String result = doGet(url);
        // 解析返回结果，获取access_token
        return result;
    }


    public  String doGet(String url) throws Exception {
        HttpURLConnection connection = (HttpURLConnection) new URL(url).openConnection();
        connection.setRequestMethod("GET");
        connection.setDoInput(true);
        connection.setDoOutput(false);
        connection.connect();

        BufferedReader reader = new BufferedReader(new InputStreamReader(connection.getInputStream()));
        StringBuilder sb = new StringBuilder();
        String line;
        while ((line = reader.readLine()) != null) {
            sb.append(line);
        }

        return sb.toString();
    }

    public static String doPost(String url, String params) throws Exception {
        HttpURLConnection connection = (HttpURLConnection) new URL(url).openConnection();
        connection.setRequestMethod("POST");
        connection.setDoInput(true);
        connection.setDoOutput(true);
        connection.getOutputStream().write(params.getBytes());
        connection.connect();

        BufferedReader reader = new BufferedReader(new InputStreamReader(connection.getInputStream()));

        StringBuilder sb = new StringBuilder();
        String line;
        while ((line = reader.readLine()) != null) {
            sb.append(line);
        }

        return sb.toString();
    }
    public static String fetchImageAsBase64(String imageUrl, String postData) {
        HttpURLConnection connection = null;
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        try {
            // 创建连接
            URL url = new URL(imageUrl);
            connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("POST");
            connection.setDoOutput(true); // 允许输出流

            // 写入请求参数
            try (OutputStream outputStream = connection.getOutputStream()) {
                outputStream.write(postData.getBytes());
                outputStream.flush();
            }

            // 检查响应状态码
            int responseCode = connection.getResponseCode();
            if (responseCode == HttpURLConnection.HTTP_OK) {
                // 读取图片数据
                try (InputStream inputStream = connection.getInputStream()) {
                    byte[] buffer = new byte[1024];
                    int bytesRead;
                    while ((bytesRead = inputStream.read(buffer)) != -1) {
                        baos.write(buffer, 0, bytesRead);
                    }
                }
               String content = baos.toString();
                if(content.contains("errcode")){
                    WeixinShareUrlVO weixinShareUrlVO = JSONObject.parseObject(content, WeixinShareUrlVO.class);
                    throw new Exception(weixinShareUrlVO.getErrmsg());
                }else{
                    byte[] imageBytes = baos.toByteArray();
                    // 将字节数组转换为 Base64 编码
                    String base64Image = Base64.getEncoder().encodeToString(imageBytes);
                    return base64Image;
                }


            } else {
                // 处理非 200 状态码的错误响应
                // 你可以读取错误流并生成相应的错误信息
                try (InputStream errorStream = connection.getErrorStream();
                     ByteArrayOutputStream errorBaos = new ByteArrayOutputStream()) {
                    if (errorStream != null) {
                        byte[] buffer = new byte[1024];
                        int bytesRead;
                        while ((bytesRead = errorStream.read(buffer)) != -1) {
                            errorBaos.write(buffer, 0, bytesRead);
                        }
                        String errorMessage = errorBaos.toString();
                        return "Error: " + responseCode + " - " + errorMessage;
                    } else {
                        return "Error: " + responseCode;
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            return "Error: " + e.getMessage();
        } finally {
            if (connection != null) {
                connection.disconnect();
            }
            try {
                baos.close();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }


}
