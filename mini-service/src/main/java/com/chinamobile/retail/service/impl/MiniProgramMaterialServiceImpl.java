package com.chinamobile.retail.service.impl;

import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.common.utils.BaseServiceUtils;
import com.chinamobile.iot.sc.enums.log.LogResultEnum;
import com.chinamobile.iot.sc.enums.log.MiniProgramOperateEnum;
import com.chinamobile.iot.sc.enums.log.ModuleEnum;
import com.chinamobile.iot.sc.exceptions.BusinessException;
import com.chinamobile.iot.sc.service.LogService;
import com.chinamobile.retail.dao.MiniProgramMaterialLibraryMapper;
import com.chinamobile.retail.pojo.entity.MiniProgramMaterialLibrary;
import com.chinamobile.retail.pojo.entity.MiniProgramMaterialLibraryExample;
import com.chinamobile.retail.pojo.param.miniprogram.*;
import com.chinamobile.retail.pojo.vo.MaterialListVO;
import com.chinamobile.retail.pojo.vo.MaterialVO;
import com.chinamobile.retail.service.IMiniProgramMaterialService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;

@Service
@Slf4j
public class MiniProgramMaterialServiceImpl implements IMiniProgramMaterialService {

    @Resource
    private MiniProgramMaterialLibraryMapper materialLibraryMapper;

    @Resource
    private LogService logService;

    @Override
    public BaseAnswer<List<MaterialListVO>> searchMaterialList(SearchMaterialListParam param) {
        MiniProgramMaterialLibraryExample example = new MiniProgramMaterialLibraryExample();
        MiniProgramMaterialLibraryExample.Criteria criteria = example.createCriteria();
        if (param.getParentId() != null) {
            criteria.andParentIdEqualTo(param.getParentId());
        }else{
            criteria.andParentIdIsNull();
        }
        if (param.getType() != null) {
            String[] types = param.getType().split(",");
            criteria.andTypeIn(Arrays.asList(types));
        }
        List<MiniProgramMaterialLibrary> materials = materialLibraryMapper.selectByExample(example);

        List<MaterialListVO> materialVOs = new ArrayList<>();
        for (MiniProgramMaterialLibrary material : materials) {
            MaterialListVO vo = new MaterialListVO();
            BeanUtils.copyProperties(material, vo);
            vo.setChildren(getChildren(material.getId()));
            materialVOs.add(vo);
        }

        return BaseAnswer.success(materialVOs);
    }

    private List<MaterialListVO> getChildren(String parentId) {
        MiniProgramMaterialLibraryExample example = new MiniProgramMaterialLibraryExample();
        example.createCriteria().andParentIdEqualTo(parentId);
        List<MiniProgramMaterialLibrary> childrenMaterials = materialLibraryMapper.selectByExample(example);

        List<MaterialListVO> childrenVOs = new ArrayList<>();
        for (MiniProgramMaterialLibrary childMaterial : childrenMaterials) {
            MaterialListVO vo = new MaterialListVO();
            BeanUtils.copyProperties(childMaterial, vo);
            vo.setChildren(getChildren(childMaterial.getId()));
            childrenVOs.add(vo);
        }

        return childrenVOs;
    }

    @Override
    public BaseAnswer<List<MaterialVO>> searchMaterials(SearchMaterialParam param) {
        MiniProgramMaterialLibraryExample example = new MiniProgramMaterialLibraryExample();
        MiniProgramMaterialLibraryExample.Criteria criteria = example.createCriteria();
        criteria.andNameLike("%" + param.getSearchText() + "%");
        if (param.getType() != null) {
            String[] types = param.getType().split(",");
            criteria.andTypeIn(Arrays.asList(types));
        }
        List<MiniProgramMaterialLibrary> materials = materialLibraryMapper.selectByExample(example);

        List<MaterialVO> materialVOs = new ArrayList<>();
        for (MiniProgramMaterialLibrary material : materials) {
            MaterialVO vo = new MaterialVO();
            BeanUtils.copyProperties(material, vo);
            materialVOs.add(vo);
        }

        // 排序：文件夹在前，素材在后；同类型按名称排序
        Collections.sort(materialVOs, new Comparator<MaterialVO>() {
            @Override
            public int compare(MaterialVO o1, MaterialVO o2) {
                if ("folder".equals(o1.getType()) && !"folder".equals(o2.getType())) {
                    return -1;
                } else if (!"folder".equals(o1.getType()) && "folder".equals(o2.getType())) {
                    return 1;
                } else {
                    return o1.getName().compareTo(o2.getName());
                }
            }
        });

        return BaseAnswer.success(materialVOs);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseAnswer createFolder(CreateFolderParam param) {
        // 校验同类型的数据不能重名
        MiniProgramMaterialLibraryExample example = new MiniProgramMaterialLibraryExample();
        MiniProgramMaterialLibraryExample.Criteria criteria = example.createCriteria();
        criteria.andTypeEqualTo(param.getType()).andNameEqualTo(param.getName());
        if (param.getParentId() != null) {
            criteria.andParentIdEqualTo(param.getParentId());
        }else{
            criteria.andParentIdIsNull();
        }

        if (!materialLibraryMapper.selectByExample(example).isEmpty()) {
            throw new BusinessException("500","同类型的文件夹或素材名称已存在");
        }

        MiniProgramMaterialLibrary material = new MiniProgramMaterialLibrary();
        material.setId(BaseServiceUtils.getId());
        material.setParentId(param.getParentId());
        material.setType(param.getType()); // 根据传入参数设置类型
        material.setName(param.getName());
        material.setCreateTime(new Date());
        material.setUpdateTime(new Date());
        material.setFileKey(param.getFileKey()); // 添加文件key
        material.setFileUrl(param.getFileUrl()); // 添加文件url
        material.setFileSize(param.getFileSize());
        materialLibraryMapper.insertSelective(material);

        String logContent = "folder".equals(param.getType()) ? 
            "【文件夹新建】\n文件夹名称：" + param.getName() : 
            "【素材新建】\n素材名称：" + param.getName();
        logService.recordOperateLog(ModuleEnum.MINI_PROGRAM.code,
                MiniProgramOperateEnum.Material.code,
                logContent,
                null, 0,
                LogResultEnum.LOG_SUCESS.code, null);
        return BaseAnswer.success(null);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseAnswer renameMaterial(RenameMaterialParam param) {
        MiniProgramMaterialLibrary material = materialLibraryMapper.selectByPrimaryKey(param.getId());
        String oldName = material.getName();
        if (material == null) {
            throw new BusinessException("500","素材或文件夹不存在");
        }

        // 校验重命名后的名称是否与同类型的数据重复
        MiniProgramMaterialLibraryExample example = new MiniProgramMaterialLibraryExample();
        MiniProgramMaterialLibraryExample.Criteria criteria = example.createCriteria();
        criteria.andTypeEqualTo(material.getType()).andNameEqualTo(param.getNewName());
        if (material.getParentId() != null) {
            criteria.andParentIdEqualTo(material.getParentId());
        }else{
            criteria.andParentIdIsNull();
        }
        if (!materialLibraryMapper.selectByExample(example).isEmpty()) {
            throw new BusinessException("500","同类型的文件夹或素材名称已存在");
        }

        material.setName(param.getNewName());
        material.setUpdateTime(new Date());
        materialLibraryMapper.updateByPrimaryKeySelective(material);

        String logContent = "folder".equals(material.getType()) ?
                "【文件夹重命名】\n文件夹名称：" + oldName + "修改为" + param.getNewName() :
                "【素材重命名】\n素材名称：" + oldName + "修改为" + param.getNewName();
        logService.recordOperateLog(ModuleEnum.MINI_PROGRAM.code,
                MiniProgramOperateEnum.Material.code,
                logContent,
                null, 0,
                LogResultEnum.LOG_SUCESS.code, null);
        return BaseAnswer.success(null);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseAnswer deleteMaterial(DeleteMaterialParam param) {
        MiniProgramMaterialLibrary material = materialLibraryMapper.selectByPrimaryKey(param.getId());
        if (material == null) {
            throw new BusinessException("500","素材或文件夹不存在");
        }
        if ("folder".equals(material.getType())) {
            MiniProgramMaterialLibraryExample example = new MiniProgramMaterialLibraryExample();
            example.createCriteria().andParentIdEqualTo(material.getId());
            List<MiniProgramMaterialLibrary> children = materialLibraryMapper.selectByExample(example);
            if (!children.isEmpty()) {
                throw new BusinessException("500","删除失败，请先清空文件夹中素材后再删除文件夹！");
            }
        }
        materialLibraryMapper.deleteByPrimaryKey(param.getId());

        String logContent = "folder".equals(material.getType()) ?
                "【文件夹删除】\n文件夹名称：" + material.getName() :
                "【素材删除】\n素材名称：" + material.getName();
        logService.recordOperateLog(ModuleEnum.MINI_PROGRAM.code,
                MiniProgramOperateEnum.Material.code,
                logContent,
                null, 0,
                LogResultEnum.LOG_SUCESS.code, null);
        return BaseAnswer.success(null);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseAnswer batchDeleteMaterial(BatchDeleteMaterialParam param) {
        for (String id : param.getIds()) {
            MiniProgramMaterialLibrary material = materialLibraryMapper.selectByPrimaryKey(id);
            if (material == null) {
                throw new BusinessException("500", "素材或文件夹不存在");
            }
            if ("folder".equals(material.getType())) {
                MiniProgramMaterialLibraryExample example = new MiniProgramMaterialLibraryExample();
                example.createCriteria().andParentIdEqualTo(material.getId());
                List<MiniProgramMaterialLibrary> children = materialLibraryMapper.selectByExample(example);
                if (!children.isEmpty()) {
                    throw new BusinessException("500", "删除失败，请先清空文件夹中素材后再删除文件夹！");
                }
            }
            materialLibraryMapper.deleteByPrimaryKey(id);

            String logContent = "folder".equals(material.getType()) ?
                    "【文件夹删除】\n文件夹名称：" + material.getName() :
                    "【素材删除】\n素材名称：" + material.getName();
            logService.recordOperateLog(ModuleEnum.MINI_PROGRAM.code,
                    MiniProgramOperateEnum.Material.code,
                    logContent,
                    null, 0,
                    LogResultEnum.LOG_SUCESS.code, null);
        }
        return BaseAnswer.success(null);
    }
}