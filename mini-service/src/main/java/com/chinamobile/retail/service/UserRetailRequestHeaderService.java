package com.chinamobile.retail.service;

import com.chinamobile.iot.sc.mode.BasePageQuery;
import com.chinamobile.iot.sc.mode.PageData;
import com.chinamobile.retail.pojo.entity.UserMinProgramRequestHeader;
import com.chinamobile.retail.pojo.param.AuditUserHeaderParam;
import com.chinamobile.retail.pojo.vo.UserHeaderVO;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/5/30
 * @description 分销中心用户头像申请记录service接口类
 */
public interface UserRetailRequestHeaderService {

    /**
     * 新增分销中心用户头像申请记录
     * @param userMinProgramRequestHeader
     */
    void addUserRetailRequestHeader(UserMinProgramRequestHeader userMinProgramRequestHeader);

    /**
     * 分页获取用户头像请求列表
     * @param basePageQuery
     * @return
     */
    PageData<UserHeaderVO> pageUserHeaderVO(BasePageQuery basePageQuery);

    /**
     * 审核用户头像
     * @param auditUserHeaderParam
     * @param userId
     */
    void auditUserHeader(AuditUserHeaderParam auditUserHeaderParam,
                         String userId);
}
