package com.chinamobile.retail.service;


import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.entity.UpResult;
import com.chinamobile.iot.sc.exceptions.BusinessException;
import com.chinamobile.retail.config.FileConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;

import javax.net.ssl.*;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLConnection;
import java.nio.file.Files;
import java.security.cert.CertificateException;
import java.security.cert.X509Certificate;

import static com.chinamobile.iot.sc.common.utils.BaseUtils.uuid;
import static com.chinamobile.iot.sc.exceptions.BaseErrorConstant.EXCEED_MAX_FILE_SIZE;
import static org.springframework.web.bind.annotation.RequestMethod.GET;

@Slf4j
@RefreshScope
public abstract class BaseOssService implements IStorageService{
    @Autowired
    protected FileConfig fileConfig;

    @Value("${ribbon.ReadTimeout:60000}")
    private int readTimeOut;
    @Value("${ribbon.ConnectTimeout:60000}")
    private int connectTimeOut;
    @Value("${spring.servlet.multipart.max-file-size:10MB}")
    private String maxFileSize;

    /**
     * 空实现，什么都不做，这个是针对私有化文件系统去实现的
     * @param key
     * @param response
     */
    public void preview(String key, HttpServletResponse response){}

    private  File getFile(String generalUrl)  {
        try {

            URL url = new URL(generalUrl);
            URLConnection c = url.openConnection();
            if("http".equals(url.getProtocol())){
                return getFile((HttpURLConnection)c);
            }else {
                // use ignore host name verifier
                HttpsURLConnection.setDefaultHostnameVerifier(hostnameVerifier);
                // 打开和URL之间的连接
                HttpsURLConnection connection = (HttpsURLConnection)c;
                // Prepare SSL Context
                TrustManager[] tm = {manager};
                SSLContext sslContext = SSLContext.getInstance("SSL", "SunJSSE");
                sslContext.init(null, tm, new java.security.SecureRandom());
                // 从上述SSLContext对象中得到SSLSocketFactory对象
                SSLSocketFactory ssf = sslContext.getSocketFactory();
                connection.setSSLSocketFactory(ssf);
                return getFile(connection);
            }
        } catch (BusinessException e) {
            log.error("文件IO操作失败",e);
            throw e;
        } catch (Exception e) {
            log.error("文件IO操作失败",e);
        }
        return null;
    }

    private File getFile(HttpURLConnection connection) throws Exception {
        connection.setRequestMethod(GET.name());
        connection.setConnectTimeout(connectTimeOut);
        connection.setReadTimeout(readTimeOut);
        File file = new File(fileConfig.getFiledir() + uuid());
        try(InputStream inStream = connection.getInputStream();
            FileOutputStream fileOutputStream = new FileOutputStream(file)) {
            long max = getMaxSize();
            long c = 0;

            byte[] buff = new byte[1024];
            int rc = 0;
            while ((rc = inStream.read(buff, 0, buff.length)) > 0) {
                c +=rc;
                if(c < max)
                    fileOutputStream.write(buff, 0, rc);
                else  {
                    //删除
                    Files.delete(file.toPath());
                    throw  new BusinessException(EXCEED_MAX_FILE_SIZE,"复制的文件超过限制");
                }
            }
            fileOutputStream.flush();
            return file;
        }
    }

    private  long getMaxSize() {
        String mfs = maxFileSize.toUpperCase();
        if (mfs.contains("P")){
            return  (long)(Double.valueOf(mfs.substring(0,mfs.indexOf('P')))*1024*1024*1024*1024*1024);
        }

        if (mfs.contains("T")){
            return (long)(Double.valueOf(mfs.substring(0,mfs.indexOf('T')))*1024*1024*1024*1024);
        }

        if (mfs.contains("G")){
            return (long)(Double.valueOf(mfs.substring(0,mfs.indexOf('G')))*1024*1024*1024);
        }

        if (mfs.contains("M")){
            return (long)(Double.valueOf(mfs.substring(0,mfs.indexOf('M')))*1024*1024);
        }

        if (mfs.contains("K")){
            return  (long)(Double.valueOf(mfs.substring(0,mfs.indexOf('K')))*1024);
        }
        if (mfs.contains("B")){
            return  Long.valueOf(mfs.substring(0,mfs.indexOf('B')));
        }
        return Long.valueOf(mfs);
    }


    // 服务器名校验
    private static HostnameVerifier hostnameVerifier = new HostnameVerifier() {
        @Override
        public boolean verify(String arg0, SSLSession arg1) {
            return true;
        }
    };
    // 证书/信任库 管理器
    private static TrustManager manager = new X509TrustManager() {

        private X509Certificate[] certificates;

        @Override
        public void checkClientTrusted(X509Certificate certificates[], String authType) throws CertificateException {
            if (this.certificates == null) {
                this.certificates = certificates;
            }
        }

        @Override
        public void checkServerTrusted(X509Certificate[] ax509certificate, String s) throws CertificateException {
            if (this.certificates == null) {
                this.certificates = ax509certificate;
            }

        }

        @Override
        public X509Certificate[] getAcceptedIssuers() {
            return null;
        }
    };

    // 新增方法：创建文件夹
    public abstract BaseAnswer<Void> createFolder(String folderPath);

    // 增强文件夹删除方法
    public abstract BaseAnswer<Void> deleteFolder(String folderPath);

    // 增强上传方法支持路径
    public abstract BaseAnswer<UpResult> uploadFilePath(File file, String targetPath, boolean isCover, int expiredDay) throws Exception;

    // 新增文件/文件夹重命名方法
    public abstract BaseAnswer<Void> renameObject(String sourceKey, String targetKey);
}
