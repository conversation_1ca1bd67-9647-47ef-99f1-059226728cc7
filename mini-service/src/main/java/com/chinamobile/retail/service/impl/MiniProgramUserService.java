package com.chinamobile.retail.service.impl;

import cn.hutool.crypto.asymmetric.SignAlgorithm;
import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.common.BaseConstant;
import com.chinamobile.iot.sc.common.Constant;
import com.chinamobile.iot.sc.common.utils.BaseServiceUtils;
import com.chinamobile.iot.sc.common.utils.JWTUtil;
import com.chinamobile.iot.sc.entity.ByteArrayUpload;
import com.chinamobile.iot.sc.entity.UpResult;
import com.chinamobile.iot.sc.entity.retail.FindMiniProgramUserParam;
import com.chinamobile.iot.sc.enums.log.LogResultEnum;
import com.chinamobile.iot.sc.enums.log.MiniProgramOperateEnum;
import com.chinamobile.iot.sc.enums.log.ModuleEnum;
import com.chinamobile.iot.sc.exceptions.BaseErrorConstant;
import com.chinamobile.iot.sc.exceptions.BusinessException;
import com.chinamobile.iot.sc.exceptions.ExcepStatus;
import com.chinamobile.iot.sc.mode.AccessToken;
import com.chinamobile.iot.sc.mode.LoginIfo4Redis;
import com.chinamobile.iot.sc.mode.PageData;
import com.chinamobile.iot.sc.service.LogService;
import com.chinamobile.retail.config.ProvinceCityConfig;
import com.chinamobile.retail.constant.*;
import com.chinamobile.retail.dao.*;
import com.chinamobile.retail.dao.ext.MiniProgramActivityMapperExt;
import com.chinamobile.retail.dao.ext.UserAgreementMapperExt;
import com.chinamobile.retail.dao.ext.UserMinProgramMapperExt;
import com.chinamobile.retail.exception.StatusContant;
import com.chinamobile.retail.pojo.dto.MiniBMUserImportDTO;
import com.chinamobile.retail.pojo.dto.MiniProgramActivityPaticipantsImportExcelDTO;
import com.chinamobile.retail.pojo.dto.MiniProgramSpuCategoryInfoImportDTO;
import com.chinamobile.retail.pojo.entity.*;
import com.chinamobile.retail.pojo.mapper.UserAgreementListDO;
import com.chinamobile.retail.pojo.param.miniprogram.*;
import com.chinamobile.retail.pojo.vo.UserCenterVO;
import com.chinamobile.retail.pojo.vo.miniprogram.*;
import com.chinamobile.retail.service.IMiniProgramUserService;
import com.chinamobile.retail.service.UserRetailRequestHeaderService;
import com.chinamobile.retail.util.AES;
import com.chinamobile.retail.util.IOTEncodeUtils;
import com.chinamobile.retail.util.LySign;
import com.chinamobile.retail.util.RSAUtils;
import com.chinamobile.retail.util.cache.RedisCacheUtil;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.imageio.ImageIO;
import javax.servlet.http.HttpServletResponse;
import java.awt.image.BufferedImage;
import java.io.BufferedWriter;
import java.io.FileWriter;
import java.io.IOException;
import java.io.InputStream;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingDeque;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.chinamobile.iot.sc.common.BaseConstant.SYSTEM_MINI;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/5/29 16:14
 * @description TODO
 */
@Service
@Slf4j
public class MiniProgramUserService implements IMiniProgramUserService {
    @Resource
    UserMiniProgramAddressMapper userMiniProgramAddressMapper;
    @Resource
    private CaptchaServiceImpl captchaService;
    @Resource
    private MiniProgramActivityUserAwardMapper miniProgramActivityUserAwardMapper;
    @Resource
    private UserMiniProgramMapper userMiniProgramMapper;
    @Resource
    private MiniProgramActivityMapperExt miniProgramActivityMapperExt;
    @Resource
    private OneNetObjectStorageService oneNetObjectStorageService;

    @Resource
    private UserRetailRequestHeaderService userRetailRequestHeaderService;

    @Resource
    private RedisTemplate redisTemplate;

    @Resource
    private ShopManagerInfoMapper shopManagerInfoMapper;

    @Resource
    private ShopCustomerInfoMapper shopCustomerInfoMapper;

    @Resource
    private UserAgreementMapper userAgreementMapper;

    @Resource
    private UserAgreementMapperExt userAgreementMapperExt;

    @Resource
    private ContractCityInfoMapper contractCityInfoMapper;

    @Value(("${private.key}"))
    private String privateKeyStr;
    @Value("${public.key}")
    private String publicKeyStr;
    @Value("${iot.encodeKey}")
    private String encryptKey;
    @Resource
    private LogService logService;

    @Resource
    private UserRetailMapper userRetailMapper;

    @Resource
    private UserPartnerMapper userPartnerMapper;

    @Resource
    private RoleInfoMapper roleInfoMapper;
    @Resource
    private RestTemplate restTemplate;
    @Resource
    private RedisCacheUtil redisCacheUtil;

    @Resource
    private MiniProgramSaleYearReportMapper miniProgramSaleYearReportMapper;

    @Resource
    private UserMinProgramMapperExt userMinProgramMapperExt;

    @Resource
    private SpuCategoryInfoMapper spuCategoryInfoMapper;

    @Resource
    private MiniProgramSaleYearReportViewTagMapper miniProgramSaleYearReportViewTagMapper;

    @Resource
    private ProvinceCityConfig provinceCityConfig;

    private ExecutorService executorService = new ThreadPoolExecutor(50, 50, 1L, TimeUnit.MINUTES, new LinkedBlockingDeque<>(10000));

    private static String[] categories = new String[]{
            "千里眼", "和对讲", "OneNET", "和易充"
    };

    //缓存用户协议
    public static final String REDIS_USER_AGREENT_KEY = "user_agreement";

    @Resource
    private OrganizationRelationMapper organizationRelationMapper;
    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Override
    @DS("save")
    public void updateUserName(UpdateUserNameParam updateUserNameParam) {

        UserMiniProgram userMiniProgram = new UserMiniProgram();
        userMiniProgram.setId(updateUserNameParam.getUserId());
        userMiniProgram.setName(updateUserNameParam.getName());
        userMiniProgram.setUpdateTime(new Date());
        userMiniProgramMapper.updateByPrimaryKeySelective(userMiniProgram);
        redisCacheUtil.delete(Constant.REDIS_KEY_MINI_USER + updateUserNameParam.getUserId());
    }

    @Override
    @DS("save")
    public void uploadUserHeader(MultipartFile file, String userId) {
        try {
            boolean isImage = isImage(file.getInputStream());
            if (!isImage) {
                throw new BusinessException("10012", "只能上传图片类型的文件");
            }
        } catch (IOException e) {
            throw new BusinessException("10500", "文件读取错误");
        }
        String snowId = BaseServiceUtils.getId();
        String filename = file.getOriginalFilename();
        //处理同名文件覆盖问题,在文件名后加"_xxxxx"
        String[] nameArray = filename.split("\\.");
        filename = nameArray[0] + "_" + snowId + "." + nameArray[1];
        ByteArrayUpload byteArrayUpload = new ByteArrayUpload();
        BaseAnswer<UpResult> uploadResult = null;
        try {
            byteArrayUpload.setBytes(file.getBytes());
            byteArrayUpload.setFileName(filename);
            uploadResult = oneNetObjectStorageService.uploadByte(byteArrayUpload);
        } catch (Exception e) {
            log.error("上传文件发生异常", e);
            throw new BusinessException(BaseErrorConstant.INTERNAL_ERROR);
        }
        if (!uploadResult.getStateCode().equals(ExcepStatus.getSuccInstance().getStateCode())) {
            log.error("上传文件响应失败:{}", JSON.toJSONString(uploadResult));
            throw new BusinessException(BaseErrorConstant.INTERNAL_ERROR);
        }

        UpResult upResult = uploadResult.getData();
        Date date = new Date();
        UserMinProgramRequestHeader userMinProgramRequestHeader = new UserMinProgramRequestHeader();
        userMinProgramRequestHeader.setId(BaseServiceUtils.getId());
        userMinProgramRequestHeader.setUserRetailId(userId);
        userMinProgramRequestHeader.setAuditStatus(HeaderAuditStatusEnum.NO_AUDIT.getStatus());
        userMinProgramRequestHeader.setHearderUrl(upResult.getOuterUrl());
        userMinProgramRequestHeader.setFileKey(upResult.getKey());
        userMinProgramRequestHeader.setCreateTime(date);
        userMinProgramRequestHeader.setUpdateTime(date);
        userRetailRequestHeaderService.addUserRetailRequestHeader(userMinProgramRequestHeader);
        redisCacheUtil.delete(Constant.REDIS_KEY_MINI_USER + userId);
    }

    @Override
    @DS("save")
    public UserCenterVO getUserCenterInfo(String userId) {
        UserMiniProgram userMiniProgram = userMiniProgramMapper.selectByPrimaryKey(userId);
        if(userMiniProgram != null){
            userMiniProgram = redisCacheUtil.loadAndCache(
                    Constant.REDIS_KEY_MINI_USER + userId,
                    RedisLockConstant.LOCK_MINI_USER + userId,
                    1,
                    TimeUnit.DAYS,
                    () -> userMiniProgramMapper.selectByPrimaryKey(userId)
            );
        }
        UserPartner userPartner = userPartnerMapper.selectByPrimaryKey(userId);
        if(userPartner != null){
            userPartner = redisCacheUtil.loadAndCache(
                    Constant.REDIS_KEY_MINI_USER + userId,
                    RedisLockConstant.LOCK_MINI_USER + userId,
                    1,
                    TimeUnit.DAYS,
                    () -> userPartnerMapper.selectByPrimaryKey(userId)
            );
        }
        if (!Optional.ofNullable(userMiniProgram).isPresent()) {
            if (!Optional.ofNullable(userPartner).isPresent()) {
                throw new BusinessException("10004", "用户信息错误");
            }
        }
        UserCenterVO userCenterVO = new UserCenterVO();
        //装维不需要后面的操作
        //要不要做头像审批及相关
        if (Optional.ofNullable(userPartner).isPresent()){
            BeanUtils.copyProperties(userPartner, userCenterVO);
            RoleInfo roleInfo = roleInfoMapper.selectByPrimaryKey(userPartner.getRoleId());
            userCenterVO.setRoleType(roleInfo.getRoleType());
            if(roleInfo.getRoleType().equals(BaseConstant.PARTNER_INSTALL_LORD_ROLE)
                    || roleInfo.getRoleType().equals(BaseConstant.PARTNER_INSTALL_SUB_ROLE)){
                userCenterVO.setRoleName("装维合作伙伴");
            }else{
                userCenterVO.setRoleName(roleInfo.getName());
            }
            userCenterVO.setPhone(IOTEncodeUtils.decryptIOTMessage(userPartner.getPhone(),encryptKey));
            return userCenterVO;
        }
        BeanUtils.copyProperties(userMiniProgram, userCenterVO);
        userCenterVO.setProvinceCode(userMiniProgram.getBeId());
        userCenterVO.setCityCode(userMiniProgram.getLocation());

        // 商城同步的账号如果缺少省份和城市的名称，则从OS的contract_city_info表查询
        if (ObjectUtils.isEmpty(userCenterVO.getProvince()) && !ObjectUtils.isEmpty(userMiniProgram.getBeId())) {
            String beid = userMiniProgram.getBeId();
            String location = userMiniProgram.getLocation();
            ContractCityInfoExample.Criteria criteria = ContractCityInfoExample.newAndCreateCriteria();
            criteria.andProvinceMallCodeEqualTo(beid);
            if (!ObjectUtils.isEmpty(location)) {
                criteria.andMallCodeEqualTo(location);
            }
            List<ContractCityInfo> contractCityInfos = contractCityInfoMapper.selectByExample(criteria.example());
            if (!CollectionUtils.isEmpty(contractCityInfos)) {
                ContractCityInfo contractCityInfo = contractCityInfos.get(0);
                userCenterVO.setProvince(contractCityInfo.getProvinceMallName());
                if (!ObjectUtils.isEmpty(location)) {
                    userCenterVO.setCity(contractCityInfo.getMallName());
                }
            }
        }
        Integer num = 0;
        if (userMiniProgram.getUserId() != null) {
            num = miniProgramActivityMapperExt.getNotReceivedAwardNum(userMiniProgram.getUserId());
        }

        userCenterVO.setPrizeWaitingCollectedNum(num == null ? 0 : num);
        Integer auditHeaderNotice = userMiniProgram.getAuditHeaderNotice();
        // 如果未提示，则查询后更新为已提示
        if (auditHeaderNotice != null && auditHeaderNotice == 1) {
            UserMiniProgram userNotice = new UserMiniProgram();
            userNotice.setId(userId);
            userNotice.setAuditHeaderNotice(2);
            userNotice.setUpdateTime(new Date());
            userMiniProgramMapper.updateByPrimaryKeySelective(userNotice);
        }
        String organizationName = StringUtils.isNotBlank(userMiniProgram.getOrgRegionName()) ? userMiniProgram.getOrgRegionName()
                : StringUtils.isNotBlank(userMiniProgram.getOrgCityName()) ?  userMiniProgram.getOrgCityName()
                : StringUtils.isNotBlank(userMiniProgram.getOrgProvinceName()) ?  userMiniProgram.getOrgProvinceName() : "全国";
        userCenterVO.setRoleName(MiniRoleEnum.getName(userMiniProgram.getRoleType(),
                userMiniProgram.getProvinceName(), userMiniProgram.getCityName(), organizationName));
        userCenterVO.setOrganizationName(organizationName);
        return userCenterVO;
    }

    @Override
    @DS("query")
    public UserMiniProgram getUserMiniProgramById(String id) {
        return userMiniProgramMapper.selectByPrimaryKey(id);
    }

    @Override
    @DS("save")
    public void updateUserSelectedById(UserMiniProgram userMiniProgram) {
        userMiniProgramMapper.updateByPrimaryKeySelective(userMiniProgram);
        redisCacheUtil.delete(Constant.REDIS_KEY_MINI_USER + userMiniProgram.getId());
    }

    @Override
    @DS("query")
    public void getLoginSmsValidCode(String phone) {
        log.info("getLoginSmsValidCode");
        log.info("加密后手机号：{}", phone);
        try {
            phone = RSAUtils.decryptByPrivateKey(phone, privateKeyStr);
            log.info("解密后手机号：{}", phone);
        } catch (Exception e) {
            throw new BusinessException(BaseErrorConstant.PHONE_ERROR);
        }
        List<UserMiniProgram> users = userMiniProgramMapper.selectByExample(new UserMiniProgramExample().createCriteria().andPhoneEqualTo(phone).example());
        // 找装维用户相关
        List<UserPartner> userPartnerList = userPartnerMapper.selectByExample(new UserPartnerExample()
                .createCriteria()
                .andRoleIdIn(Arrays.asList("1376576697935507456","1376576697935507457","1376576697935507458"))
                .andPhoneEqualTo(IOTEncodeUtils.encryptIOTMessage(phone, encryptKey))
                .andIsCancelEqualTo(false)
                .andIsLogoffEqualTo(false)
                .example());
        if (CollectionUtils.isEmpty(users)) {
            if (CollectionUtils.isEmpty(userPartnerList)) {
                throw new BusinessException(StatusConstant.MINI_USER_NO_EXIST);
            }
        }
        for (UserMiniProgram user : users) {
            if (MiniRoleEnum.NORMAL.getType().equals(user.getRoleType())) {
                // 角色不对不许登录
                continue;
            }
            // 判断账户是否可用
            if ("1".equals(user.getStatus())) {
                captchaService.getSmsLoginMiniProgram(phone);
                return;
            }
        }
        if (CollectionUtils.isNotEmpty(userPartnerList)) {
            captchaService.getSmsLoginMiniProgram(phone);
            return;
        }
        throw new BusinessException(StatusConstant.MINI_USER_NO_EXIST);
    }

    @Override
    @DS("save")
    public MiniProgramLoginVO loginBySmsCode(String phone) {
        log.info("loginBySmsCode");
        log.info("加密后手机号：{}", phone);
        try {
            phone = RSAUtils.decryptByPrivateKey(phone, privateKeyStr);
            log.info("解密后手机号：{}", phone);
        } catch (Exception e) {
            throw new BusinessException(BaseErrorConstant.PHONE_ERROR);
        }
        log.info("login phone = {}", phone);
        List<UserMiniProgram> users = userMiniProgramMapper.selectByExample(new UserMiniProgramExample().createCriteria().andPhoneEqualTo(phone).andStatusEqualTo("1").example());
        // 找装维用户相关
        List<UserPartner> userPartnerList = userPartnerMapper.selectByExample(new UserPartnerExample()
                .createCriteria()
                .andRoleIdIn(Arrays.asList("1376576697935507456","1376576697935507457","1376576697935507458"))
                .andPhoneEqualTo(IOTEncodeUtils.encryptIOTMessage(phone, encryptKey))
                .andIsCancelEqualTo(false)
                .andIsLogoffEqualTo(false)
                .example());
        if (CollectionUtils.isEmpty(users)) {
            if(CollectionUtils.isEmpty(userPartnerList)){
                throw new BusinessException(StatusConstant.MINI_USER_NO_EXIST);
            }
        }
        users = sortUser(users);
        for (UserMiniProgram user : users) {
            if (MiniRoleEnum.NORMAL.getType().equals(user.getRoleType())) {
                // 角色不对不许登录
                continue;
            }
            // 判断账户是否可用
            if ("1".equals(user.getStatus())) {
                MiniProgramLoginVO miniProgramLoginVO = new MiniProgramLoginVO();
                miniProgramLoginVO.setToken(generateToken(user));
                miniProgramLoginVO.setUserId(user.getId());
                miniProgramLoginVO.setOsUserId(user.getUserId());
                user.setLatestLoginTime(new Date());
                userMiniProgramMapper.updateByPrimaryKeySelective(user);
                //logService.recordOperateLog(ModuleEnum.LOGIN.code, LoginOperateEnum.MINI_PROGRAM_LOGIN.code, "-", user.getId(), 2, LogResultEnum.LOG_SUCESS.code, null);

                //分销员，渠道商登录时，将用户添加到分销用户表UserRetail，触发积分统计
                if (!ObjectUtils.isEmpty(user.getUserId()) && (StringUtils.equals(MiniRoleEnum.DISTRIBUTOR_FIRST.getType(), user.getRoleType())
                        || StringUtils.equals(MiniRoleEnum.DISTRIBUTOR_SECOND.getType(), user.getRoleType())
                        || StringUtils.equals(MiniRoleEnum.CHANNEL.getType(), user.getRoleType()))) {
                    UserRetail userRetail = userRetailMapper.selectByPrimaryKey(user.getUserId());
                    if (userRetail == null) {
                        userRetail = new UserRetail();
                        convertUserMini2UserRetail(user, userRetail);
                        userRetailMapper.insertSelective(userRetail);
                    }
                }
                return miniProgramLoginVO;
            }
        }
        if(CollectionUtils.isNotEmpty(userPartnerList)){
            MiniProgramLoginVO miniProgramLoginVO = new MiniProgramLoginVO();
            miniProgramLoginVO.setToken(generateTokenUserPartner(userPartnerList.get(0)));
            miniProgramLoginVO.setUserId(userPartnerList.get(0).getUserId());
            miniProgramLoginVO.setOsUserId(userPartnerList.get(0).getUserId());
            return miniProgramLoginVO;
        }
        throw new BusinessException(StatusConstant.MINI_USER_NO_EXIST);
    }

    private List<UserMiniProgram> sortUser(List<UserMiniProgram> users) {
        // 根据角色排序,排序规则
        // 物联网业管-> 省管 -> 市管 -> 区域管理 -> 客户经理 -> 一级分销员 -> 二级分销员 -> 渠道商 ->普通用户
        List<UserMiniProgram> sortUsers = new ArrayList<>();
        Map<String,List<UserMiniProgram>> userMap = users.stream().collect(Collectors.groupingBy(UserMiniProgram::getRoleType));
        sortUsers.addAll(userMap.getOrDefault(MiniRoleEnum.IOT.getType(),new ArrayList<>()));
        sortUsers.addAll(userMap.getOrDefault(MiniRoleEnum.PROVINCE.getType(),new ArrayList<>()));
        sortUsers.addAll(userMap.getOrDefault(MiniRoleEnum.CITY.getType(),new ArrayList<>()));
        sortUsers.addAll(userMap.getOrDefault(MiniRoleEnum.REGION.getType(),new ArrayList<>()));
        sortUsers.addAll(userMap.getOrDefault(MiniRoleEnum.MANAGER.getType(),new ArrayList<>()));
        sortUsers.addAll(userMap.getOrDefault(MiniRoleEnum.DISTRIBUTOR_FIRST.getType(),new ArrayList<>()));
        sortUsers.addAll(userMap.getOrDefault(MiniRoleEnum.DISTRIBUTOR_SECOND.getType(),new ArrayList<>()));
        sortUsers.addAll(userMap.getOrDefault(MiniRoleEnum.CHANNEL.getType(),new ArrayList<>()));
        sortUsers.addAll(userMap.getOrDefault(MiniRoleEnum.NORMAL.getType(),new ArrayList<>()));
        return sortUsers;
    }

    private void convertUserMini2UserRetail(UserMiniProgram mini, UserRetail userRetail) {
        userRetail.setRoleType(Integer.valueOf(mini.getRoleType()));
        userRetail.setRecommendCode(mini.getCode());
        userRetail.setCustCode(mini.getCode());
        userRetail.setWorkNum(mini.getNumber());
        userRetail.setName(mini.getName());
        userRetail.setPhone(mini.getPhone());
        userRetail.setProvinceCode(mini.getBeId());
        userRetail.setCityCode(mini.getLocation());
        userRetail.setProvince(mini.getProvinceName());
        userRetail.setCity(mini.getCityName());
        userRetail.setUserId(mini.getUserId());
        userRetail.setId(mini.getUserId());
        userRetail.setRegTime(new Date());
        userRetail.setLatestLoginTime(new Date());
        userRetail.setAuditStatus(mini.getAuditStatus());
        userRetail.setAuditReason(mini.getAuditReason());
        userRetail.setAuditHeaderNotice(mini.getAuditHeaderNotice());
    }

    @Override
    @DS("query")
    public String findUserId(FindMiniProgramUserParam param) {
        log.debug("查询小程序用户ID，param: {}", JSON.toJSONString(param));
        UserMiniProgramExample userMiniProgramExample = new UserMiniProgramExample();
        UserMiniProgramExample.Criteria criteria = userMiniProgramExample.createCriteria();
        if (StringUtils.isNotEmpty(param.getCode())) {
            criteria.andCodeEqualTo(param.getCode());
        }
        if (StringUtils.isNotEmpty(param.getPhone())) {
            criteria.andPhoneEqualTo(param.getPhone());
        }
        List<UserMiniProgram> userMiniPrograms = userMiniProgramMapper.selectByExample(userMiniProgramExample);
        if (!CollectionUtils.isEmpty(userMiniPrograms)) {
            return userMiniPrograms.get(0).getId();
        } else {
            return null;
        }
    }

    @Override
    @DS("save")
    public String migrateFromManageAndCustomer() {
        userMiniProgramMapper.deleteByExample(new UserMiniProgramExample());
        int mgrNew = 0;
        int mgrUpdate = 0;
        int customerNew = 0;
        int customerUpdate = 0;
        List<UserMiniProgram> userMiniPrograms = new ArrayList<>();
        List<ShopManagerInfo> shopManagerInfos = shopManagerInfoMapper.selectByExample(new ShopManagerInfoExample());
        if (!CollectionUtils.isEmpty(shopManagerInfos)) {
            for (int i = 0; i < shopManagerInfos.size(); i++) {
                ShopManagerInfo shopManagerInfo = shopManagerInfos.get(i);
                UserMiniProgram userMiniProgram = convertFromShopManagerInfo(shopManagerInfo);
                userMiniProgram.setId(BaseServiceUtils.getId());
                userMiniProgram.setCreateTime(shopManagerInfo.getCreateTime());
                userMiniProgram.setUpdateTime(shopManagerInfo.getUpdateTime());
                userMiniPrograms.add(userMiniProgram);
                mgrNew++;
            }
        }
        List<ShopCustomerInfo> shopCustomerInfos = shopCustomerInfoMapper.selectByExample(new ShopCustomerInfoExample());
        if (!CollectionUtils.isEmpty(shopCustomerInfos)) {
            for (int i = 0; i < shopCustomerInfos.size(); i++) {
                ShopCustomerInfo shopCustomerInfo = shopCustomerInfos.get(i);
                UserMiniProgram userMiniProgram = convertFromShopCustomerInfo(shopCustomerInfo);
                userMiniProgram.setId(BaseServiceUtils.getId());
                userMiniProgram.setCreateTime(new Date());
                userMiniProgram.setUpdateTime(new Date());
                userMiniPrograms.add(userMiniProgram);
                customerNew++;
            }
        }
        if (!CollectionUtils.isEmpty(userMiniPrograms)) {
            for (int i = 0; i < userMiniPrograms.size(); i += 1000) {
                List<UserMiniProgram> subList = userMiniPrograms.subList(i, Math.min(i + 1000, userMiniPrograms.size()));
                userMiniProgramMapper.batchInsert(subList);
            }
        }
        return "迁移完成，客户经理：新增" + mgrNew + "，更新" + mgrUpdate + "，分销员和渠道商：新增" + customerNew + "，更新" + customerUpdate;
    }

    @Override
    @DS("query")
    public void customerEncryption() {
        ServletRequestAttributes requestAttr = (ServletRequestAttributes) RequestContextHolder.currentRequestAttributes();
        HttpServletResponse response = requestAttr.getResponse();
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");

        FileWriter writer = null;
        BufferedWriter bufferedWriter = null;
        try {
            writer = new FileWriter("outManagerSql.txt");
            bufferedWriter = new BufferedWriter(writer);
            UserMiniProgramExample userMiniProgramExample = new UserMiniProgramExample();
            UserMiniProgramExample.Criteria criteria = userMiniProgramExample.createCriteria().andCodeEqualTo("531BIOT2023010310015626118");
            criteria.example();
            List<UserMiniProgram> userMiniPrograms = userMiniProgramMapper.selectByExample(userMiniProgramExample);
            for (UserMiniProgram userMiniProgram : userMiniPrograms) {
//                userMiniProgram.setNameEncryption(IOTEncodeUtils.encryptIOTMessage(userMiniProgram.getName(), encryptKey));
//                userMiniProgram.setCodeEncryption(IOTEncodeUtils.encryptIOTMessage(userMiniProgram.getCode(), encryptKey));
//                userMiniProgramMapper.updateByPrimaryKeySelective(userMiniProgram);
                Map<String, String> map = new HashMap<>();
                map.put("code_encryption", IOTEncodeUtils.encryptIOTMessage(userMiniProgram.getCode(), encryptKey));
                map.put("name_encryption", IOTEncodeUtils.encryptIOTMessage(userMiniProgram.getName(), encryptKey));
                String condition = "id = '" + userMiniProgram.getId() + "'";
                String updateQueryInfo = buildUpdateQuery("user_mini_program", map, condition);

                try {
                    bufferedWriter.write(updateQueryInfo);
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }


        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            try {
                if (bufferedWriter != null) {
                    bufferedWriter.close();
                }
                if (writer != null) {
                    writer.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 拼装update  sql   manager
     *
     * @param tableName
     * @param updateValues
     * @param condition
     * @return
     */
    public String buildUpdateQuery(String tableName, Map<String, String> updateValues, String condition) {
        StringBuilder builder = new StringBuilder();
        builder.append("update ").append(tableName).append(" set ");
        for (Map.Entry<String, String> entry : updateValues.entrySet()) {
            builder.append(entry.getKey()).append(" = '").append(entry.getValue()).append("', ");
        }
        builder.setLength(builder.length() - 2);
        builder.append(" where ").append(condition).append(";\n");
        return builder.toString();
    }

    @Override
    @DS("query")
    public PageData<UserAgreementListVO> userAgreementList(UserAgreementListParam param, boolean isMini) {
        PageData<UserAgreementListVO> pageData = new PageData<>();
        pageData.setPage(param.getPageNum());
        PageHelper.startPage(param.getPageNum(),param.getPageSize());
        List<UserAgreementListDO> userAgreements = userAgreementMapperExt.userAgreementList(param);
        if(CollectionUtils.isEmpty(userAgreements)){
            return pageData;
        }
        PageInfo<UserAgreementListDO> pageInfo = new PageInfo<>(userAgreements);
        pageData.setCount(pageInfo.getTotal());
        List<UserAgreementListVO> data = userAgreements.stream().map(userAgreement -> {
            UserAgreementListVO userAgreementListVO = new UserAgreementListVO();
            BeanUtils.copyProperties(userAgreement, userAgreementListVO);
            //对于小程序前端：审核中的和驳回状态和草稿，需要展示提交审核之前的内容
            if(isMini &&  (userAgreement.getStatus() == 1 || userAgreement.getStatus() == 4 || userAgreement.getStatus() == 0)){
                String str = (String)stringRedisTemplate.opsForHash().get(REDIS_USER_AGREENT_KEY, userAgreement.getId());
                UserAgreement agreement = JSONObject.parseObject(str, UserAgreement.class);
                userAgreementListVO.setContent(agreement.getContent());
            }
            return userAgreementListVO;
        }).collect(Collectors.toList());
        pageData.setData(data);
        return pageData;
    }

    @DS("save")
    @Override
    public void updateUserAgreement(List<UpdateUserAgreementParam> params) {
        /*for (UpdateUserAgreementParam param : params) {
            UserAgreement userAgreement = new UserAgreement();
            BeanUtils.copyProperties(param, userAgreement);

            List<UserAgreement> userAgreements = userAgreementMapper.selectByExample(
                    new UserAgreementExample().createCriteria().andTypeEqualTo(param.getType()).example()
            );
            if (!CollectionUtils.isEmpty(userAgreements)) {
                UserAgreement savedAgreement = userAgreements.get(0);
                userAgreement.setId(savedAgreement.getId());
                userAgreement.setCreateTime(savedAgreement.getCreateTime());
                userAgreement.setUpdateTime(new Date());
                userAgreementMapper.updateByPrimaryKeySelective(userAgreement);
            } else {
                userAgreement.setId(BaseServiceUtils.getId());
                userAgreement.setCreateTime(new Date());
                userAgreement.setUpdateTime(new Date());
                userAgreementMapper.insertSelective(userAgreement);
            }
            HttpServletRequest request = ((ServletRequestAttributes) (RequestContextHolder.currentRequestAttributes())).getRequest();
            String userId = request.getHeader(Constant.HEADER_KEY_USER_ID);
            logService.recordOperateLog(ModuleEnum.MINI_PROGRAM.code, MiniProgramOperateEnum.USER_AGREEMENT.code, "【编辑】\n" + param.getName() + "内容编辑为：\n" + param.getContent(), userId, 0, LogResultEnum.LOG_SUCESS.code, null);
        }*/
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @DS("save")
    public BaseAnswer<Void> addAddress(ActivityAwardAddressParam param, String userId) {
        UserMiniProgram userMiniPrograms = userMiniProgramMapper.selectByPrimaryKey(userId);
        if (userMiniPrograms == null) {
            throw new BusinessException(StatusContant.MINI_PROGRAM_ACTIVITY_USER_NOT_EXIST);
        }
        List<UserMiniProgramAddress> userMiniProgramAddresses = userMiniProgramAddressMapper.selectByExample(new UserMiniProgramAddressExample().createCriteria().andUserIdEqualTo(userId).example());
        if (userMiniProgramAddresses != null && userMiniProgramAddresses.size() > 10) {
            throw new BusinessException(StatusContant.MINI_PROGRAM_ADDRESS_OVER_LIMIT);
        }
        UserMiniProgramAddress userMiniProgramAddress = new UserMiniProgramAddress();
        userMiniProgramAddress.setId(BaseServiceUtils.getId());
        userMiniProgramAddress.setUserId(userMiniPrograms.getUserId());
        userMiniProgramAddress.setAddr1(param.getAddr1());
        userMiniProgramAddress.setAddr2(param.getAddr2());
        userMiniProgramAddress.setAddr3(param.getAddr3());
        userMiniProgramAddress.setUsaddr(param.getUsaddr());
        userMiniProgramAddress.setName(param.getName());
        userMiniProgramAddress.setPhone(param.getPhone());
        userMiniProgramAddress.setIsdefault(param.getIsDefault());
        userMiniProgramAddress.setCreateTime(new Date());
        //判断之前是否有默认地址，有则设为非默认
        if (param.getIsDefault() == 0) {
            for (UserMiniProgramAddress userMiniProgramAddressItem : userMiniProgramAddresses) {
                if (userMiniProgramAddressItem.getIsdefault() == 0) {
                    userMiniProgramAddressItem.setIsdefault(1);
                    userMiniProgramAddressMapper.updateByPrimaryKeySelective(userMiniProgramAddressItem);
                }
            }

        }
        userMiniProgramAddressMapper.insertSelective(userMiniProgramAddress);


        return BaseAnswer.success(null);

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @DS("save")
    public BaseAnswer<Void> editAddress(ActivityAwardAddressParam param, String userId) {
        UserMiniProgram userMiniProgram = userMiniProgramMapper.selectByPrimaryKey(userId);
        if (userMiniProgram == null) {
            throw new BusinessException(StatusContant.MINI_PROGRAM_ACTIVITY_USER_NOT_EXIST);
        }
        UserMiniProgramAddress userMiniProgramAddress = userMiniProgramAddressMapper.selectByPrimaryKey(param.getId());
        if (userMiniProgramAddress == null) {
            throw new BusinessException(StatusContant.MINI_PROGRAM_ACTIVITY_USER_ADDRESS_NOT_EXIST);
        }
        userMiniProgramAddress.setUserId(userMiniProgram.getUserId());
        userMiniProgramAddress.setAddr1(param.getAddr1());
        userMiniProgramAddress.setAddr2(param.getAddr2());
        userMiniProgramAddress.setAddr3(param.getAddr3());
        userMiniProgramAddress.setUsaddr(param.getUsaddr());
        userMiniProgramAddress.setName(param.getName());
        userMiniProgramAddress.setPhone(param.getPhone());
        userMiniProgramAddress.setIsdefault(param.getIsDefault());
        userMiniProgramAddress.setUpdateTime(new Date());
        //判断之前是否有默认地址，有则设为非默认
        if (param.getIsDefault() == 0) {
            List<UserMiniProgramAddress> userMiniProgramAddresses = userMiniProgramAddressMapper.selectByExample(new UserMiniProgramAddressExample().createCriteria().andUserIdEqualTo(userId).andIsdefaultEqualTo(0).example());
            if (userMiniProgramAddresses != null && userMiniProgramAddresses.size() > 0) {
                UserMiniProgramAddress userMiniProgramAddressReset = userMiniProgramAddresses.get(0);
                userMiniProgramAddressReset.setIsdefault(1);
                userMiniProgramAddressMapper.updateByPrimaryKeySelective(userMiniProgramAddressReset);
            }

        }
        userMiniProgramAddressMapper.updateByPrimaryKeySelective(userMiniProgramAddress);
        return BaseAnswer.success(null);

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @DS("save")
    public BaseAnswer<Void> deleteAddress(String id) {
        UserMiniProgramAddress userMiniProgramAddress = userMiniProgramAddressMapper.selectByPrimaryKey(id);
        if (userMiniProgramAddress == null) {
            throw new BusinessException(StatusContant.MINI_PROGRAM_ACTIVITY_USER_ADDRESS_NOT_EXIST);
        }
        //判断删除地址id是否关联了奖品，关联则做软删除。未关联直接删除
        List<MiniProgramActivityUserAward> miniProgramActivityUserAwards = miniProgramActivityUserAwardMapper.selectByExample(new MiniProgramActivityUserAwardExample().createCriteria().andAddressIdEqualTo(id).example());
        if (miniProgramActivityUserAwards != null && miniProgramActivityUserAwards.size() > 0) {
            userMiniProgramAddress.setIsDelete(1);
            userMiniProgramAddressMapper.updateByPrimaryKeySelective(userMiniProgramAddress);
        } else {
            userMiniProgramAddressMapper.deleteByPrimaryKey(id);
        }

        return BaseAnswer.success(null);

    }

    @Override
    @DS("query")
    public BaseAnswer<List<UserMiniProgramAddress>> getAddress(String userId) {
        UserMiniProgram userMiniProgram = userMiniProgramMapper.selectByPrimaryKey(userId);
        if (userMiniProgram == null) {
            throw new BusinessException(StatusContant.MINI_PROGRAM_ACTIVITY_USER_NOT_EXIST);
        }
        UserMiniProgramAddressExample example = new UserMiniProgramAddressExample();
        UserMiniProgramAddressExample.Criteria criteria = example.createCriteria();
        criteria.andUserIdEqualTo(userMiniProgram.getUserId());
        criteria.andIsDeleteEqualTo(0);
        example.setOrderByClause("create_time DESC");
        List<UserMiniProgramAddress> userMiniProgramAddresses = userMiniProgramAddressMapper.selectByExample(example);
        //将默认地址，设为第一个
        if (userMiniProgramAddresses != null && userMiniProgramAddresses.size() > 0) {
            for (UserMiniProgramAddress userMiniProgramAddress : userMiniProgramAddresses) {
                if (userMiniProgramAddress.getIsdefault() == 0) {
                    userMiniProgramAddresses.remove(userMiniProgramAddress);
                    userMiniProgramAddresses.add(0, userMiniProgramAddress);
                    break;
                }
            }
        }
        return BaseAnswer.success(userMiniProgramAddresses);
    }

    @Override
    @DS("query")
    public List<UserRoleInfoVO> listUserRole(String userId) {
        UserMiniProgram userMiniProgram = userMiniProgramMapper.selectByPrimaryKey(userId);
        UserPartner userPartner = userPartnerMapper.selectByPrimaryKey(userId);
        if (null == userMiniProgram || "0".equals(userMiniProgram.getStatus())) {
            if(null == userPartner){
                throw new BusinessException(StatusConstant.USER_NO_EXIST);
            }
        }
        String phone = null;
        if(!(null == userMiniProgram || "0".equals(userMiniProgram.getStatus()))){
            phone = userMiniProgram.getPhone();
        }else if(null != userPartner){
            phone = IOTEncodeUtils.decryptIOTMessage(userPartner.getPhone(), encryptKey);

        }
        List<UserMiniProgram> userMiniPrograms = userMiniProgramMapper.selectByExample(
                new UserMiniProgramExample().createCriteria()
                        .andPhoneEqualTo(phone)
                        .andRoleTypeNotEqualTo("0")
                        .andStatusEqualTo("1")
                        .example()
        );
        userMiniPrograms = sortUser(userMiniPrograms);
        List<UserRoleInfoVO> userRoleInfoVOS = null;
        if (!CollectionUtils.isEmpty(userMiniPrograms)) {
            userRoleInfoVOS = userMiniPrograms.stream().map(user -> {
                UserRoleInfoVO userRoleInfoVO = new UserRoleInfoVO();
                userRoleInfoVO.setUserId(user.getId());
                userRoleInfoVO.setRoleType(user.getRoleType());
                userRoleInfoVO.setCode(user.getCode());
                userRoleInfoVO.setName(user.getName());
                userRoleInfoVO.setRoleName(MiniRoleEnum.getName(user.getRoleType(),
                        user.getProvinceName(),user.getCityName(),user.getRegionName()));
                return userRoleInfoVO;
            }).collect(Collectors.toList());
        }
        List<UserPartner> userPartnerList = userPartnerMapper.selectByExample(new UserPartnerExample()
                .createCriteria()
                .andRoleIdIn(Arrays.asList("1376576697935507456","1376576697935507457","1376576697935507458"))
                .andPhoneEqualTo(IOTEncodeUtils.encryptIOTMessage(phone, encryptKey))
                .andIsCancelEqualTo(false)
                .andIsLogoffEqualTo(false)
                .example());
        if (!CollectionUtils.isEmpty(userPartnerList)) {
            List<UserRoleInfoVO> userRoleInfoVOSPartner = userPartnerList.stream().map(user -> {
                UserRoleInfoVO userRoleInfoVO = new UserRoleInfoVO();
                RoleInfo roleInfo = roleInfoMapper.selectByPrimaryKey(user.getRoleId());
                userRoleInfoVO.setUserId(user.getUserId());
                userRoleInfoVO.setName(user.getName());
                userRoleInfoVO.setRoleType(roleInfo.getRoleType());
                if(roleInfo.getRoleType().equals(BaseConstant.PARTNER_INSTALL_LORD_ROLE)
                        || roleInfo.getRoleType().equals(BaseConstant.PARTNER_INSTALL_SUB_ROLE)){
                    userRoleInfoVO.setRoleName("装维合作伙伴");
                }else{
                    userRoleInfoVO.setRoleName(roleInfo.getName());
                }
                return userRoleInfoVO;
            }).collect(Collectors.toList());
            if (userRoleInfoVOS == null) {
                userRoleInfoVOS = userRoleInfoVOSPartner;
            } else {
                userRoleInfoVOS.addAll(userRoleInfoVOSPartner);
            }
        }
        return userRoleInfoVOS;
    }

    @Override
    @DS("save")
    public MiniProgramLoginVO switchRole(SwitchRoleParam param) {
        MiniProgramLoginVO miniProgramLoginVO = new MiniProgramLoginVO();
        String userId = param.getUserId();
        UserMiniProgram user = userMiniProgramMapper.selectByPrimaryKey(userId);
        if(user == null){
            UserPartner userPartner = userPartnerMapper.selectByPrimaryKey(userId);
            miniProgramLoginVO.setToken(generateTokenUserPartner(userPartner));
            miniProgramLoginVO.setUserId(userPartner.getUserId());
            miniProgramLoginVO.setOsUserId(userPartner.getUserId());
        }else{
            if (MiniRoleEnum.NORMAL.getType().equals(user.getRoleType()) || !"1".equals(user.getStatus())) {
                // 角色不对不许登录
                throw new BusinessException(StatusConstant.MINI_USER_NO_EXIST);
            }
            miniProgramLoginVO.setToken(generateToken(user));
            miniProgramLoginVO.setUserId(user.getId());
            miniProgramLoginVO.setOsUserId(user.getUserId());
            user.setLatestLoginTime(new Date());
            userMiniProgramMapper.updateByPrimaryKeySelective(user);
            //logService.recordOperateLog(ModuleEnum.LOGIN.code, LoginOperateEnum.MINI_PROGRAM_LOGIN.code, "-", user.getId(), 2, LogResultEnum.LOG_SUCESS.code, null);
            //分销员，渠道商登录时，将用户添加到分销用户表UserRetail，触发积分统计
            if (!ObjectUtils.isEmpty(user.getUserId()) && (StringUtils.equals(MiniRoleEnum.DISTRIBUTOR_FIRST.getType(), user.getRoleType())
                    || StringUtils.equals(MiniRoleEnum.DISTRIBUTOR_SECOND.getType(), user.getRoleType())
                    || StringUtils.equals(MiniRoleEnum.CHANNEL.getType(), user.getRoleType()))) {
                UserRetail userRetail = userRetailMapper.selectByPrimaryKey(user.getUserId());
                if (userRetail == null) {
                    userRetail = new UserRetail();
                    convertUserMini2UserRetail(user, userRetail);
                    userRetailMapper.insertSelective(userRetail);
                }
            }
        }

        return miniProgramLoginVO;
    }

    @Override
    @DS("save")
    public MiniProgramLoginVO loginOneClick(LoginOneClickParam param) {
        final String interfaceVersion = "1.0";
        final String appId = "************";
        final String traceId = UUID.randomUUID().toString().replaceAll("-", "");
        final String timestamp = new SimpleDateFormat("yyyyMMddHHmmssSSS").format(new Date());

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.add("interfaceVersion", interfaceVersion);
        headers.add("appId", appId);
        headers.add("traceId", traceId);
        headers.add("timestamp", timestamp);
        headers.add("businessType", "8");

        // 生成签名
        String stringToSign = appId + traceId + timestamp + param.getToken() + interfaceVersion;
        log.info("待签名数据：{}", stringToSign);
        LySign lySign = LySign.of(SignAlgorithm.SHA256withRSA, privateKeyStr, publicKeyStr, false);
        String sign = lySign.sign(stringToSign);
        log.info("签名数据：{}", sign);
        log.info("验证结果：{}", lySign.verify(stringToSign, sign));

        // 构建请求体
        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("token", param.getToken());
        requestBody.put("sign", sign);
        requestBody.put("userInformation", param.getUserInformation());
        HttpEntity<Map<String, Object>> httpEntity = new HttpEntity<>(requestBody, headers);

        String url = "https://www.cmpassport.com/h5/onekeylogin/tokenValidate";
        ResponseEntity<TokenValidateVO> responseEntity = restTemplate.postForEntity(url, httpEntity, TokenValidateVO.class);
        TokenValidateVO response = responseEntity.getBody();

        if ("103000".equals(response.getResultCode())) {
            String encryptedMsisdn = response.getData().getMsisdn();
            String phone = decryptMsisdn(encryptedMsisdn);
            log.info("login phone = {}", phone);
            List<UserMiniProgram> users = userMiniProgramMapper.selectByExample(new UserMiniProgramExample().createCriteria().andPhoneEqualTo(phone).andStatusEqualTo("1").example());
            // 找装维用户相关
            List<UserPartner> userPartnerList = userPartnerMapper.selectByExample(new UserPartnerExample()
                    .createCriteria()
                    .andRoleIdIn(Arrays.asList("1376576697935507456","1376576697935507457","1376576697935507458"))
                    .andPhoneEqualTo(IOTEncodeUtils.encryptIOTMessage(phone, encryptKey))
                    .andIsCancelEqualTo(false)
                    .andIsLogoffEqualTo(false)
                    .example());
            if (CollectionUtils.isEmpty(users)) {
                if(CollectionUtils.isEmpty(userPartnerList)){
                    throw new BusinessException(StatusConstant.MINI_USER_NO_EXIST);
                }
            }
            for (UserMiniProgram user : users) {
                if (MiniRoleEnum.NORMAL.getType().equals(user.getRoleType())) {
                    // 角色不对不许登录
                    continue;
                }
                // 判断账户是否可用
                if ("1".equals(user.getStatus())) {
                    MiniProgramLoginVO miniProgramLoginVO = new MiniProgramLoginVO();
                    miniProgramLoginVO.setToken(generateToken(user));
                    miniProgramLoginVO.setUserId(user.getId());
                    miniProgramLoginVO.setOsUserId(user.getUserId());
                    user.setLatestLoginTime(new Date());
                    userMiniProgramMapper.updateByPrimaryKeySelective(user);
                    //logService.recordOperateLog(ModuleEnum.LOGIN.code, LoginOperateEnum.MINI_PROGRAM_LOGIN.code, "-", user.getId(), 2, LogResultEnum.LOG_SUCESS.code, null);

                    //分销员，渠道商登录时，将用户添加到分销用户表UserRetail，触发积分统计
                    if (!ObjectUtils.isEmpty(user.getUserId()) && (StringUtils.equals(MiniRoleEnum.DISTRIBUTOR_FIRST.getType(), user.getRoleType())
                            || StringUtils.equals(MiniRoleEnum.DISTRIBUTOR_SECOND.getType(), user.getRoleType())
                            || StringUtils.equals(MiniRoleEnum.CHANNEL.getType(), user.getRoleType()))) {
                        UserRetail userRetail = userRetailMapper.selectByPrimaryKey(user.getUserId());
                        if (userRetail == null) {
                            userRetail = new UserRetail();
                            convertUserMini2UserRetail(user, userRetail);
                            userRetailMapper.insertSelective(userRetail);
                        }
                    }
                    return miniProgramLoginVO;
                }
            }
            if(CollectionUtils.isNotEmpty(userPartnerList)){
                MiniProgramLoginVO miniProgramLoginVO = new MiniProgramLoginVO();
                miniProgramLoginVO.setToken(generateTokenUserPartner(userPartnerList.get(0)));
                miniProgramLoginVO.setUserId(userPartnerList.get(0).getUserId());
                miniProgramLoginVO.setOsUserId(userPartnerList.get(0).getUserId());
                return miniProgramLoginVO;
            }
            throw new BusinessException(StatusConstant.MINI_USER_NO_EXIST);
        } else {
            log.error("一键登录失败，resultCode：{}，desc：{}", response.getResultCode(), response.getDesc());
            throw new BusinessException(response.getResultCode(), response.getDesc());
        }
    }

    // 解密 msisdn 方法
    private String decryptMsisdn(String encryptedMsisdn) {
        AES aes = new AES(getAesKey("283D92D1C8BF293E3AC5B8589D356E48"));
        String decryptedData = new String(aes.decryptBase64(encryptedMsisdn));
        log.info("解密后手机号：{}", decryptedData);
        return decryptedData;
    }

    private String getAesKey(String key) {
        if (org.apache.commons.lang3.StringUtils.isNotBlank(key)) {
            if (key.length() > 16) {
                return key.substring(0, 16);
            }
            return org.apache.commons.lang3.StringUtils.rightPad(key, 16, "0");
        }
        return org.apache.commons.lang3.StringUtils.EMPTY;
    }

    private UserMiniProgram convertFromShopManagerInfo(ShopManagerInfo info) {
        UserMiniProgram userMiniProgram = new UserMiniProgram();
        BeanUtils.copyProperties(info, userMiniProgram);
        userMiniProgram.setUserId(info.getUserId());
        userMiniProgram.setCode(info.getCreateOperCode());
        userMiniProgram.setNumber(info.getEmployeeNum());
        userMiniProgram.setName(info.getCustomerManagerName());
        userMiniProgram.setPhone(info.getCreateOperPhone());
        userMiniProgram.setRoleType("4");
        userMiniProgram.setStatus("1".equals(info.getMrgStatus()) ? "1" : "0");
        userMiniProgram.setCreateTime(info.getCreateTime());
        userMiniProgram.setUpdateTime(info.getUpdateTime());

        return userMiniProgram;
    }

    private UserMiniProgram convertFromShopCustomerInfo(ShopCustomerInfo info) {
        UserMiniProgram userMiniProgram = new UserMiniProgram();
        BeanUtils.copyProperties(info, userMiniProgram);
        userMiniProgram.setUserId(info.getUserId());
        userMiniProgram.setName(info.getCustName());
        userMiniProgram.setPhone(info.getCustId());
        userMiniProgram.setRoleType(info.getRoleType());
        userMiniProgram.setStatus("1".equals(info.getClientStatus()) ? "1" : "0");
        userMiniProgram.setCreateTime(info.getCreateTime());
        userMiniProgram.setUpdateTime(info.getUpdateTime());

        if ("1".equals(info.getRoleType()) || "2".equals(info.getRoleType())) {
            userMiniProgram.setCode(info.getDistributorReferralCode());
        } else if ("3".equals(info.getRoleType())) {
            userMiniProgram.setCode(info.getCustCode());
            userMiniProgram.setNumber(info.getAgentNumber());
        }

        return userMiniProgram;
    }

    /**
     * 检查文件是否是图片
     *
     * @param inputStream
     * @return
     */
    private static boolean isImage(InputStream inputStream) {
        try {
            BufferedImage bufferedImage = ImageIO.read(inputStream);
            if (bufferedImage == null) {
                return false;
            } else {
                return true;
            }
        } catch (IOException e) {
            return false;
        }
    }

    private String generateToken(UserMiniProgram user) {
        String userId = user.getId();
        log.info("UserRetail generateToken Eneter, userId = {}", userId);
        //生成token
        AccessToken accessToken = new AccessToken();
        accessToken.setUserId(userId);

        String token = JWTUtil.getToken(accessToken, null, null, null);
        LoginIfo4Redis loginIfo4Redis = new LoginIfo4Redis();
        loginIfo4Redis.setUserId(userId);
        loginIfo4Redis.setToken(token);
        loginIfo4Redis.setPhone(user.getPhone());
        loginIfo4Redis.setUserName(user.getName());
        loginIfo4Redis.setSystem(SYSTEM_MINI);//标识小程序用户
        loginIfo4Redis.setBeId(user.getBeId());//用户省归属
        loginIfo4Redis.setLocation(user.getLocation());//用户市归属
        loginIfo4Redis.setMallUserId(user.getUserId());//用户商城userId
        loginIfo4Redis.setRoleType(user.getRoleType());//用户角色
        loginIfo4Redis.setRoleName(MiniRoleEnum.getName(user.getRoleType(),user.getProvinceName(),
                user.getCityName(),user.getRegionName()));
        log.debug("小程序缓存的登录数据;{}", loginIfo4Redis);
        // 将权限信息放入redis
        redisTemplate.opsForValue().set(Constant.REDIS_KEY_USER_TOKEN + userId, loginIfo4Redis, 3, TimeUnit.HOURS);
        return token;
    }

    @Override
    @DS("query")
    public SaleYearReportVO getSaleYearReport(Integer year, LoginIfo4Redis loginIfo4Redis) {
        //异步调用添加年度报告访问标记，要调用主库
        asyncAddUserViewSaleReport(year, loginIfo4Redis.getMallUserId());
        SaleYearReportVO vo = redisCacheUtil.loadAndCache(
                Constant.REDIS_KEY_MINI_USER_SALE_YEAR_REPORT + year + ":" + loginIfo4Redis.getMallUserId(),
                RedisLockConstant.LOCK_MINI_USER_SALE_REPORT + year + ":" + loginIfo4Redis.getMallUserId(),
                30, TimeUnit.DAYS,
                () -> {
                    SaleYearReportVO result = new SaleYearReportVO();
                    List<MiniProgramSaleYearReport> reports = miniProgramSaleYearReportMapper.selectByExample(new MiniProgramSaleYearReportExample()
                            .createCriteria().andYearEqualTo(year).andUserIdEqualTo(loginIfo4Redis.getMallUserId()).example());
                    Long userCount = miniProgramSaleYearReportMapper.countByExample(
                            new MiniProgramSaleYearReportExample().createCriteria()
                                    .andOrderTotalCountGreaterThan(0L)
                                    .example()
                    );
                    if (CollectionUtils.isEmpty(reports)) {
                        return null;
                    }
                    BeanUtils.copyProperties(reports.get(0), result);
                    if (userCount != null && result.getSaleRanking() != null) {
                        result.setSaleRanking(((userCount - result.getSaleRanking() + 1) * 10000 / userCount));
                    }
                    return result;
                });
        if (vo == null) {
            vo = new SaleYearReportVO();
            UserMiniProgram userMiniProgram = redisCacheUtil.loadAndCache(
                    Constant.REDIS_KEY_MINI_USER + loginIfo4Redis.getMallUserId(),
                    RedisLockConstant.LOCK_MINI_USER + loginIfo4Redis.getMallUserId(),
                    1,
                    TimeUnit.DAYS,
                    () -> userMiniProgramMapper.selectByPrimaryKey(loginIfo4Redis.getMallUserId())
            );
            if (userMiniProgram == null) {
                throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "用户不存在" + year + "年的年度销售报告");
            }
            vo.setUserId(userMiniProgram.getUserId());
            vo.setName(userMiniProgram.getName());
            vo.setRegisterTime(userMiniProgram.getCreateTime());
            vo.setRoleType(userMiniProgram.getRoleType());
            vo.setProvinceCode(userMiniProgram.getBeId());
            vo.setProvinceName(userMiniProgram.getProvinceName());
            vo.setCityCode(userMiniProgram.getLocation());
            vo.setCityName(userMiniProgram.getCityName());
            vo.setHasReport(false);
        } else {
            vo.setHasReport(true);
        }
        return vo;
    }

    @Override
    @DS("query")
    public void saleReportLoad2Redis(Integer year, LoginIfo4Redis loginIfo4Redis) {
        List<MiniProgramSaleYearReport> reports = miniProgramSaleYearReportMapper.selectByExample(new MiniProgramSaleYearReportExample()
                .createCriteria().andYearEqualTo(year).example());
        Long userCount = userMiniProgramMapper.countByExample(new UserMiniProgramExample());
        if (CollectionUtils.isEmpty(reports)) {
            return;
        }
        reports.forEach(x -> {
            SaleYearReportVO vo = new SaleYearReportVO();
            BeanUtils.copyProperties(x, vo);
            if (userCount != null && vo.getSaleRanking() != null) {
                vo.setSaleRanking(((userCount - vo.getSaleRanking() + 1) * 10000 / userCount));
            }
            String key = Constant.REDIS_KEY_MINI_USER_SALE_YEAR_REPORT + year + ":" + x.getUserId();
            redisTemplate.opsForValue().set(key, vo, 30, TimeUnit.DAYS);
        });

    }

    /**
     * 生成小程序用户年度销售报告
     *
     * @param year
     */
    @Override
    @DS("save")
    @Transactional(rollbackFor = Exception.class)
    public void generateSaleReport(Integer year) {
        Map<String, MiniProgramSaleYearReport> reportMap = new HashMap<>();
        // 年度报告基础信息
        selectSaleYearReportBasic(year, reportMap);
        // 年度销售订单笔数、年度销售排名
        selectSaleYearReportOrder(year, reportMap);
        // 年度销售订单金额
        selectSaleYearReportOrderPrice(year, reportMap);
        // 年度销售最好单品商品编码、名称和订单数
        selectSaleYearReportSpu(year, reportMap);
        selectSaleYearReportSpuGroup(year, reportMap);
        selectSaleYearReportSpuIndividual(year, reportMap);
        // 最晚一笔订单成交时间
        selectSaleYearReportLastOrderTime(year, reportMap);
        // 发展客户数
        selectSaleYearReportCustomerCount(year, reportMap);

        // 批量插入年度报告
        List<MiniProgramSaleYearReport> reports = new ArrayList<>(reportMap.values());
        batchInsertSaleYearReport(year, reports);
    }

    @Override
    public void importActivityPaticipants(Integer year, MultipartFile excel) {
        try {
            List<MiniProgramActivityPaticipantsImportExcelDTO> datas = EasyExcel.read(excel.getInputStream(), MiniProgramActivityPaticipantsImportExcelDTO.class, null).sheet(0).headRowNumber(1).doReadSync();
            if (CollectionUtils.isEmpty(datas)) {
                throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "导入数据为空");
            }
            Map<String, MiniProgramActivityPaticipantsImportExcelDTO> codeMap = datas.stream().collect(Collectors.toMap(MiniProgramActivityPaticipantsImportExcelDTO::getCode, Function.identity()));
            List<UserMiniProgram> userMiniPrograms = userMiniProgramMapper.selectByExample(
                    new UserMiniProgramExample().createCriteria()
                            .andUserIdIsNotNull()
                            .andCodeIn(new ArrayList<>(codeMap.keySet()))
                            .example()
            );
            Map<String, MiniProgramActivityPaticipantsImportExcelDTO> userIdMap = new HashMap<>();
            for (UserMiniProgram userMiniProgram : userMiniPrograms) {
                if (!ObjectUtils.isEmpty(userMiniProgram.getUserId())) {
                    userIdMap.put(userMiniProgram.getUserId(), codeMap.get(userMiniProgram.getCode()));
                }
            }

            List<String> userIds = new ArrayList<>(userIdMap.keySet());
            MiniProgramSaleYearReportExample.Criteria criteria = MiniProgramSaleYearReportExample.newAndCreateCriteria();
            criteria.andYearEqualTo(year);
            if (CollectionUtils.isNotEmpty(userIds)) {
                criteria.andUserIdIn(userIds);
            }
            List<MiniProgramSaleYearReport> reports = miniProgramSaleYearReportMapper.selectByExample(criteria.example());
            Map<String, MiniProgramSaleYearReport> reportMap = reports.stream().collect(Collectors.toMap(MiniProgramSaleYearReport::getUserId, Function.identity()));
            reportMap.forEach((k, v) -> {
                MiniProgramActivityPaticipantsImportExcelDTO dto = userIdMap.get(k);
                if (dto != null) {
                    v.setJoinMallActivityCount(Integer.valueOf(dto.getJoinMallActivityCount() != null ? dto.getJoinMallActivityCount() : "0"));
                    v.setJoinMallActivityRewardCount(Integer.valueOf(dto.getJoinMallActivityRewardCount() != null ? dto.getJoinMallActivityRewardCount() : "0"));
                    miniProgramSaleYearReportMapper.updateByPrimaryKeySelective(v);
                }
            });
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public void importSpuCategoryInfo(MultipartFile excel) {
        try {
            List<MiniProgramSpuCategoryInfoImportDTO> datas = EasyExcel.read(excel.getInputStream(), MiniProgramSpuCategoryInfoImportDTO.class, null).sheet(0).headRowNumber(1).doReadSync();
            if (CollectionUtils.isEmpty(datas)) {
                throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "导入数据为空");
            }
            spuCategoryInfoMapper.deleteByExample(new SpuCategoryInfoExample());
            int number = 0;
            List<SpuCategoryInfo> spuCategoryInfos = new ArrayList<>();
            for (MiniProgramSpuCategoryInfoImportDTO data : datas) {
                String spuName = data.getSpuName();
                String spuCode = data.getSpuCode();
                String category = data.getCategory();
                if (StringUtils.isBlank(spuName) || StringUtils.isBlank(spuCode) || StringUtils.isBlank(category)) {
                    throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "数据格式异常");
                }

                SpuCategoryInfo spuCategoryInfo = new SpuCategoryInfo();
                spuCategoryInfo.setId(BaseServiceUtils.getId());
                spuCategoryInfo.setSpuCode(spuCode);
                spuCategoryInfo.setSpuName(spuName);
                spuCategoryInfo.setCategory(category);
                spuCategoryInfos.add(spuCategoryInfo);
            }
            spuCategoryInfoMapper.batchInsert(spuCategoryInfos);
            log.info("小程序年度报告产品类别导入成功，共{}条", number);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 查询年度报告基础信息（user_id、报告年份、注册顺序、注册时间、用户角色、省份编码、省份名称、城市编码、城市名称、第一笔订单时间）
     */
    private void selectSaleYearReportBasic(int year, Map<String, MiniProgramSaleYearReport> reportMap) {
        List<SaleYearReportVO> saleYearReportVOS = userMinProgramMapperExt.selectSaleYearReportBasic(year);
        if (CollectionUtils.isEmpty(saleYearReportVOS)) {
            return;
        }
        for (SaleYearReportVO item : saleYearReportVOS) {
            MiniProgramSaleYearReport report = new MiniProgramSaleYearReport();
            BeanUtils.copyProperties(item, report);
            report.setId(BaseServiceUtils.getId());
            reportMap.put(report.getUserId(), report);
        }
    }

    /**
     * 年度销售订单笔数、总积分、预付费订单数、后付费订单数、融合营销订单数、年度销售排名
     */
    private void selectSaleYearReportOrder(int year, Map<String, MiniProgramSaleYearReport> reportMap) {
        List<SaleYearReportVO> saleYearReportVOS = userMinProgramMapperExt.selectSaleYearReportOrder(year);
        if (CollectionUtils.isEmpty(saleYearReportVOS) || MapUtils.isEmpty(reportMap)) {
            return;
        }
        for (SaleYearReportVO item : saleYearReportVOS) {
            MiniProgramSaleYearReport report = reportMap.get(item.getUserId());
            if (report != null) {
                report.setOrderTotalCount(item.getOrderTotalCount());
                report.setOrderTotalPoint(item.getOrderTotalPoint());
                report.setOrderTotalCountPrepay(item.getOrderTotalCountPrepay());
                report.setOrderTotalCountAfterpay(item.getOrderTotalCountAfterpay());
                report.setOrderTotalCountMix(item.getOrderTotalCountMix());
                report.setSaleRanking(item.getSaleRanking());
            }
        }
    }

    /**
     * 年度销售订单金额
     */
    private void selectSaleYearReportOrderPrice(int year, Map<String, MiniProgramSaleYearReport> reportMap) {
        List<SaleYearReportVO> saleYearReportVOS = userMinProgramMapperExt.selectSaleYearReportOrderPrice(year);
        if (CollectionUtils.isEmpty(saleYearReportVOS) || MapUtils.isEmpty(reportMap)) {
            return;
        }
        for (SaleYearReportVO item : saleYearReportVOS) {
            MiniProgramSaleYearReport report = reportMap.get(item.getUserId());
            if (report != null) {
                report.setOrderTotalPrice(item.getOrderTotalPrice());
            }
        }
    }

    /**
     * 年度销售最好单品商品编码、名称、订单数、商品款数
     */
    private void selectSaleYearReportSpu(int year, Map<String, MiniProgramSaleYearReport> reportMap) {
        List<SaleYearReportVO> saleYearReportVOS = userMinProgramMapperExt.selectSaleYearReportSpu(year);
        if (CollectionUtils.isEmpty(saleYearReportVOS) || MapUtils.isEmpty(reportMap)) {
            return;
        }
        for (SaleYearReportVO item : saleYearReportVOS) {
            MiniProgramSaleYearReport report = reportMap.get(item.getUserId());
            if (report != null) {
                report.setBestSaleSpuCode(item.getBestSaleSpuCode());
                report.setBestSaleSpuName(item.getBestSaleSpuName());
                report.setBestSaleSpuOrderCount(item.getBestSaleSpuOrderCount());
                report.setSpuTotalCount(item.getSpuTotalCount());

                List<SpuCategoryInfo> spuCategoryInfos = spuCategoryInfoMapper.selectByExample(
                        new SpuCategoryInfoExample().createCriteria()
                                .andSpuCodeEqualTo(item.getBestSaleSpuCode())
                                .example()
                );
                if (CollectionUtils.isNotEmpty(spuCategoryInfos)) {
                    SpuCategoryInfo spuCategoryInfo = spuCategoryInfos.get(0);
                    report.setBestSaleSpuPrompt(spuCategoryInfo.getCategory());
                } else {
                    report.setBestSaleSpuPrompt(replaceSpuName(item.getBestSaleSpuName()));
                }
            }
        }
    }

    /**
     * 年度集团客户购买的最多产品编码、名称、次数
     */
    private void selectSaleYearReportSpuGroup(int year, Map<String, MiniProgramSaleYearReport> reportMap) {
        List<SaleYearReportVO> saleYearReportVOS = userMinProgramMapperExt.selectSaleYearReportSpuGroup(year);
        if (CollectionUtils.isEmpty(saleYearReportVOS) || MapUtils.isEmpty(reportMap)) {
            return;
        }
        for (SaleYearReportVO item : saleYearReportVOS) {
            MiniProgramSaleYearReport report = reportMap.get(item.getUserId());
            if (report != null) {
                report.setBestSaleSpuCodeGroup(item.getBestSaleSpuCodeGroup());
                report.setBestSaleSpuNameGroup(item.getBestSaleSpuNameGroup());
                report.setBestSaleSpuCountGroup(item.getBestSaleSpuCountGroup());
            }
        }
    }

    /**
     * 年度个人客户购买的最多产品编码、名称、次数
     */
    private void selectSaleYearReportSpuIndividual(int year, Map<String, MiniProgramSaleYearReport> reportMap) {
        List<SaleYearReportVO> saleYearReportVOS = userMinProgramMapperExt.selectSaleYearReportSpuIndividual(year);
        if (CollectionUtils.isEmpty(saleYearReportVOS) || MapUtils.isEmpty(reportMap)) {
            return;
        }
        for (SaleYearReportVO item : saleYearReportVOS) {
            MiniProgramSaleYearReport report = reportMap.get(item.getUserId());
            if (report != null) {
                report.setBestSaleSpuCodeIndividual(item.getBestSaleSpuCodeIndividual());
                report.setBestSaleSpuNameIndividual(item.getBestSaleSpuNameIndividual());
                report.setBestSaleSpuCountIndividual(item.getBestSaleSpuCountIndividual());
            }
        }
    }

    /**
     * 最晚一笔订单时间、最后一笔订单时间
     */
    private void selectSaleYearReportLastOrderTime(int year, Map<String, MiniProgramSaleYearReport> reportMap) {
        List<SaleYearReportVO> saleYearReportVOS = userMinProgramMapperExt.selectSaleYearReportLastOrderTime(year);
        if (CollectionUtils.isEmpty(saleYearReportVOS) || MapUtils.isEmpty(reportMap)) {
            return;
        }
        for (SaleYearReportVO item : saleYearReportVOS) {
            MiniProgramSaleYearReport report = reportMap.get(item.getUserId());
            if (report != null) {
                report.setYearLatestOrderTime(item.getYearLatestOrderTime());
                report.setYearLastOrderTime(item.getYearLastOrderTime());
            }
        }
    }

    /**
     * 年度客户总数、年度个人客户数、年度集团客户数、发展客户总数
     */
    private void selectSaleYearReportCustomerCount(int year, Map<String, MiniProgramSaleYearReport> reportMap) {
        List<SaleYearReportVO> saleYearReportVOS = userMinProgramMapperExt.selectSaleYearReportCustomerCount(year);
        if (CollectionUtils.isEmpty(saleYearReportVOS) || MapUtils.isEmpty(reportMap)) {
            return;
        }
        for (SaleYearReportVO item : saleYearReportVOS) {
            MiniProgramSaleYearReport report = reportMap.get(item.getUserId());
            if (report != null) {
                report.setCustomerCount(item.getCustomerCount());
                report.setCustomerCountIndividual(item.getCustomerCountIndividual());
                report.setCustomerCountGroup(item.getCustomerCountGroup());
                report.setCustomerTotalCount(item.getCustomerTotalCount());
            }
        }
    }


    /**
     * 批量插入年度报告
     */
    private void batchInsertSaleYearReport(int year, List<MiniProgramSaleYearReport> reports) {
        miniProgramSaleYearReportMapper.deleteByExample(
                new MiniProgramSaleYearReportExample().createCriteria()
                        .andYearEqualTo(year)
                        .example()
        );
        if (!CollectionUtils.isEmpty(reports)) {
            final int batchSize = 1000;
            final int reportsSize = reports.size();
            for (int i = 0; i < reportsSize; i += batchSize) {
                int end = Math.min(i + batchSize, reportsSize);
                miniProgramSaleYearReportMapper.batchInsert(reports.subList(i, end));
            }
        }
    }

    @Override
    @DS("query")
    public Boolean isSaleReportViewed(Integer year, LoginIfo4Redis loginIfo4Redis) {
        List<String> viewUserIds = redisCacheUtil.loadAndCache(
                Constant.REDIS_KEY_MINI_VIEWED_SALE_YEAR_REPORT + year,
                RedisLockConstant.LOCK_MINI_VIEWED_SALE_REPORT + year,
                30, TimeUnit.DAYS,
                () -> {
                    List<MiniProgramSaleYearReportViewTag> tags = miniProgramSaleYearReportViewTagMapper.selectByExample(
                            new MiniProgramSaleYearReportViewTagExample()
                                    .createCriteria().andYearEqualTo(year).example());
                    if (CollectionUtils.isEmpty(tags)) {
                        return null;
                    }
                    List<String> result = tags.stream().map(MiniProgramSaleYearReportViewTag::getUserId).collect(Collectors.toList());
                    return result;
                });

        return CollectionUtils.isNotEmpty(viewUserIds) && viewUserIds.contains(loginIfo4Redis.getMallUserId());
    }

    @Override
    public UserLocationVO getLocationByUserId(String id) {
        UserMiniProgram userMiniProgram = redisCacheUtil.loadAndCache(
                Constant.REDIS_KEY_MINI_USER + id,
                RedisLockConstant.LOCK_MINI_USER + id,
                1,
                TimeUnit.DAYS,
                () -> userMiniProgramMapper.selectByPrimaryKey(id)
        );
        if (userMiniProgram == null) {
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "用户不存在");
        }
        UserLocationVO userLocationVO = new UserLocationVO();
        userLocationVO.setProvinceName(StringUtils.defaultString(userMiniProgram.getProvinceName(),"全国"));
        userLocationVO.setCityName(userMiniProgram.getCityName());
        return userLocationVO;
    }

    @Override
    public void clearCache(ClearCacheParam param) {
        if (Boolean.TRUE.equals(param.getAll())) {
            redisCacheUtil.deleteAll(param.getKey());
        } else {
            redisCacheUtil.delete(param.getKey());
        }
    }

    private void asyncAddUserViewSaleReport(Integer year, String userId) {
        executorService.execute(() -> {
            List<MiniProgramSaleYearReportViewTag> tags = miniProgramSaleYearReportViewTagMapper.selectByExample(
                    new MiniProgramSaleYearReportViewTagExample()
                            .createCriteria().andYearEqualTo(year).andUserIdEqualTo(userId).example());
            if (CollectionUtils.isEmpty(tags)) {
                MiniProgramSaleYearReportViewTag tag = new MiniProgramSaleYearReportViewTag();
                tag.setUserId(userId);
                tag.setId(BaseServiceUtils.getId());
                tag.setYear(year);
                tag.setCreateTime(new Date());
                miniProgramSaleYearReportViewTagMapper.insertSelective(tag);

                redisCacheUtil.delete(Constant.REDIS_KEY_MINI_VIEWED_SALE_YEAR_REPORT + year);
            }
        });
    }

    private String replaceSpuName(String spuName) {
        if (StringUtils.isBlank(spuName)) {
            return "";
        }
        for (String s : categories) {
            if (spuName.contains(s)) {
                return s;
            }
        }
        return spuName;
    }

    @Override
    @DS("save")
    public void importBMUser(MultipartFile excel) {
        try {
            List<MiniBMUserImportDTO> datas = EasyExcel.read(excel.getInputStream(), MiniBMUserImportDTO.class, null).sheet(0).headRowNumber(1).doReadSync();
            if (CollectionUtils.isEmpty(datas)) {
                throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "导入数据为空");
            }
            int number = 0;
            List<UserMiniProgram> bmUsers = new ArrayList<>();
            Date now = new Date();
            for (MiniBMUserImportDTO data : datas) {
                if (StringUtils.isBlank(data.getPhone()) || StringUtils.isBlank(data.getName()) || StringUtils.isBlank(data.getRoleType())) {
                    throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "手机号、姓名、角色必填");
                }
                UserMiniProgram userMiniProgram = new UserMiniProgram();
                userMiniProgram.setId(BaseServiceUtils.getId());
                userMiniProgram.setPhone(data.getPhone());
                userMiniProgram.setName(data.getName());
                userMiniProgram.setStatus("1");
                userMiniProgram.setCreateTime(now);
                userMiniProgram.setUpdateTime(now);
                String type = MiniRoleEnum.getType(data.getRoleType());
                if (StringUtils.isBlank(type)) {
                    throw new BusinessException(BaseErrorConstant.PARAM_ERROR, (number + 2) + "行角色类型" + data.getRoleType() + "异常");
                }
                userMiniProgram.setRoleType(type);
                if (StringUtils.isNotBlank(data.getProvinceName())) {
                    userMiniProgram.setProvinceName(data.getProvinceName());
                    userMiniProgram.setBeId(provinceCityConfig.getProvinceNameCodeMap().get(data.getProvinceName()));
                }
                if (StringUtils.isNotBlank(data.getCityName())) {
                    userMiniProgram.setCityName(data.getCityName());
                    userMiniProgram.setLocation(provinceCityConfig.getCityNameCodeMap().get(data.getCityName()));
                }
                if (StringUtils.isNotBlank(data.getRegionName())) {
                    userMiniProgram.setRegionName(data.getRegionName());
                    userMiniProgram.setRegionId(provinceCityConfig.getRegionNameCodeMap().get(userMiniProgram.getLocation() + data.getRegionName()));
                }

                bmUsers.add(userMiniProgram);

                number++;
            }

            if (CollectionUtils.isNotEmpty(bmUsers)) {
                userMiniProgramMapper.batchInsert(bmUsers);
            }


            log.info("小程序业管用户导入成功，共{}条", number);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    @DS("save")
    public void create(UserMiniProgramParam param) {
        if (StringUtils.equals(param.getRoleType(), MiniRoleEnum.PROVINCE.getType())
                && StringUtils.isBlank(param.getBeId())) {
                throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "请选择所属省份");
        }
        if (StringUtils.equals(param.getRoleType(), MiniRoleEnum.CITY.getType())
                && (StringUtils.isBlank(param.getBeId())|| StringUtils.isBlank(param.getLocation()))) {
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "请选择所属城市");
        }
        if (StringUtils.equals(param.getRoleType(), MiniRoleEnum.REGION.getType())
                && (StringUtils.isBlank(param.getBeId()) || StringUtils.isBlank(param.getLocation()) || StringUtils.isBlank(param.getRegionId()))) {
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "请选择所属区域");
        }

        UserMiniProgram userMiniProgram = new UserMiniProgram();
        BeanUtils.copyProperties(param, userMiniProgram);
        userMiniProgram.setId(BaseServiceUtils.getId());
        userMiniProgram.setStatus("1");
        userMiniProgram.setCreateTime(new Date());
        userMiniProgram.setUpdateTime(new Date());

        userMiniProgramMapper.insertSelective(userMiniProgram);
    }

    @Override
    public String generateTokenUserPartner(UserPartner user) {
        String userId = user.getUserId();
        //生成token
        AccessToken accessToken = new AccessToken();
        accessToken.setUserId(userId);
        //由于存在账号的注销等操作，这样就需要将注销用户踢下线，所以需要将登陆信息保存到redis
        //这里只是使用jwt作为生产token的工具，验证还是在redis验证
        String token = JWTUtil.getToken(accessToken, null, null, null);

        LoginIfo4Redis loginIfo4Redis = new LoginIfo4Redis();
        loginIfo4Redis.setUserId(userId);
        loginIfo4Redis.setToken(token);
        loginIfo4Redis.setIsAdmin(false);
        loginIfo4Redis.setRoleId(user.getRoleId());
        loginIfo4Redis.setIsSend(user.getIsSend());
        String phone = IOTEncodeUtils.decryptIOTMessage(user.getPhone(), encryptKey);
        loginIfo4Redis.setPhone(org.apache.commons.lang3.StringUtils.defaultIfBlank(phone, "空白手机号"));
        loginIfo4Redis.setUserName(user.getName());
        loginIfo4Redis.setPartnerName(user.getPartnerName());
        loginIfo4Redis.setCompanyType(user.getCompanyType());
        if (com.baomidou.mybatisplus.core.toolkit.ObjectUtils.isNotEmpty(user.getIsPrimary())) {
            loginIfo4Redis.setIsPrimary(user.getIsPrimary());
        }
        // 封装loginIfo4Redis
        // 查询角色信息

        RoleInfo roleInfo = roleInfoMapper.selectByPrimaryKey(user.getRoleId());
        loginIfo4Redis.setRoleType(roleInfo.getRoleType());
        loginIfo4Redis.setRoleName(roleInfo.getName());
        // 设置退换货地址flag标识符
        Boolean isReAddrNull = false;

        loginIfo4Redis.setIsReAddrNull(isReAddrNull);

        log.info("缓存的登录数据;{}", loginIfo4Redis);
        //将权限信息放入redis
        redisTemplate.opsForValue().set(Constant.REDIS_KEY_USER_TOKEN + userId, loginIfo4Redis, 24, TimeUnit.HOURS);

        return token;
    }

    @Override
    public void saveUserAgreement(SaveUserAgreementParam param, LoginIfo4Redis loginIfo4Redis) {
        Date now = new Date();
        String userId = loginIfo4Redis.getUserId();
        String id = param.getId();
        boolean update = false;
        String type = null;
        if (StringUtils.isNotEmpty(id)) {
            //更新
            update = true;
            UserAgreement userAgreement = userAgreementMapper.selectByPrimaryKey(id);
            if(userAgreement == null){
                throw new BusinessException(StatusContant.MINI_PROGRAM_AGREEMENT_NOT_EXIST);
            }
            if(
                    param.getStatus().intValue() == UserAgreementStatusEnum.IN_PROGRESS.getStatus().intValue()
            ||param.getStatus().intValue() == UserAgreementStatusEnum.DRAFT .getStatus().intValue()
            ){
                Object o = stringRedisTemplate.opsForHash().get(REDIS_USER_AGREENT_KEY, id);
                if(o == null){
                    //缓存提交审核前的数据，用于前端展示(多次修改时，只有第一次修改的时候才缓存，因为那时候的数据才是已发布的)
                    stringRedisTemplate.opsForHash().put(REDIS_USER_AGREENT_KEY,id,JSON.toJSONString(userAgreement));
                }
                if(param.getStatus().intValue() == UserAgreementStatusEnum.IN_PROGRESS.getStatus().intValue()){
                    userAgreement.setSubmitAuditTime(now);
                    userAgreement.setAuditStatus(UserAgreementAuditStatusEnum.IN_PROGRESS.getStatus());
                }
            }
            userAgreement.setContent(param.getContent());
            userAgreement.setStatus(param.getStatus());
            userAgreement.setUpdateTime(now);
            userAgreementMapper.updateByPrimaryKeySelective(userAgreement);
        }else {
            //新增
            UserAgreement userAgreement = new UserAgreement();
            BeanUtils.copyProperties(param,userAgreement);
            userAgreement.setId(BaseServiceUtils.getId());
            userAgreement.setCreateTime(now);
            userAgreement.setUpdateTime(now);
            if(param.getStatus().intValue() == UserAgreementStatusEnum.IN_PROGRESS.getStatus().intValue()){
                userAgreement.setSubmitAuditTime(now);
                userAgreement.setAuditStatus(UserAgreementAuditStatusEnum.IN_PROGRESS.getStatus());
            }
            if(param.getType() == null){
                throw new BusinessException(BaseErrorConstant.PARAM_ERROR,"新增协议必须传递type");
            }
            if(param.getType() == 1){
                userAgreement.setName("用户协议");
                type = "用户协议";
            }
            if(param.getType() == 2){
                userAgreement.setName("隐私政策");
                type = "隐私政策";
            }
            userAgreementMapper.insertSelective(userAgreement);
        }
        //记录日志
        logService.recordOperateLog(ModuleEnum.MINI_PROGRAM.code, MiniProgramOperateEnum.USER_AGREEMENT.code, "【"+(update ? "编辑" : "新增")+"】\n" + type + "内容为：\n" + param.getContent(), userId, 0, LogResultEnum.LOG_SUCESS.code, null);
    }

    @Override
    public void auditUserAgreement(AuditUserAgreementParam param) {
        Boolean pass = param.getPass();
        List<String> idList = param.getIdList();
        Date now = new Date();
        UserAgreement userAgreement = new UserAgreement();
        userAgreement.setStatus(pass ? UserAgreementStatusEnum.PASSED.getStatus() : UserAgreementStatusEnum.DENIED.getStatus());
        userAgreement.setAuditStatus(pass ? UserAgreementAuditStatusEnum.PASSED.getStatus() : UserAgreementAuditStatusEnum.DENIED.getStatus());
        userAgreement.setUpdateTime(now);
        UserAgreementExample example = new UserAgreementExample().createCriteria().andIdIn(idList).example();
        if(pass){
            idList.stream().forEach(id->{
                //清掉redis缓存
                stringRedisTemplate.opsForHash().delete(REDIS_USER_AGREENT_KEY,id);
            });
        }
        userAgreementMapper.updateByExampleSelective(userAgreement, example);
    }

    @Override
    public void userAgreementOffline(UserAgreementIdParam param) {
/*        String id = param.getId();
        Date now = new Date();
        UserAgreement userAgreement = userAgreementMapper.selectByPrimaryKey(id);
        if(userAgreement == null){
            throw new BusinessException(StatusContant.MINI_PROGRAM_AGREEMENT_NOT_EXIST);
        }
        if(userAgreement.getStatus().intValue() == UserAgreementStatusEnum.OFFLINE.getStatus().intValue()){
            //已下线，不处理
            return;
        }
        if(userAgreement.getStatus().intValue() != UserAgreementStatusEnum.PASSED.getStatus().intValue()){
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR,"只有【已发布】状态才可以下线");
        }
        userAgreement.setStatus(UserAgreementStatusEnum.OFFLINE.getStatus());
        userAgreement.setUpdateTime(now);
        userAgreementMapper.updateByPrimaryKeySelective(userAgreement);*/
    }

    @Override
    public void deleteUserAgreement(UserAgreementIdParam param) {
/*        String id = param.getId();
        UserAgreement userAgreement = userAgreementMapper.selectByPrimaryKey(id);
        if(userAgreement == null){
            //已删除,不处理
            return;
        }
        if(
                userAgreement.getStatus().intValue() != UserAgreementStatusEnum.OFFLINE.getStatus().intValue()
                        && userAgreement.getStatus().intValue() != UserAgreementStatusEnum.DRAFT.getStatus().intValue()
                        &&  userAgreement.getStatus().intValue() != UserAgreementStatusEnum.DENIED.getStatus().intValue()
        ){
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR,"只有【已下线】,【待发布】,【已驳回】状态才可以删除");
        }
        userAgreementMapper.deleteByPrimaryKey(id);*/
    }

    @Override
    public void submitAudit(UserAgreementIdParam param) {
        String id = param.getId();
        Date now = new Date();
        UserAgreement userAgreement = userAgreementMapper.selectByPrimaryKey(id);
        if(userAgreement == null){
            throw new BusinessException(StatusContant.MINI_PROGRAM_AGREEMENT_NOT_EXIST);
        }
        userAgreement.setUpdateTime(now);
        userAgreement.setSubmitAuditTime(now);
        userAgreement.setStatus(UserAgreementStatusEnum.IN_PROGRESS.getStatus());
        userAgreement.setAuditStatus(UserAgreementAuditStatusEnum.IN_PROGRESS.getStatus());
        userAgreementMapper.updateByPrimaryKeySelective(userAgreement);
    }
}
