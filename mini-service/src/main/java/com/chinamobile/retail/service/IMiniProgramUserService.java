package com.chinamobile.retail.service;


import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.entity.retail.FindMiniProgramUserParam;
import com.chinamobile.iot.sc.mode.LoginIfo4Redis;
import com.chinamobile.iot.sc.mode.PageData;
import com.chinamobile.retail.pojo.entity.UserMiniProgram;
import com.chinamobile.retail.pojo.entity.UserMiniProgramAddress;
import com.chinamobile.retail.pojo.entity.UserPartner;
import com.chinamobile.retail.pojo.param.miniprogram.*;
import com.chinamobile.retail.pojo.vo.UserCenterVO;
import com.chinamobile.retail.pojo.vo.miniprogram.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/5/29 16:09
 * @description TODO
 */
public interface IMiniProgramUserService {

    /**
     * 更新用户姓名
     *
     * @param updateUserNameParam
     */
    void updateUserName(UpdateUserNameParam updateUserNameParam);

    /**
     * 更新用户头像
     *
     * @param file
     * @param userId
     */
    void uploadUserHeader(MultipartFile file, String userId);

    /**
     * 获取用户中心信息
     *
     * @param userId
     * @return
     */
    UserCenterVO getUserCenterInfo(String userId);

    /**
     * 根据id获取用户信息
     * @param id
     * @return
     */
    UserMiniProgram getUserMiniProgramById(String id);

    /**
     * 根据主键更新所选字段
     *
     * @param userMiniProgram
     */
    void updateUserSelectedById(UserMiniProgram userMiniProgram);

    /**
     * 小程序发送短信验证码
     * @param phone
     */
    void getLoginSmsValidCode(String phone);

    /**
     * 小程序短信验证码登录
     * @param phone
     * @return
     */
    MiniProgramLoginVO loginBySmsCode(String phone);

    String findUserId(FindMiniProgramUserParam param);

    /**
     * 首次上线迁移客户经理、分销员、渠道商账号到user_mini_program
     * @return
     */
    String migrateFromManageAndCustomer();
    void customerEncryption();

    /**
     * 查看用户协议
     * @return
     */
    PageData<UserAgreementListVO> userAgreementList(UserAgreementListParam param, boolean isMini);

    /**
     * 编辑用户协议
     * @param params
     */
    void updateUserAgreement(List<UpdateUserAgreementParam> params);
    BaseAnswer<Void> addAddress(ActivityAwardAddressParam param, String userId);
    BaseAnswer<Void> editAddress(ActivityAwardAddressParam param, String userId);
    BaseAnswer<Void> deleteAddress(String id);
    BaseAnswer<List<UserMiniProgramAddress>> getAddress(String userId);

    List<UserRoleInfoVO> listUserRole(String userId);

    MiniProgramLoginVO switchRole(SwitchRoleParam param);

    MiniProgramLoginVO loginOneClick(LoginOneClickParam param);

    SaleYearReportVO getSaleYearReport(Integer year, LoginIfo4Redis loginIfo4Redis);
    void saleReportLoad2Redis(Integer year, LoginIfo4Redis loginIfo4Redis);

    void generateSaleReport(Integer year);

    void importActivityPaticipants(Integer year, MultipartFile excel);
    void importSpuCategoryInfo(MultipartFile excel);

    Boolean isSaleReportViewed(Integer year, LoginIfo4Redis loginIfo4Redis);

    UserLocationVO getLocationByUserId(String id);

    void clearCache(ClearCacheParam param);

    void importBMUser(MultipartFile excel);

    void create(UserMiniProgramParam param);

    String generateTokenUserPartner(UserPartner user);

    void saveUserAgreement(SaveUserAgreementParam param,LoginIfo4Redis loginIfo4Redis);

    void auditUserAgreement(@Valid AuditUserAgreementParam param);

    void userAgreementOffline(UserAgreementIdParam param);

    void deleteUserAgreement(UserAgreementIdParam param);

    void submitAudit(UserAgreementIdParam param);
}
