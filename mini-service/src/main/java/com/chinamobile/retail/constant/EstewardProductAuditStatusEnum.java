package com.chinamobile.retail.constant;

public enum EstewardProductAuditStatusEnum {

    DRAFT(1, "待审核"),
    IN_PROGRESS(2, "审核中"),
    DENIED(3, "已驳回"),
    PASSED(4, "已通过");


    private Integer status;

    private String name;

    EstewardProductAuditStatusEnum(Integer status, String name) {
        this.status = status;
        this.name = name;
    }

    public Integer getStatus() {
        return status;
    }

    public String getName() {
        return name;
    }

    /**
     * 根据 code 获取对应的枚举值
     */
    public static String fromCode(int statu) {
        for (EstewardProductAuditStatusEnum status : values()) {
            if (status.getStatus() == statu) {
                return status.getName();
            }
        }
        throw new IllegalArgumentException("未知的状态码: " + statu);
    }
}
