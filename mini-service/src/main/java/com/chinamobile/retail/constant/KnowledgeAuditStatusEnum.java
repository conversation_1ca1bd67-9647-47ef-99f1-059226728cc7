package com.chinamobile.retail.constant;

public enum KnowledgeAuditStatusEnum {

    DRAFT(0, "待审核"),
    IN_PROGRESS(1, "审核中"),
    PASSED(2, "通过"),
    REJECT(3, "驳回");

    private Integer status;

    private String name;

    KnowledgeAuditStatusEnum(Integer status, String name) {
        this.status = status;
        this.name = name;
    }

    public Integer getStatus() {
        return status;
    }

    public String getName() {
        return name;
    }

}
