package com.chinamobile.retail.constant;

public enum EstewardProductStatusEnum {

    UNPUBLISHED(1, "未发布"),
    UNDER_REVIEW(2, "审核中"),
    REJECTED(3, "驳回"),
    PUBLISHED(4, "审核通过"),
    OFFLINE(5, "下线");


    private Integer status;

    private String name;

    EstewardProductStatusEnum(Integer status, String name) {
        this.status = status;
        this.name = name;
    }

    public Integer getStatus() {
        return status;
    }

    public String getName() {
        return name;
    }

    /**
     * 根据 code 获取对应的枚举值
     */
    public static String fromCode(int statu) {
        for (EstewardProductStatusEnum status : values()) {
            if (status.getStatus() == statu) {
                return status.getName();
            }
        }
        throw new IllegalArgumentException("未知的状态码: " + statu);
    }
}
