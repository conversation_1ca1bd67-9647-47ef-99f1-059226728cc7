package com.chinamobile.retail.constant;

public enum ActivityStatusEnum {

    DRAFT(0, "待发布"),
    READY(1, "待开始"),
    IN_PROGRESS(2, "进行中"),
    SETTLEMENT(3, "结算中"),
    FINISH(4, "已结束"),
    AUDITING(5, "审核中"),
    REJECTED(6, "已驳回"),
    WAITING_FOR_AUDITING(7, "待审核"),
    OFFLINE(8, "已下线"),
    CONFIRMING(9, "订单确认中");

    private Integer status;

    private String name;

    ActivityStatusEnum(Integer status, String name) {
        this.status = status;
        this.name = name;
    }

    public Integer getStatus() {
        return status;
    }

    public String getName() {
        return name;
    }

}
