package com.chinamobile.retail.constant;

public enum ActivityAuditStatusEnum {

    DRAFT(0, "待审核"),
    IN_PROGRESS(1, "审核中"),
    PASSED(2, "审核通过"),
    DENIED(3, "审核不通过");

    private Integer status;

    private String name;

    ActivityAuditStatusEnum(Integer status, String name) {
        this.status = status;
        this.name = name;
    }

    public Integer getStatus() {
        return status;
    }

    public String getName() {
        return name;
    }

}
