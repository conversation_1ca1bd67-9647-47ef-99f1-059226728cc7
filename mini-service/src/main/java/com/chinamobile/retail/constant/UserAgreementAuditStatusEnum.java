package com.chinamobile.retail.constant;

/**
 * 用户协议 审核状态枚举
 */
public enum UserAgreementAuditStatusEnum {

    IN_PROGRESS(0, "审核中"),
    PASSED(1, "已通过"),
    DENIED(2, "已驳回");

    private Integer status;

    private String name;

    UserAgreementAuditStatusEnum(Integer status, String name) {
        this.status = status;
        this.name = name;
    }

    public Integer getStatus() {
        return status;
    }

    public String getName() {
        return name;
    }

}
