package com.chinamobile.retail.constant;

public enum KnowledgeTypeEnum {

    PRODUCT_DESC("1", "产品介绍"),
    FLOW_HANDLE("2", "流程操作视频"),
    FAQ("3", "FAQ问答对");
    private String type;

    private String name;

    KnowledgeTypeEnum(String type, String name) {
        this.type = type;
        this.name = name;
    }

    public String getType() {
        return type;
    }

    public String getName() {
        return name;
    }

    public static String getName(String type) {
        for (KnowledgeTypeEnum value : KnowledgeTypeEnum.values()) {
            if (value.type.equals(type)) {
                return value.name;
            }
        }
        return "";
    }

}
