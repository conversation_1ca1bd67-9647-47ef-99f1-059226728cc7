package com.chinamobile.retail.constant;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/5/30
 * @description  头像审批状态枚举类
 */
public enum HeaderAuditStatusEnum {

    NO_AUDIT(1,"未审批"),
    PASS_AUDIT(2,"审批通过"),
    NOT_PASS_AUDIT(3,"审批未通过");

    /**
     * 审批状态
     */
    private Integer status;

    /**
     * 审批状态描述
     */
    private String desc;

    HeaderAuditStatusEnum(Integer status, String desc){
        this.status = status;
        this.desc = desc;
    }

    public static String getDesc(String status) {
        for (HeaderAuditStatusEnum value : HeaderAuditStatusEnum.values()) {
            if (value.status.equals(status)) {
                return value.desc;
            }
        }
        return "";
    }

    public static Boolean containDesc(String desc) {
        for (HeaderAuditStatusEnum value : HeaderAuditStatusEnum.values()) {
            if (value.desc.equals(desc)) {
                return true;
            }
        }
        return false;
    }

    public Integer getStatus() {
        return status;
    }

    public String getDesc() {
        return desc;
    }
}
