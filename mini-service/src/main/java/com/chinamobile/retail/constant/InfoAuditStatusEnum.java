package com.chinamobile.retail.constant;

public enum InfoAuditStatusEnum {

    NOT_CHECK(-1, "不可审核"),
    DRAFT(0, "待审核"),
    AUDITING(1, "审核中"),
    PASSED(2, "通过"),
    DENIED(3, "驳回");

    private Integer status;

    private String name;

    InfoAuditStatusEnum(Integer status, String name) {
        this.status = status;
        this.name = name;
    }

    public Integer getStatus() {
        return status;
    }

    public String getName() {
        return name;
    }

}
