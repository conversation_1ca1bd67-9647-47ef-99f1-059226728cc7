package com.chinamobile.retail.constant;

import com.chinamobile.iot.sc.exceptions.BaseErrorConstant;
import com.chinamobile.iot.sc.exceptions.ExcepStatus;

import static com.chinamobile.iot.sc.exceptions.ExcepStatus.createInstance;

public class StatusConstant extends BaseErrorConstant {

    private final static String PREF = "20";

    public static final ExcepStatus PHONE_ERROR = createInstance(PREF+"001", "手机号格式有误！");
    public static final ExcepStatus USER_NO_EXIST = createInstance(PREF+"003", "用户不存在或该账号已停用，请与系统管理员联系！");
    public static final ExcepStatus MINI_USER_NO_EXIST = createInstance(PREF+"004", "该用户无权访问物联网商城营销通！");
    public static final ExcepStatus PHONE_IS_EXIST = createInstance(PREF+"008", "新手机号已使用！");
    public static final ExcepStatus PHONE_NOT_REGISTER = createInstance(PREF+"028", "手机号码未注册！");

    public static final ExcepStatus NOT_SHARE_URL = createInstance(PREF+"036", "商品分享链接格式错误！");

    public static final ExcepStatus NOT_IN_REGAUTH_STATUS = createInstance(PREF+"037", "不在审批状态！");

    public static final ExcepStatus CITYCODE_ERROR = createInstance(PREF+"038", "城市编码错误！");

    public static final ExcepStatus USER_IN_AUTH = createInstance(PREF+"039", "用户还在审批状态中！");
    public static final ExcepStatus FILE_NOT_EXIST=createInstance("10018","请选择上传文件");

    public static final ExcepStatus NULL_RECEIPT=createInstance(PREF+"050","未传入凭证");

    public static final ExcepStatus RECEIPT_NOT_EXIST=createInstance(PREF+"051","未找到相应凭证，请重新登录");

    public static final ExcepStatus USER_AGREEMENT_NOT_EXIST=createInstance(PREF+"052","用户协议不存在");

}
