package com.chinamobile.retail.constant;

public enum ActivityAwardStatusEnum {
    NOT_RECEIVED(0, "未领取"),
    NOT_SENT(1, "待发货"),
    SENT(2, "已发货");


    private Integer status;

    private String name;

    ActivityAwardStatusEnum(Integer status, String name) {
        this.status = status;
        this.name = name;
    };

    public String getName(Integer status) {
        for (ActivityAwardStatusEnum value : ActivityAwardStatusEnum.values()) {
            if (value.status.equals(status)) {
                return value.name;
            }
        }
        return "";

    }
    public Integer getStatus() {
        return status;
    }

    public String getName() {
        return name;
    }
}
