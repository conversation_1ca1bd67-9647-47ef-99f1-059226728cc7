package com.chinamobile.retail.constant;

/**
 * @Author: wang<PERSON>qi
 * @Date: 2024/7/26 10:52
 * @Description:
 */
public class RedisLockConstant {
    public static final String ACTIVITY_LOCK = "SC:ACTIVITY_LOCK:";
    public static final String LOCK_ACTIVITY_REGION = "SC:ACTIVITY_LOCK:REGION:";
    public static final String LOCK_ACTIVITY_ID = "SC:ACTIVITY_LOCK:ID:";
    public static final String LOCK_ACTIVITY_USER = "SC:ACTIVITY_LOCK:USER:";
    public static final String LOCK_HOME_REGION = "SC:HOME_LOCK:REGION:";
    public static final String LOCK_HOME_ID = "SC:HOME_LOCK:ID:";
    public static final String LOCK_INFO_ID = "SC:INFO_LOCK:ID:";
    public static final String LOCK_KNOWLEDGE_HOME_LIST = "SC:KNOWLEDGE_HOME_LIST_LOCK";
    public static final String LOCK_INFO_LIST_PARAM = "SC:INFO_LOCK:LIST_PARAM:";

    public static final String LOCK_MINI_PRODUCT_DETAIL = "SC:MINI:PRODUCT_LOCK:DETAIL:";
    public static final String LOCK_MINI_PRODUCT_SKU_POINT = "SC:MINI:PRODUCT_LOCK:SKU_POINT:";
    public static final String LOCK_MINI_USER = "SC:MINI:USER_LOCK:USER:";
    public static final String LOCK_PRODUCT_FLOW_INSTANCE_ATTACHMENT = "SC:MINI:PRODUCT_LOCK:ATTACHMENT:";
    public static final String LOCK_MINI_PRODUCT_COUNT = "SC:MINI:PRODUCT_LOCK:COUNT:";
    public static final String LOCK_MINI_PRODUCT_SEARCH_COUNT = "SC:MINI:PRODUCT_LOCK:SEARCH_COUNT:";
    public static final String LOCK_MINI_PRODUCT_LIST = "SC:MINI:PRODUCT_LOCK:LIST:";
    public static final String LOCK_MINI_PRODUCT_SEARCH = "SC:MINI:PRODUCT_LOCK:SEARCH:";

    public static final String LOCK_SCENE_ID = "SC:SCENE_LOCK:ID:";
    public static final String LOCK_SCENE_DIRECTORY_LIST = "SC:SCENE_DIRECTORY_LOCK:LIST";
    public static final String LOCK_MINI_SCENE_DIRECTORY = "SC:MINI:SCENE_LOCK:DIRECTORY:";
    public static final String LOCK_MINI_SCENE_LIST = "SC:MINI:SCENE_LOCK:LIST:";
    public static final String LOCK_MINI_SCENE_COUNT = "SC:MINI:SCENE_LOCK:COUNT:";
    public static final String LOCK_MINI_SCENE_RELATED_SPU = "SC:MINI:SCENE_LOCK:RELATED_SPU:";
    public static final String LOCK_MINI_SCENE_DETAIL = "SC:MINI:SCENE_LOCK:DETAIL:";

    public static final String PRODUCT_MINI_NAVIGATION_DIRECTORY_LOCK = "SC:MINI:PRODUCT_NAVIGATION_DIRECTORY_LOCK";
    public static final String PRODUCT_MINI_PROVINCE_AND_CITY = "SC:MINI:PROVINCE_AND_CITY:";
    public static final String LOCK_MINI_SCENE_REQUIREMENT_TEMPLATE = "SC:MINI:SCENE_LOCK:REQUIREMENT_TEMPLATE:";

    public static final String LOCK_TEMPLATE_ID = "SC:TEMPLATE_LOCK:ID:";

    public static final String LOCK_MINI_USER_SALE_REPORT = "SC:MINI:USER_LOCK:SALE_REPORT:";
    public static final String LOCK_MINI_VIEWED_SALE_REPORT = "SC:MINI:VIEWED_LOCK:SALE_REPORT:";

    public static final String LOCK_SPU_SKU_ATTACHMENT = "SC:MINI:SPU_SKU_LOCK:ATTACHMENT:";
    public static final String REDIS_KEY_ESTEWARD_DIRECTORY_LIST = "SC:ESTEWARD:DIRECTORY:LIST:";

    public static final String PRODUCT_MINI_HOME_PROVINCE_AND_CITY = "SC:MINI:HOME:PROVINCE_AND_CITY:";
}
