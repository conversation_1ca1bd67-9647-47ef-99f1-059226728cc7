package com.chinamobile.retail.constant;

public enum KnowledgeStatusEnum {

    DRAFT(0, "待发布"),
    PUBLISHED(1, "已发布"),
    OFFLINE(2, "已下线");

    private Integer status;

    private String name;

    KnowledgeStatusEnum(Integer status, String name) {
        this.status = status;
        this.name = name;
    }

    public Integer getStatus() {
        return status;
    }

    public String getName() {
        return name;
    }

}
