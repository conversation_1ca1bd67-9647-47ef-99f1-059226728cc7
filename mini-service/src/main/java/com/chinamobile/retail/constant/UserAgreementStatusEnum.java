package com.chinamobile.retail.constant;

/**
 * 用户协议 发布状态枚举
 */
public enum UserAgreementStatusEnum {
    DRAFT(0, "待发布"),
    IN_PROGRESS(1, "审核中"),
    PASSED(2, "已发布"),
//    OFFLINE(3, "已下线"),
    DENIED(4, "已驳回");

    private Integer status;

    private String name;

    UserAgreementStatusEnum(Integer status, String name) {
        this.status = status;
        this.name = name;
    }

    public Integer getStatus() {
        return status;
    }

    public String getName() {
        return name;
    }

}
