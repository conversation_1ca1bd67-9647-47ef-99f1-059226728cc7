package com.chinamobile.retail.constant;

public enum InfoCategoryEnum {

    DRAFT("1", "资讯中心"),
    PUBLISHED("2", "营销素材"),
    KNOWLEDGE("4", "知识库");
    private String type;

    private String name;

    InfoCategoryEnum(String type, String name) {
        this.type = type;
        this.name = name;
    }

    public String getType() {
        return type;
    }

    public String getName() {
        return name;
    }

    public static String getName(String type) {
        for (InfoCategoryEnum value : InfoCategoryEnum.values()) {
            if (value.type.equals(type)) {
                return value.name;
            }
        }
        return "";
    }

}
