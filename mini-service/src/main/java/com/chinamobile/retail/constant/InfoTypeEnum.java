package com.chinamobile.retail.constant;

public enum InfoTypeEnum {

    DRAFT("1", "产品推荐"),
    PUBLISHED("2", "产品评测"),
    OFFLINE("3", "真实案例");

    private String type;

    private String name;

    InfoTypeEnum(String type, String name) {
        this.type = type;
        this.name = name;
    }

    public String getType() {
        return type;
    }

    public String getName() {
        return name;
    }

    public static String getName(String type) {
        for (InfoTypeEnum value : InfoTypeEnum.values()) {
            if (value.type.equals(type)) {
                return value.name;
            }
        }
        return "";
    }

}
