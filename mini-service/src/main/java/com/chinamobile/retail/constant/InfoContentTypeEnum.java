package com.chinamobile.retail.constant;

public enum InfoContentTypeEnum {

    IMAGE_TEXT(1, "图文素材"),
    VIDEO(2, "视频素材"),
    FAQ(3, "FAQ"),
    WORD(4, "知识文档");

    private int type;

    private String name;

    InfoContentTypeEnum(int type, String name) {
        this.type = type;
        this.name = name;
    }

    public int getType() {
        return type;
    }

    public String getName() {
        return name;
    }

    public static String getName(int type) {
        for (InfoContentTypeEnum value : InfoContentTypeEnum.values()) {
            if (value.type == type) {
                return value.name;
            }
        }
        return "";
    }

}
