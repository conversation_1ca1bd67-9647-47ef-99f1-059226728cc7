package com.chinamobile.retail.constant;

import java.util.Arrays;
import java.util.List;

public enum MiniRoleEnum {

    NORMAL("0", "普通用户"),
    DISTRIBUTOR_FIRST("1", "一级分销员"),
    DISTRIBUTOR_SECOND("2", "二级分销员"),
    CHANNEL("3", "渠道商"),
    MANAGER("4", "客户经理"),
    IOT("10", "全国（物联网）业管"),
    PROVINCE("11", "省业管"),
    CITY("12", "地市业管"),
    REGION("13", "区县业管");

    private String type;

    private String name;

    MiniRoleEnum(String type, String name) {
        this.type = type;
        this.name = name;
    }

    public String getType() {
        return type;
    }

    public String getName() {
        return name;
    }

    public static String getName(String type) {
        for (MiniRoleEnum value : MiniRoleEnum.values()) {
            if (value.type.equals(type)) {
                return value.name;
            }
        }
        return "";
    }

    public static String getType(String name) {
        for (MiniRoleEnum value : MiniRoleEnum.values()) {
            if (value.name.equals(name)) {
                return value.type;
            }
        }
        return "";
    }

    public static String getName(String type, String provinceName, String cityName, String organizationName) {
        for (MiniRoleEnum value : MiniRoleEnum.values()) {
            if (value.type.equals(type)) {
                if (value.equals(MiniRoleEnum.PROVINCE)) {
                    List<String> special = Arrays.asList("北京", "天津", "上海", "重庆");
                    if (special.contains(provinceName)) {
                        return provinceName + "市业管";
                    } else {
                        return provinceName + value.name;
                    }
                } else if (value.equals(MiniRoleEnum.CITY)) {
                    return cityName + value.name;
                } else if (value.equals(MiniRoleEnum.REGION)) {
                    return organizationName;
                } else {
                    return value.name;
                }
            }
        }
        return "";
    }

}
