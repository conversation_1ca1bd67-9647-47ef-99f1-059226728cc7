package com.chinamobile.retail.schedule;

import com.alibaba.fastjson.JSONObject;
import com.chinamobile.iot.sc.util.DateTimeUtil;
import com.chinamobile.iot.sc.util.HttpUtil;
import com.chinamobile.retail.config.CommonConstant;
import com.chinamobile.retail.config.WeixinConfig;
import com.chinamobile.retail.enums.PointOperateTypeEnum;
import com.chinamobile.retail.pojo.entity.PointOperate;
import com.chinamobile.retail.pojo.entity.PointOperateExample;
import com.chinamobile.retail.pojo.entity.UserRetail;
import com.chinamobile.retail.pojo.entity.UserRetailExample;
import com.chinamobile.retail.pojo.vo.WeixinAccessTokenVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * created by liuxiang on 2022/12/28 11:12
 * 定时刷新微信的access_token并缓存
 */
@Component
@Slf4j
public class WeixinAccessTokenRefreshTask {

    public static final String lockKey = "WeixinAccessTokenRefreshTask";

    public static final String urlFormat = "https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=%s&secret=%s";

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Autowired
    private WeixinConfig weixinConfig;

    //每小时执行一次
    @Scheduled(cron = "0 0 0/1 * * ? ")
    public void work(){
        Boolean getLock = null;
        try {
            getLock = stringRedisTemplate.opsForValue().setIfAbsent(lockKey, "1",10, TimeUnit.MINUTES);
            if(getLock){
                log.info("开始刷新微信的access_token");
                String url = String.format(urlFormat,weixinConfig.getAppId(),weixinConfig.getAppSecret());
                Object o = HttpUtil.get(url, null, 10000, 10000);
                log.info("刷新微信的access_token响应:{}",o);
                if(o != null){
                    String str = (String) o;
                    WeixinAccessTokenVO weixinAccessTokenVO = JSONObject.parseObject(str, WeixinAccessTokenVO.class);
                    if(weixinAccessTokenVO.getAccess_token() != null && weixinAccessTokenVO.getExpires_in() != null){
                        //缓存到redis中
                        stringRedisTemplate.delete(CommonConstant.redis_weixin_access_token_key);
                        stringRedisTemplate.opsForValue().set(CommonConstant.redis_weixin_access_token_key,weixinAccessTokenVO.getAccess_token(),weixinAccessTokenVO.getExpires_in(),TimeUnit.SECONDS);
                    }
                }
                log.info("完成刷新微信的access_token");
            }
        } catch (Exception e) {
            log.error("刷新微信的access_token发生异常",e);
        }
    }

}
