package com.chinamobile.retail.schedule;

import com.alibaba.fastjson.JSONObject;
import com.chinamobile.iot.sc.util.HttpUtil;
import com.chinamobile.retail.config.CommonConstant;
import com.chinamobile.retail.config.WeixinConfig;
import com.chinamobile.retail.pojo.vo.WeixinJsapiTicketVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

/**
 * created by liuxiang on 2022/12/28 11:11
 * 定时刷新微信的jsapi_ticket并缓存
 */
@Component
@Slf4j
public class WeixinJsapiTicketRefreshTask {

    public static final String lockKey = "WeixinJsapiTicketRefreshTask";

    public static final String urlFormat = "https://api.weixin.qq.com/cgi-bin/ticket/getticket?access_token=%s&type=jsapi";

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Autowired
    private WeixinConfig weixinConfig;

    @Autowired
    private WeixinAccessTokenRefreshTask weixinAccessTokenRefreshTask;

    //每小时执行一次
    @Scheduled(cron = "0 0 0/1 * * ? ")
    public void work(){
        Boolean getLock = null;
        try {
            getLock = stringRedisTemplate.opsForValue().setIfAbsent(lockKey, "1",10, TimeUnit.MINUTES);
            if(getLock){
                log.info("开始刷新微信的jsapi_ticket");
                //首先获取access_token
                String accessToken = stringRedisTemplate.opsForValue().get(CommonConstant.redis_weixin_access_token_key);
                if(accessToken == null){
                    //没有缓存token就再次刷新token
                    weixinAccessTokenRefreshTask.work();
                    accessToken = stringRedisTemplate.opsForValue().get(CommonConstant.redis_weixin_access_token_key);
                }
                String url = String.format(urlFormat,accessToken);
                Object o = HttpUtil.get(url, null, 10000, 10000);
                if(o != null){
                    log.info("刷新微信的jsapi_ticket响应:{}",o);
                    String str = (String) o;
                    WeixinJsapiTicketVO jsapiTicketVO = JSONObject.parseObject(str, WeixinJsapiTicketVO.class);
                    if(!"0".equals(jsapiTicketVO.getErrcode())){
                        log.info("刷新微信的jsapi_ticket响应失败，失败原因:{}",jsapiTicketVO.getErrmsg());
                        return;
                    }
                    //缓存到redis
                    stringRedisTemplate.delete(CommonConstant.redis_weixin_jsapi_ticket_key);
                    stringRedisTemplate.opsForValue().set(CommonConstant.redis_weixin_jsapi_ticket_key,jsapiTicketVO.getTicket(),jsapiTicketVO.getExpires_in(),TimeUnit.SECONDS);
                }
                log.info("完成刷新微信的jsapi_ticket");
            }
        } catch (Exception e) {
            log.error("刷新微信的jsapi_ticket发生异常",e);
        }
    }
}
