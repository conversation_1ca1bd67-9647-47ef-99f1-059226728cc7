package com.chinamobile.retail.schedule;

import com.chinamobile.iot.sc.common.redis.RedisUtil;
import com.chinamobile.retail.constant.ActivityStatusEnum;
import com.chinamobile.retail.dao.MiniProgramActivityMapper;
import com.chinamobile.retail.pojo.entity.MiniProgramActivity;
import com.chinamobile.retail.pojo.entity.MiniProgramActivityExample;
import com.chinamobile.retail.service.IMiniActivityService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * created by wa<PERSON><PERSON><PERSON> on 2024/7/25 15:31
 * 每天计算活动排名
 */
@Component
@EnableScheduling
@Slf4j
public class ActivityRankingTask {

    private String lockKey = "ActivityRankingTask";
    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Resource
    private MiniProgramActivityMapper miniProgramActivityMapper;
    @Resource
    private IMiniActivityService miniActivityService;
    @Resource
    private RedisUtil redisUtil;

    //每天零时零分零秒执行
//    @Scheduled(cron = "0 */10 * * * ?")
    @Scheduled(cron = "0 0 0 * * ?")
    public void work() {
        Boolean getLock = null;
        try {
            getLock = stringRedisTemplate.opsForValue().setIfAbsent(lockKey, "1", 1, TimeUnit.MINUTES);
            log.info("getLock:{}", getLock);
            if (getLock) {
                log.info("====start ActivityRankingTask====");
                Date now = new Date();

                //获取所有状态为进行中的排位赛
                List<Integer> status = new ArrayList<>();
                status.add(ActivityStatusEnum.IN_PROGRESS.getStatus());
                status.add(ActivityStatusEnum.CONFIRMING.getStatus());
                status.add(ActivityStatusEnum.SETTLEMENT.getStatus());
                List<MiniProgramActivity> miniProgramActivitys = miniProgramActivityMapper.selectByExample(new MiniProgramActivityExample().createCriteria().andActivityTypeEqualTo(1).andStatusIn(status).andIsDeleteEqualTo(0).example());
                if (miniProgramActivitys != null && miniProgramActivitys.size() > 0) {
                    //对每个活动进行排名

                    miniProgramActivitys.forEach(a -> {
                        log.info("ActivityRankingTask活动id:{},活动状态:{}", a.getId(), a.getStatus());
                        if (Objects.equals(a.getStatus(), ActivityStatusEnum.IN_PROGRESS.getStatus())) {
                            if (now.compareTo(a.getConfirmTime()) < 0) {
                                log.info("ActivityRankingTask活动id,进行中:{},小于确认时间,保存排名");
                                miniActivityService.saveRankActivityUserAwards(a);
                            }

                        } else {
                            log.info("ActivityRankingTask活动id:{},当前时间和确认时间比较:{}", a.getId(), now.compareTo(a.getConfirmTime()));
                            if (now.compareTo(a.getConfirmTime()) < 0) {
                                log.info("ActivityRankingTask活动id,结算中:{},小于确认时间,保存排名");
                                miniActivityService.saveRankActivityUserAwards(a);
                            }
                        }

                    });
                }
                log.info("====finish ActivityRankingTask====");
            }
        } catch (Exception e) {
            log.error("ActivityRankingTask发生异常", e);
        }
    }
}
