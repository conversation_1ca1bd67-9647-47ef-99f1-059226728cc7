package com.chinamobile.retail.schedule;

import com.chinamobile.iot.sc.common.utils.NumberUtil;
import com.chinamobile.iot.sc.util.DateTimeUtil;
import com.chinamobile.retail.dao.PointOperateMapper;
import com.chinamobile.retail.dao.UserRetailMapper;
import com.chinamobile.retail.dao.ext.PartnerPointMapperExt;
import com.chinamobile.retail.enums.PointOperateTypeEnum;
import com.chinamobile.retail.pojo.entity.PointOperate;
import com.chinamobile.retail.pojo.entity.PointOperateExample;
import com.chinamobile.retail.pojo.entity.UserRetail;
import com.chinamobile.retail.pojo.entity.UserRetailExample;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * created by liuxiang on 2022/10/18 10:23
 * 更新‘可兑换积分’，兑换截至上个自然月底所有完成交易订单获得的积分。
 * 计算上个月的可兑换积分，叠加到总的可兑换积分
 */
@Component
@Slf4j
public class AvailablePointTask {

    private String lockKey = "AvailablePointTask";

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Resource
    private PointOperateMapper pointOperateMapper;

    @Resource
    private UserRetailMapper userRetailMapper;

    @Resource
    private PartnerPointMapperExt partnerPointMapperExt;

    //每月1日零时零分零秒执行
    @Scheduled(cron = "0 0 0 1 * ?")
//    @Scheduled(cron = "0 * * * * ?")
    public void work(){
        Boolean getLock = null;
        try {
            getLock = stringRedisTemplate.opsForValue().setIfAbsent(lockKey, "1",1, TimeUnit.MINUTES);
            if(getLock){
                log.info("====start AvailablePointTask====");
                Date now = new Date();
                Date previousMonthStart = DateTimeUtil.getMonthStart(now, -1);
                Date previousMonthEnd = DateTimeUtil.getMonthEnd(previousMonthStart);
                log.info("开始时间:{},结束时间:{}",previousMonthStart,previousMonthEnd);
                //按照合作伙伴查询
                List<UserRetail> partnerList = userRetailMapper.selectByExample(new UserRetailExample());
                List<String> partnerIdList = partnerList.stream().map(u -> {
                    return u.getId();
                }).collect(Collectors.toList());
                log.info("合伙人数量:{}",partnerIdList.size());
                for (String partnerId : partnerIdList) {
                    try {
                        //这里只计算上个月的订单激励（完成订单）的积分，不考虑补发和扣减，那部分是积分调整时直接更新可兑换积分
                        //也不考虑兑换的积分，那是发起兑换任务和兑换任务完成的时候更新可用积分
                        PointOperateExample example = new PointOperateExample().createCriteria().andTypeEqualTo(PointOperateTypeEnum.ORDER_POINT.code)
                                .andUserIdEqualTo(partnerId)
                                .andChannelEqualTo(0)
                                .andCreateTimeGreaterThanOrEqualTo(previousMonthStart)
                                .andCreateTimeLessThanOrEqualTo(previousMonthEnd)
                                .example();
                        List<PointOperate> pointOperates = pointOperateMapper.selectByExample(example);
                        //根据供应商分组更新积分
                        Map<String, List<PointOperate>> map = pointOperates.stream().collect(Collectors.groupingBy(p -> {
                            return p.getSupplierId();
                        }));
                        for (Map.Entry<String, List<PointOperate>> entry : map.entrySet()) {
                            String supplierId = entry.getKey();
                            List<PointOperate> pointList = entry.getValue();
                            long orderPoint = pointList.stream().mapToLong(PointOperate::getPoint).sum();
                            orderPoint = NumberUtil.getMoneyUnit(orderPoint);
                            log.info("合伙人id:{}查询出的供应商id:{}下的订单激励积分:{}",partnerId,supplierId,orderPoint);
                            //累加用户的可兑换积分
                            if (orderPoint > 0){
                                partnerPointMapperExt.addAvailablePoint(partnerId,supplierId,orderPoint,0);
                            }
                        }
                    } catch (Exception e) {
                        log.error("定时任务计算用户id:{}的可兑换积分发生异常",e);
                    }
                }
                log.info("====finish AvailablePointTask====");
            }
        } catch (Exception e) {
            log.error("AvailablePointTask发生异常",e);
        }
        //这里不主动释放锁，目的是避免多节点情况下，由于节点时间偏差，某节点在短时间内执行完任务后解锁，另一节点才开始执行就会获取到锁，造成任务重复执行
/*        finally {
            if(getLock != null && getLock){
                stringRedisTemplate.delete(lockKey);
            }
        }*/
    }


    /**
     * 用于快速验证功能，将本月已完成订单计算积分
     * @param userId
     */
    public void workTest(String userId,Integer channel){
        Boolean getLock = null;
        try {
            getLock = stringRedisTemplate.opsForValue().setIfAbsent(lockKey, "1",1, TimeUnit.MINUTES);
            if(getLock){
                log.info("====start AvailablePointTask====");
                Date now = new Date();
                Date monthStart = DateTimeUtil.getMonthStart(now, 0);
                Date monthEnd = DateTimeUtil.getMonthEnd(monthStart);
                log.info("开始时间:{},结束时间:{}",monthStart,monthEnd);
                //按照合作伙伴查询
                UserRetailExample userRetailExample = new UserRetailExample();
                if(userId != null){
                    userRetailExample.createCriteria().andIdEqualTo(userId);
                }
                List<UserRetail> partnerList = userRetailMapper.selectByExample(userRetailExample);
                List<String> partnerIdList = partnerList.stream().map(u -> {
                    return u.getId();
                }).collect(Collectors.toList());
                log.info("合伙人数量:{}",partnerIdList.size());
                if(channel == null){
                    channel=0;
                }
                for (String partnerId : partnerIdList) {
                    try {
                        //这里只计算订单激励（完成订单）的积分，不考虑补发和扣减，那部分是积分调整时直接更新可兑换积分
                        //也不考虑兑换的积分，那是发起兑换任务和兑换任务完成的时候更新可用积分
                        PointOperateExample example = new PointOperateExample().createCriteria().andTypeEqualTo(PointOperateTypeEnum.ORDER_POINT.code)
                                .andUserIdEqualTo(partnerId)
                                .andChannelEqualTo(channel)
                                .andCreateTimeGreaterThanOrEqualTo(monthStart)
                                .andCreateTimeLessThanOrEqualTo(monthEnd)
                                .example();
                        List<PointOperate> pointOperates = pointOperateMapper.selectByExample(example);
                        //根据供应商分组更新积分
                        Map<String, List<PointOperate>> map = pointOperates.stream().collect(Collectors.groupingBy(p -> {
                            return p.getSupplierId();
                        }));
                        for (Map.Entry<String, List<PointOperate>> entry : map.entrySet()) {
                            String supplierId = entry.getKey();
                            List<PointOperate> pointList = entry.getValue();
                            long orderPoint = pointList.stream().mapToLong(PointOperate::getPoint).sum();
                            log.info("合伙人id:{}查询出的供应商id:{}下的订单激励积分:{}",partnerId,supplierId,orderPoint);
                            //累加用户的可兑换积分
                            if (orderPoint > 0){
                                partnerPointMapperExt.addAvailablePoint(partnerId,supplierId,orderPoint,channel);
                            }
                        }
                    } catch (Exception e) {
                        log.error("定时任务计算用户id:{}的可兑换积分发生异常",e);
                    }
                }
                log.info("====finish AvailablePointTask====");
            }
        } catch (Exception e) {
            log.error("AvailablePointTask发生异常",e);
        }
    }
}
