package com.chinamobile.retail.dao;

import com.chinamobile.retail.pojo.entity.SpuSkuAttachment;
import com.chinamobile.retail.pojo.entity.SpuSkuAttachmentExample;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface SpuSkuAttachmentMapper {
    /**
     *
     * @mbg.generated Thu Feb 13 11:32:09 CST 2025
     */
    long countByExample(SpuSkuAttachmentExample example);

    /**
     *
     * @mbg.generated Thu Feb 13 11:32:09 CST 2025
     */
    int deleteByExample(SpuSkuAttachmentExample example);

    /**
     *
     * @mbg.generated Thu Feb 13 11:32:09 CST 2025
     */
    int deleteByPrimaryKey(String id);

    /**
     *
     * @mbg.generated Thu Feb 13 11:32:09 CST 2025
     */
    int insert(SpuSkuAttachment record);

    /**
     *
     * @mbg.generated Thu Feb 13 11:32:09 CST 2025
     */
    int insertSelective(SpuSkuAttachment record);

    /**
     *
     * @mbg.generated Thu Feb 13 11:32:09 CST 2025
     */
    List<SpuSkuAttachment> selectByExample(SpuSkuAttachmentExample example);

    /**
     *
     * @mbg.generated Thu Feb 13 11:32:09 CST 2025
     */
    SpuSkuAttachment selectByPrimaryKey(String id);

    /**
     *
     * @mbg.generated Thu Feb 13 11:32:09 CST 2025
     */
    int updateByExampleSelective(@Param("record") SpuSkuAttachment record, @Param("example") SpuSkuAttachmentExample example);

    /**
     *
     * @mbg.generated Thu Feb 13 11:32:09 CST 2025
     */
    int updateByExample(@Param("record") SpuSkuAttachment record, @Param("example") SpuSkuAttachmentExample example);

    /**
     *
     * @mbg.generated Thu Feb 13 11:32:09 CST 2025
     */
    int updateByPrimaryKeySelective(SpuSkuAttachment record);

    /**
     *
     * @mbg.generated Thu Feb 13 11:32:09 CST 2025
     */
    int updateByPrimaryKey(SpuSkuAttachment record);

    /**
     *
     * @mbg.generated Thu Feb 13 11:32:09 CST 2025
     */
    int batchInsert(@Param("list") List<SpuSkuAttachment> list);

    /**
     *
     * @mbg.generated Thu Feb 13 11:32:09 CST 2025
     */
    int batchInsertSelective(@Param("list") List<SpuSkuAttachment> list, @Param("selective") SpuSkuAttachment.Column ... selective);
}