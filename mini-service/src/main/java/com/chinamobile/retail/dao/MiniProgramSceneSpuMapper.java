package com.chinamobile.retail.dao;

import com.chinamobile.retail.pojo.entity.MiniProgramSceneSpu;
import com.chinamobile.retail.pojo.entity.MiniProgramSceneSpuExample;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface MiniProgramSceneSpuMapper {
    /**
     *
     * @mbg.generated Tue Dec 10 14:29:42 GMT+08:00 2024
     */
    long countByExample(MiniProgramSceneSpuExample example);

    /**
     *
     * @mbg.generated Tue Dec 10 14:29:42 GMT+08:00 2024
     */
    int deleteByExample(MiniProgramSceneSpuExample example);

    /**
     *
     * @mbg.generated Tue Dec 10 14:29:42 GMT+08:00 2024
     */
    int deleteByPrimaryKey(String id);

    /**
     *
     * @mbg.generated Tue Dec 10 14:29:42 GMT+08:00 2024
     */
    int insert(MiniProgramSceneSpu record);

    /**
     *
     * @mbg.generated Tue Dec 10 14:29:42 GMT+08:00 2024
     */
    int insertSelective(MiniProgramSceneSpu record);

    /**
     *
     * @mbg.generated Tue Dec 10 14:29:42 GMT+08:00 2024
     */
    List<MiniProgramSceneSpu> selectByExample(MiniProgramSceneSpuExample example);

    /**
     *
     * @mbg.generated Tue Dec 10 14:29:42 GMT+08:00 2024
     */
    MiniProgramSceneSpu selectByPrimaryKey(String id);

    /**
     *
     * @mbg.generated Tue Dec 10 14:29:42 GMT+08:00 2024
     */
    int updateByExampleSelective(@Param("record") MiniProgramSceneSpu record, @Param("example") MiniProgramSceneSpuExample example);

    /**
     *
     * @mbg.generated Tue Dec 10 14:29:42 GMT+08:00 2024
     */
    int updateByExample(@Param("record") MiniProgramSceneSpu record, @Param("example") MiniProgramSceneSpuExample example);

    /**
     *
     * @mbg.generated Tue Dec 10 14:29:42 GMT+08:00 2024
     */
    int updateByPrimaryKeySelective(MiniProgramSceneSpu record);

    /**
     *
     * @mbg.generated Tue Dec 10 14:29:42 GMT+08:00 2024
     */
    int updateByPrimaryKey(MiniProgramSceneSpu record);

    /**
     *
     * @mbg.generated Tue Dec 10 14:29:42 GMT+08:00 2024
     */
    int batchInsert(@Param("list") List<MiniProgramSceneSpu> list);

    /**
     *
     * @mbg.generated Tue Dec 10 14:29:42 GMT+08:00 2024
     */
    int batchInsertSelective(@Param("list") List<MiniProgramSceneSpu> list, @Param("selective") MiniProgramSceneSpu.Column ... selective);
}