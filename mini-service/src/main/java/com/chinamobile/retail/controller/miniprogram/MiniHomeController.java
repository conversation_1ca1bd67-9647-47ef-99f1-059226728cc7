package com.chinamobile.retail.controller.miniprogram;

import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.common.Constant;
import com.chinamobile.iot.sc.mode.LoginIfo4Redis;
import com.chinamobile.iot.sc.mode.PageData;
import com.chinamobile.retail.pojo.param.miniprogram.*;
import com.chinamobile.retail.pojo.vo.miniprogram.HomeSearchVO;
import com.chinamobile.retail.pojo.vo.miniprogram.HomeVO;
import com.chinamobile.retail.pojo.vo.miniprogram.InfoDetailVO;
import com.chinamobile.retail.pojo.vo.miniprogram.*;
import com.chinamobile.retail.service.IMiniHomeService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/7/16 09:42
 * @description TODO
 */
@RestController
@RequestMapping("/miniprogram/home")
public class MiniHomeController {

    @Resource
    private IMiniHomeService miniHomeService;

    /**
     * 管理后台首页配置列表分页查询
     *
     * @return
     */
    @GetMapping ("/list")
    public BaseAnswer<PageData<HomeVO>> pageInfoListBack(PageHomeListParam param, @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis) {
        PageData<HomeVO> pageData = miniHomeService.pageHomeList(param,loginIfo4Redis);
        return BaseAnswer.success(pageData);
    }
    /**
     * 管理后台首页审批列表分页查询
     *
     * @return
     */
    @GetMapping ("/listAudit")
    public BaseAnswer<PageData<HomeVO>> pageInfoListBackAudit(PageHomeListParam param, @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis) {
        PageData<HomeVO> pageData = miniHomeService.pageHomeList(param,loginIfo4Redis);
        return BaseAnswer.success(pageData);
    }

    /**
     * 小程序前端获取首页
     * */
    @GetMapping("/mini")
    public BaseAnswer<HomeVO> getHome(@RequestParam(name = "userId",required = false) String userId,
                                      @RequestParam(name = "provinceCode",required = false) String provinceCode,
                                      @RequestParam(name = "cityCode",required = false) String cityCode) {
        return BaseAnswer.success(miniHomeService.getHomeMini(userId,provinceCode,cityCode));
    }

    /**
     * 管理后台配置首页详情
     *
     * @return
     */
    @GetMapping("/detail")
    public BaseAnswer<HomeVO> getDetail(@RequestParam("id") String id,
                                              @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis) {
        return BaseAnswer.success(miniHomeService.getHomeDetail(id,loginIfo4Redis));
    }
    /**
     * 管理后台审批首页详情
     *
     * @return
     */
    @GetMapping("/detailAudit")
    public BaseAnswer<HomeVO> getDetailAudit(@RequestParam("id") String id,
                                        @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis) {
        return BaseAnswer.success(miniHomeService.getHomeDetail(id,loginIfo4Redis));
    }

    /**
     * 创建首页
     *
     * @param param
     * @param userId
     * @return
     */
    @PostMapping("/create")
    public BaseAnswer create(@Valid @RequestBody HomeParam param,
                               @RequestHeader(name = Constant.HEADER_KEY_USER_ID) String userId) {
        miniHomeService.create(param, userId);
        return new BaseAnswer<>();
    }

    /**
     * 编辑首页
     *
     * @param param
     * @param userId
     * @return
     */
    @PostMapping("/edit")
    public BaseAnswer edit(@Valid @RequestBody HomeParam param,
                             @RequestHeader(name = Constant.HEADER_KEY_USER_ID) String userId) {
        miniHomeService.edit(param, userId);
        return new BaseAnswer<>();
    }

    /**
     * 首页审核
     *
     * @param param
     * @param loginIfo4Redis
     * @return
     */
    @PostMapping("/audit")
    public BaseAnswer audit(@Valid @RequestBody InfoAuditParam param,
                            @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis) {
        miniHomeService.audit(param, loginIfo4Redis);
        return new BaseAnswer<>();
    }

    /**
     * 资讯下线
     *
     * @param param
     * @param loginIfo4Redis
     * @return
     */
    @PostMapping("/offline")
    public BaseAnswer offline(@Valid @RequestBody InfoOfflineParam param,
                            @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis) {
        miniHomeService.offline(param, loginIfo4Redis);
        return new BaseAnswer<>();
    }

    /**
     * 发布素材
     */
    @PostMapping("/publish")
    public BaseAnswer publish(@Valid @RequestBody HomePublishParam param,
                              @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis) {
        miniHomeService.publish(param,loginIfo4Redis);
        return new BaseAnswer();
    }

    @PostMapping("/load2Redis")
    public BaseAnswer load2Redis() {
        miniHomeService.loadHome2Redis();
        return new BaseAnswer<>();
    }
    /**
     * 首页删除
     *
     * @return
     */
    @DeleteMapping("/delete")
    public BaseAnswer<Void> delete(@RequestParam String id) {
        miniHomeService.delete(id);
        return BaseAnswer.success(null);
    }

    @PostMapping("/migrateSpu")
    public BaseAnswer migrateSpu() {
        miniHomeService.migrateSpu();
        return new BaseAnswer<>();
    }

    /**
     * 首页搜索（产品、活动、素材、资讯、知识）
     */
    @GetMapping("/search")
    public BaseAnswer<PageData<HomeSearchVO>> search(HomeSearchParam param) {
        PageData<HomeSearchVO> pageData = miniHomeService.search(param);
        return BaseAnswer.success(pageData);
    }

    /**
     * 获取全国的省份信息
     *
     * @return
     */
    @GetMapping("/regions")
    public BaseAnswer<List<ActivityProvinceVO>> getAllRegions() {
        List<ActivityProvinceVO> activityProvinceVOS = miniHomeService.getAllRegions();
        return BaseAnswer.success(activityProvinceVOS);
    }

}
