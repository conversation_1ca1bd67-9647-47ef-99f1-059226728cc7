package com.chinamobile.retail.controller.miniprogram;

import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.common.Constant;
import com.chinamobile.iot.sc.mode.LoginIfo4Redis;
import com.chinamobile.retail.pojo.entity.JkbanQuestionInfo;
import com.chinamobile.retail.pojo.param.miniprogram.CreateJkbanParam;
import com.chinamobile.retail.pojo.param.miniprogram.JkbanListParam;
import com.chinamobile.retail.pojo.vo.miniprogram.JkbanDetailVO;
import com.chinamobile.retail.pojo.vo.miniprogram.JkbanListVO;
import com.chinamobile.retail.pojo.vo.miniprogram.JkbanProvinceListVO;
import com.chinamobile.retail.service.IMiniProgramJkbanService;
import com.github.pagehelper.PageInfo;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

@RestController
@RequestMapping("/miniprogram/jkban")
public class MiniProgramJkbanController {

    @Resource
    private IMiniProgramJkbanService jkbanService;

    /**
     * 获取jkban列表
     * @return
     */
    @GetMapping("/list")
    public BaseAnswer<PageInfo<JkbanListVO>> getJkbanList(JkbanListParam param,@RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis) {
        return jkbanService.getJkbanList(param, loginIfo4Redis);
    }

    /**
     * 创建jkban
     * @return
     */
    @PostMapping("/create")
    public BaseAnswer<Void> createJkban(@RequestBody @Valid CreateJkbanParam param,@RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis) {
        return jkbanService.createJkban(param, loginIfo4Redis);
    }


    /**
     * 删除jkban
     * @return
     */
    @GetMapping("/detail")
    public BaseAnswer<JkbanDetailVO> getJkbanDetail(@RequestParam(value = "id") String id) {
        return jkbanService.getJkbanDetail(id);
    }

    /**
     * 获取jkban省列表
     * @return
     */
    @GetMapping("/province/list")
    public BaseAnswer<List<JkbanProvinceListVO>> getJkbanProvinceList() {
        return jkbanService.getJkbanProvinceList();
    }

    /**
     * 获取jkban故障列表
     * @return
     */
    @GetMapping("/question/list")
    public BaseAnswer<List<JkbanQuestionInfo>> getJkbanQuestionList() {
        return jkbanService.getJkbanQuestionList();
    }
}