package com.chinamobile.retail.controller.miniprogram;

import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.common.Constant;
import com.chinamobile.iot.sc.mode.BasePageQuery;
import com.chinamobile.iot.sc.mode.LoginIfo4Redis;
import com.chinamobile.iot.sc.mode.PageData;
import com.chinamobile.retail.pojo.param.AuditUserHeaderParam;
import com.chinamobile.retail.pojo.vo.UserHeaderVO;
import com.chinamobile.retail.service.UserRetailRequestHeaderService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/5/31
 * @description 分销中心用户头像申请记录controller类
 */
@RestController
@RequestMapping(value = "/miniprogram")
public class UserMinProgramRequestHeaderController {

    @Resource
    private UserRetailRequestHeaderService userRetailRequestHeaderService;

    /**
     * 获取头像申请记录列表
     * @param basePageQuery
     * @return
     */
    @GetMapping(value = "/pageUserHeader")
    public BaseAnswer<PageData<UserHeaderVO>> pageUserHeader(BasePageQuery basePageQuery){
        BaseAnswer baseAnswer = new BaseAnswer();
        PageData<UserHeaderVO> pageData = userRetailRequestHeaderService.pageUserHeaderVO(basePageQuery);
        baseAnswer.setData(pageData);
        return baseAnswer;
    }

    /**
     * 审核用户头像
     * @param auditUserHeaderParam
     * @param userId
     * @return
     */
    @PostMapping(value = "/auditUserHeader")
    public BaseAnswer auditUserHeader(@RequestBody @Valid AuditUserHeaderParam auditUserHeaderParam,
                               @RequestHeader(name = Constant.HEADER_KEY_USER_ID) String userId){
        userRetailRequestHeaderService.auditUserHeader(auditUserHeaderParam,userId);
        return new BaseAnswer();
    }

}
