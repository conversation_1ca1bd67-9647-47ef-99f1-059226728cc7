package com.chinamobile.retail.controller.miniprogram;

import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.common.Constant;
import com.chinamobile.iot.sc.mode.LoginIfo4Redis;
import com.chinamobile.iot.sc.mode.PageData;
import com.chinamobile.retail.constant.InfoStatusEnum;
import com.chinamobile.retail.pojo.param.MiniProgramInfoRequestOnlineParam;
import com.chinamobile.retail.pojo.param.miniprogram.*;
import com.chinamobile.retail.pojo.vo.miniprogram.InfoDetailVO;
import com.chinamobile.retail.pojo.vo.miniprogram.KnowledgeHomeVO;
import com.chinamobile.retail.pojo.vo.miniprogram.PageInfoVO;
import com.chinamobile.retail.service.IMiniInfoService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/7/16 09:42
 * @description TODO
 */
@RestController
@RequestMapping("/miniprogram/info")
public class MiniInfoController {

    @Resource
    private IMiniInfoService miniInfoService;

    /**
     * 管理后台素材配置管理列表分页查询
     *
     * @return
     */
    @GetMapping ("/list/back")
    public BaseAnswer pageInfoListBack(PageInfoListParam param,@RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis) {
        PageData<PageInfoVO> pageData = miniInfoService.pageInfoList(param,loginIfo4Redis);
        return BaseAnswer.success(pageData);
    }
    /**
     * 管理后台素材审批列表分页查询
     *
     * @return
     */
    @GetMapping ("/list/backAudit")
    public BaseAnswer pageInfoListBackAudit(PageInfoListParam param,@RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis) {
        PageData<PageInfoVO> pageData = miniInfoService.pageInfoList(param,loginIfo4Redis);
        return BaseAnswer.success(pageData);
    }
    /**
     * 小程序资讯列表分页查询
     *
     * @return
     */
    @GetMapping("/list/mini")
    public BaseAnswer pageInfoListMini(PageInfoListParam param) {
        //小程序只查询“已发布”资讯
        param.setStatus(InfoStatusEnum.PUBLISHED.getStatus());
        param.setIsMini(1);
        PageData<PageInfoVO> pageData = miniInfoService.pageInfoListMini(param);
        return BaseAnswer.success(pageData);
    }
    /**
     * 小程序知识库首页
     *
     * @return
     */
    @GetMapping("/knowledge/home")
    public BaseAnswer<KnowledgeHomeVO> pageKnowledgeHome() {
        //小程序知识库首页
        KnowledgeHomeVO vo = miniInfoService.pageKnowledgeHome();
        return BaseAnswer.success(vo);
    }

    @GetMapping("/detail/mini")
    public BaseAnswer<InfoDetailVO> getDetail(@RequestParam("id") String id,
                                              @RequestParam(value = "userId",required = false) String userId,
                                              @RequestParam(value = "provinceCode",required = false) String provinceCode,
                                              @RequestParam(value = "cityCode",required = false) String cityCode) {
        return BaseAnswer.success(miniInfoService.getInfoDetail(id,userId,provinceCode,cityCode,null));
    }

    @GetMapping("/detail/back")
    public BaseAnswer<InfoDetailVO> getDetail(@RequestParam("id") String id,
                                              @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis) {
        return BaseAnswer.success(miniInfoService.getInfoDetail(id, null, null,null,loginIfo4Redis));
    }
    @GetMapping("/detail/backAudit")
    public BaseAnswer<InfoDetailVO> getDetailAudit(@RequestParam("id") String id,
                                                   @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis) {
        return BaseAnswer.success(miniInfoService.getInfoDetail(id, null, null,null,loginIfo4Redis));
    }

    /**
     * 创建资讯
     *
     * @param param
     * @param userId
     * @return
     */
    @PostMapping("/create")
    public BaseAnswer create(@Valid @RequestBody InfoParam param,
                               @RequestHeader(name = Constant.HEADER_KEY_USER_ID) String userId) {
        miniInfoService.create(param, userId);
        return new BaseAnswer<>();
    }

    /**
     * 编辑资讯
     *
     * @param param
     * @param userId
     * @return
     */
    @PostMapping("/edit")
    public BaseAnswer edit(@Valid @RequestBody InfoParam param,
                             @RequestHeader(name = Constant.HEADER_KEY_USER_ID) String userId) {
        miniInfoService.edit(param, userId);
        return new BaseAnswer<>();
    }

    /**
     * 设置为首页显示  热门
     *
     * @param id
     * @return
     */
    @GetMapping("/setPopular")
    public BaseAnswer setPopular(@RequestParam("id") String id,@RequestParam("isPopular") Integer isPopular) {
        miniInfoService.setPopular(id,isPopular);
        return new BaseAnswer<>();
    }

    /**
     * 资讯审核
     *
     * @param param
     * @param loginIfo4Redis
     * @return
     */
    @PostMapping("/audit")
    public BaseAnswer audit(@Valid @RequestBody InfoAuditParam param,
                            @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis) {
        miniInfoService.audit(param, loginIfo4Redis);
        return new BaseAnswer<>();
    }

    /**
     * 资讯下线
     *
     * @param param
     * @param loginIfo4Redis
     * @return
     */
    @PostMapping("/offline")
    public BaseAnswer offline(@Valid @RequestBody InfoOfflineParam param,
                            @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis) {
        miniInfoService.offline(param, loginIfo4Redis);
        return new BaseAnswer<>();
    }

    /**
     * 判断用户是否有资讯附件产品访问权限
     * */
    @GetMapping("/product/judge")
    public BaseAnswer<Boolean> judgeProduct(@RequestParam("spuCode") String spuCode,
                                            @RequestParam(value = "userId",required = false) String userId,
                                            @RequestParam(value = "provinceCode",required = false) String provinceCode,
                                            @RequestParam(value = "cityCode",required = false) String cityCode) {
        return BaseAnswer.success(miniInfoService.judgeProduct(spuCode,userId,provinceCode,cityCode));
    }

    /**
     * 发布素材
     */
    @PostMapping("/publish")
    public BaseAnswer publish(@Valid @RequestBody PublishInfoParam param) {
        miniInfoService.publish(param);
        return new BaseAnswer();
    }

    @GetMapping ("/search")
    public BaseAnswer<List<PageInfoVO>> searchInfo(@RequestParam(value = "keyWord",required = false) String keyWord,
                                                   @RequestParam("contentType") Integer contentType,@RequestParam("categoryList") List<Integer> categoryList) {
        List<PageInfoVO> pageData = miniInfoService.searchInfo(keyWord,contentType,categoryList);
        return BaseAnswer.success(pageData);
    }
    /**
     * 资讯删除
     *
     * @return
     */
    @DeleteMapping("/delete")
    public BaseAnswer<Void> delete(@RequestParam String id) {
        miniInfoService.delete(id);
        return BaseAnswer.success(null);
    }

    @PostMapping("/request/online")
    public BaseAnswer requestOnline(@Valid @RequestBody MiniProgramInfoRequestOnlineParam param,
                                    @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis) {
        miniInfoService.requestOnline(param, loginIfo4Redis);
        return new BaseAnswer<>();
    }
}
