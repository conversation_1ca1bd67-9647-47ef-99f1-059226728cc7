package com.chinamobile.retail.controller.miniprogram;

import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.common.Constant;
import com.chinamobile.iot.sc.entity.retail.FindMiniProgramUserParam;
import com.chinamobile.iot.sc.mode.LoginIfo4Redis;
import com.chinamobile.iot.sc.mode.PageData;
import com.chinamobile.retail.annotation.CodeValidMark;
import com.chinamobile.retail.constant.UserAgreementStatusEnum;
import com.chinamobile.retail.pojo.entity.UserMiniProgramAddress;
import com.chinamobile.retail.pojo.param.miniprogram.*;
import com.chinamobile.retail.pojo.vo.UserCenterVO;
import com.chinamobile.retail.pojo.vo.miniprogram.*;
import com.chinamobile.retail.service.IMiniProgramUserService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/5/29 15:41
 * @description TODO
 */
@RestController
@RequestMapping("/miniprogram/user")
public class MiniUserController {

    @Resource
    private IMiniProgramUserService userService;

    /**
     * 更新用户姓名
     * @param updateUserNameParam
     * @return
     */
    @PostMapping(value = "/updateUserName")
    public BaseAnswer updateUserName(@RequestBody UpdateUserNameParam updateUserNameParam,
             @RequestHeader(name = Constant.HEADER_KEY_USER_ID) String userId){
        BaseAnswer baseAnswer = new BaseAnswer();
        updateUserNameParam.setUserId(userId);
        userService.updateUserName(updateUserNameParam);
        return baseAnswer;
    }

    /**
     * 上传用户头像
     * @param file
     * @param userId
     * @return
     */
    @PostMapping("/uploadUserHeader")
    public BaseAnswer uploadUserHeader(MultipartFile file,
                 @RequestHeader(name = Constant.HEADER_KEY_USER_ID) String userId){
        BaseAnswer baseAnswer = new BaseAnswer();
        userService.uploadUserHeader(file,userId);
        return baseAnswer;
    }

    /**
     * 获取用户个人中心信息
     * @param userId
     * @return
     */
    @GetMapping(value = "/getUserCenterInfo")
    public BaseAnswer<UserCenterVO> getUserCenterInfo(@RequestHeader(name = Constant.HEADER_KEY_USER_ID) String userId){
        UserCenterVO userCenterInfo = userService.getUserCenterInfo(userId);
        return new BaseAnswer<UserCenterVO>().setData(userCenterInfo);
    }

    @GetMapping("/captcha/sms")
    public BaseAnswer<Void> getLoginSmsValidCode(@RequestParam("phone") String phone) {
        userService.getLoginSmsValidCode(phone);
        return new BaseAnswer<>();
    }

    @CodeValidMark(validCode = false, validSmsCode = true, encrypted = true)
    @PostMapping("/sms/login")
    public BaseAnswer loginBySmsCode(@RequestParam("phone") String phone, @RequestParam("code") String code){
        MiniProgramLoginVO miniProgramLoginVO = userService.loginBySmsCode(phone);
        return BaseAnswer.success(miniProgramLoginVO);
    }

    @PostMapping("/findUserId")
    public BaseAnswer<String> findUserId(@RequestBody FindMiniProgramUserParam param) {
        String userId = userService.findUserId(param);
        return BaseAnswer.success(userId);
    }

    @GetMapping("/migrateFromManageAndCustomer")
    public BaseAnswer migrateFromManageAndCustomer() {
        String msg = userService.migrateFromManageAndCustomer();
        return BaseAnswer.success(msg);
    }
    @GetMapping("/customerEncryption")
    public void customerEncryption() {
       userService.customerEncryption();

    }

    /**
     * 保存用户协议/隐私政策
     */
    @PostMapping("/userAgreement/save")
    public BaseAnswer<Void> saveUserAgreement(@Validated @RequestBody SaveUserAgreementParam param,
                                              @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis) {
        userService.saveUserAgreement(param,loginIfo4Redis);
        return new BaseAnswer<>();
    }

    /**
     * （OS）搜索用户协议列表(创建时间倒序)
     * @return
     */
    @GetMapping("/userAgreementList")
    public BaseAnswer userAgreementList(UserAgreementListParam param) {
        param.setSortType(1);
        PageData<UserAgreementListVO> userAgreementListVOS = userService.userAgreementList(param,false);
        return BaseAnswer.success(userAgreementListVOS);
    }

    /**
     * (OS)搜索用户协议审核列表（提交审核时间倒序）
     */
    @GetMapping("/userAgreementAuditList")
    public BaseAnswer userAgreementAuditList(UserAgreementListParam param) {
        param.setSortType(2);
        PageData<UserAgreementListVO> userAgreementListVOS = userService.userAgreementList(param,false);
        return BaseAnswer.success(userAgreementListVOS);
    }


    /**
     * (小程序)查看用户协议列表
     */
    @GetMapping("/userAgreementListFront")
    public BaseAnswer userAgreementListFront(UserAgreementListParam param) {
        param.setSortType(1);
        PageData<UserAgreementListVO> userAgreementListVOS = userService.userAgreementList(param,true);
        return BaseAnswer.success(userAgreementListVOS);
    }

    /**
     * (OS)审核用户协议
     */
    @PostMapping("/userAgreement/audit")
    public BaseAnswer<Void> auditUserAgreement(@Valid @RequestBody AuditUserAgreementParam param) {
        userService.auditUserAgreement(param);
        return new BaseAnswer<>();
    }


    /**
     * (OS)下线用户协议(产品经理取消下线状态，暂不使用此接口)
     */
    @PostMapping("/userAgreement/offline")
    public BaseAnswer<Void> userAgreementOffline(@RequestBody UserAgreementIdParam param) {
        userService.userAgreementOffline(param);
        return new BaseAnswer<>();
    }

    /**
     * (OS)删除用户协议(产品经理取消删除功能，暂不使用此接口)
     */
    @PostMapping("/userAgreement/delete")
    public BaseAnswer<Void> deleteUserAgreement(@RequestBody UserAgreementIdParam param) {
        userService.deleteUserAgreement(param);
        return new BaseAnswer<>();
    }

    /**
     * (OS)发布按钮，提交审核
     */
    @PostMapping("/userAgreement/submitAudit")
    public BaseAnswer<Void> submitAudit(@RequestBody UserAgreementIdParam param) {
        userService.submitAudit(param);
        return new BaseAnswer<>();
    }


    /**
     * (老接口，弃用)
     * @param params
     * @return
     */
    @PostMapping("/userAgreement/update")
    public BaseAnswer<Void> updateUserAgreement(@Validated @RequestBody List<UpdateUserAgreementParam> params) {
        userService.updateUserAgreement(params);
        return new BaseAnswer<>();
    }


    /**
     * 查询用户收货地址
     * @return
     */
    @GetMapping("/address/get")
    public BaseAnswer<List<UserMiniProgramAddress>> getAddress(@RequestHeader(name = Constant.HEADER_KEY_USER_ID) String userId) {
        return userService.getAddress(userId);
    }
    /**
     * 新建收货地址
     * @return
     */
    @PostMapping("/address/add")
    public BaseAnswer<Void> addAddress(@Valid @RequestBody ActivityAwardAddressParam param, @RequestHeader(name = Constant.HEADER_KEY_USER_ID) String userId) {
        return userService.addAddress(param, userId);
    }
    /**
     * 修改收货地址
     * @return
     */
    @PostMapping("/address/edit")
    public BaseAnswer<Void> editAddress(@Valid @RequestBody ActivityAwardAddressParam param, @RequestHeader(name = Constant.HEADER_KEY_USER_ID) String userId) {
        return userService.editAddress(param, userId);
    }
    /**
     * 删除收货地址
     * @return
     */
    @DeleteMapping("/address/delete")
    public BaseAnswer<Void> deleteAddress(@RequestParam("id") String id) {
        return userService.deleteAddress(id);
    }

    /**
     * 小程序用户相同手机号的角色列表
     *
     * @param userId
     * @return
     */
    @GetMapping("/role/list")
    public BaseAnswer listUserRole(@RequestHeader(name = Constant.HEADER_KEY_USER_ID) String userId) {
        List<UserRoleInfoVO> userRoleInfoVOS = userService.listUserRole(userId);
        return BaseAnswer.success(userRoleInfoVOS);
    }

    /**
     * 切换角色
     *
     * @param param
     * @return
     */
    @PostMapping("/role/switch")
    public BaseAnswer switchRole(@Valid @RequestBody SwitchRoleParam param) {
        MiniProgramLoginVO miniProgramLoginVO = userService.switchRole(param);
        return BaseAnswer.success(miniProgramLoginVO);
    }

    /**
     * 一键登录
     * @param param
     * @return
     */
    @PostMapping("/loginOneClick")
    public BaseAnswer loginOneClick(@Valid @RequestBody LoginOneClickParam param) {
        MiniProgramLoginVO miniProgramLoginVO = userService.loginOneClick(param);
        return BaseAnswer.success(miniProgramLoginVO);
    }

    @GetMapping("/sale/report")
    public BaseAnswer getSaleYearReport(@RequestParam("year") Integer year,
                                        @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis) {
        SaleYearReportVO vo = userService.getSaleYearReport(year,loginIfo4Redis);
        return BaseAnswer.success(vo);
    }

    @GetMapping("/sale/report/load2redis")
    public BaseAnswer saleReportLoad2Redis(@RequestParam("year") Integer year,
            @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis) {
        userService.saleReportLoad2Redis(year,loginIfo4Redis);
        return BaseAnswer.success("成功");
    }

    /**
     * 生成小程序用户年度销售报告
     * @param year
     */
    @PostMapping("/sale/report/generate")
    public BaseAnswer generateSaleReport(@RequestParam("year") Integer year) {
        userService.generateSaleReport(year);
        return BaseAnswer.success("成功");
    }

    /**
     * 导入年度报告活动参与次数
     */
    @PostMapping("/sale/report/importActivityPaticipants")
    public BaseAnswer importActivityPaticipants(@RequestParam("year") Integer year, @RequestPart("file") MultipartFile excel) {
        userService.importActivityPaticipants(year, excel);
        return BaseAnswer.success("成功");
    }

    /**
     * 导入spu商品类别（计收科目）
     */
    @PostMapping("/sale/report/importSpuCategoryInfo")
    public BaseAnswer importSpuCategoryInfo(@RequestPart("file") MultipartFile excel) {
        userService.importSpuCategoryInfo(excel);
        return BaseAnswer.success("成功");
    }

    @GetMapping("/sale/report/viewed")
    public BaseAnswer isSaleReportViewed(@RequestParam("year") Integer year,
                                        @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis) {
        Boolean vo = userService.isSaleReportViewed(year,loginIfo4Redis);
        return BaseAnswer.success(vo);
    }

    @GetMapping("/getLocationByUserId")
    public BaseAnswer<UserLocationVO> getLocationByUserId(@RequestParam("id") String id) {
        UserLocationVO userLocationVO = userService.getLocationByUserId(id);
        return BaseAnswer.success(userLocationVO);
    }

    /**
     * 清除缓存
     * @param param
     * @return
     */
    @PostMapping("/cache/clear")
    public BaseAnswer clearCache(@RequestBody ClearCacheParam param) {
        userService.clearCache(param);
        return BaseAnswer.success(null);
    }

    /**
     * 导入业管账号
     */
    @PostMapping("/importBMUser")
    public BaseAnswer importBMUser(@RequestPart("file") MultipartFile excel) {
        userService.importBMUser(excel);
        return BaseAnswer.success("成功");
    }

    /**
     * 业管用户创建
     * @param param
     * @return
     */
    @PostMapping("/create")
    public BaseAnswer create(@Valid @RequestBody UserMiniProgramParam param) {
        userService.create(param);
        return BaseAnswer.success("成功");
    }
}
