package com.chinamobile.retail.controller.miniprogram;

import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.common.Constant;
import com.chinamobile.iot.sc.mode.PageData;
import com.chinamobile.retail.pojo.entity.ContractProvinceInfo;
import com.chinamobile.retail.pojo.param.miniprogram.PageProductParam;
import com.chinamobile.retail.pojo.param.miniprogram.ProductDetailParam;
import com.chinamobile.retail.pojo.param.miniprogram.SearchProductParam;
import com.chinamobile.retail.pojo.param.miniprogram.WebSearchProductParam;
import com.chinamobile.retail.pojo.vo.MiniProgramProductDetailVO;
import com.chinamobile.retail.pojo.vo.MiniProgramProductListVO;
import com.chinamobile.retail.pojo.vo.ShareUrlVO;
import com.chinamobile.retail.pojo.vo.miniprogram.ProductNavigationDirectoryVO;
import com.chinamobile.retail.pojo.vo.miniprogram.ProvinceInfoVO;
import com.chinamobile.retail.service.IProductService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/5/22 10:41
 * @description TODO
 */
@RestController
@RequestMapping("/miniprogram/product")
public class ProductController {

    @Resource
    private IProductService productService;

    @GetMapping("/page")
    public BaseAnswer pageProduct(PageProductParam param) {
        PageData<MiniProgramProductListVO> pageData = productService.pageMiniProgramProduct(param, param.getUserId());
        return BaseAnswer.success(pageData);
    }
    @GetMapping("/search")
    public BaseAnswer searchProduct(SearchProductParam param) {
        PageData<MiniProgramProductListVO> pageData = productService.searchProduct(param);
        return BaseAnswer.success(pageData);
    }
    @GetMapping("/shareCode")
    public BaseAnswer getShareCode(@RequestParam(name = "scene", required = false)String scene,@RequestParam(name = "page")String page,@RequestParam(name = "width",required = false)String width,@RequestParam(name = "expireInterval",required = false)Integer expireInterva) {
        String pageData = productService.getShareCode(scene,page,width,expireInterva);
        return BaseAnswer.success(pageData);
    }
    @GetMapping("/shareCodeScene")
    public BaseAnswer shareCodeScene(@RequestParam(name = "scene")String scene) {
        String pageData = productService.shareCodeScene(scene);
        return BaseAnswer.success(pageData);
    }
    @GetMapping("/shareLink")
    public BaseAnswer getShareLink(@RequestParam(name = "path")String path,@RequestParam(name = "query",required = false)String query,@RequestParam(name = "expireInterval",required = false)Integer expireInterval) {
        String pageData = productService.getShareLink(path,query,expireInterval);
        return BaseAnswer.success(pageData);
    }

    @GetMapping("/detail")
    public BaseAnswer productDetail(ProductDetailParam param) {
        MiniProgramProductDetailVO miniProgramProductDetailVO = productService.productDetail(param, param.getUserId());
        return BaseAnswer.success(miniProgramProductDetailVO);
    }

    @GetMapping("/detailByActivityId")
    public BaseAnswer productDetailByActivityId(@RequestParam(required = true) String spuCode,
                                                @RequestParam(required = false) String activityId,
                                                @RequestParam(required = false) String provinceCode) {
        MiniProgramProductDetailVO miniProgramProductDetailVO = productService.productDetailByActivityId(spuCode,activityId,provinceCode);
        return BaseAnswer.success(miniProgramProductDetailVO);
    }

    /**
     * 前端商品分享链接
     */
    @GetMapping("/shareUrl")
    public BaseAnswer getShareUrl(@RequestParam(name = "spuCode") String spuCode, @RequestParam(name = "userId") String userId) {
        ShareUrlVO shareUrlVO = productService.getShareUrl(spuCode, userId);
        return BaseAnswer.success(shareUrlVO);
    }

    @GetMapping("/search/web")
    public BaseAnswer<PageData<MiniProgramProductListVO>> searchProductForWeb(WebSearchProductParam param) {
        PageData<MiniProgramProductListVO> pageData = productService.searchProductForWeb(param);
        return BaseAnswer.success(pageData);
    }

    @GetMapping("/search/web/scene")
    public BaseAnswer<PageData<MiniProgramProductListVO>> searchProductForWebScene(WebSearchProductParam param) {
        PageData<MiniProgramProductListVO> pageData = productService.searchProductForWebScene(param);
        return BaseAnswer.success(pageData);
    }

    /**
     * 列出商品导航目录
     * @return
     */
    @GetMapping("/directory/list")
    public BaseAnswer<List<ProductNavigationDirectoryVO>> listDirectory() {
        List<ProductNavigationDirectoryVO> vos = productService.listDirectory();
        return new BaseAnswer<List<ProductNavigationDirectoryVO>>().setData(vos);
    }

    @GetMapping("/province/list")
    public BaseAnswer<List<ProvinceInfoVO>> getProvinces(){

        return productService.getProvinces();
    }
}
