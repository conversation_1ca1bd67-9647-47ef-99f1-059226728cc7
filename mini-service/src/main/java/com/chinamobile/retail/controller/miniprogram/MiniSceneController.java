package com.chinamobile.retail.controller.miniprogram;

import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.common.Constant;
import com.chinamobile.iot.sc.mode.LoginIfo4Redis;
import com.chinamobile.iot.sc.mode.PageData;
import com.chinamobile.retail.pojo.dto.SceneDirectoryDTO;
import com.chinamobile.retail.pojo.param.miniprogram.*;
import com.chinamobile.retail.pojo.vo.miniprogram.*;
import com.chinamobile.retail.service.IMiniSceneService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/7/16 09:42
 * @description TODO
 */
@RestController
@RequestMapping("/miniprogram/scene")
public class MiniSceneController {

    @Resource
    private IMiniSceneService miniSceneService;

    /*********************************** 客户端接口 START ***********************************/
    /**
     * 小程序前端获取场景目录
     */
    @GetMapping("/frontend/directory/list")
    public BaseAnswer<List<SceneDirectoryVO>> listSceneDirectory() {
        List<SceneDirectoryVO> sceneDirectoryVOS = miniSceneService.listSceneDirectory();
        return BaseAnswer.success(sceneDirectoryVOS);
    }

    /**
     * 小程序前端分页查询场景列表
     */
    @GetMapping("/frontend/list")
    public BaseAnswer<PageData<SceneFrontendItemVO>> pageSceneFrontend(@Valid PageSceneFrontendParam param) {
        PageData<SceneFrontendItemVO> sceneFrontendItemVOS = miniSceneService.pageSceneFrontend(param);
        return BaseAnswer.success(sceneFrontendItemVOS);
    }

    /**
     * 小程序前端查看场景详情
     */
    @GetMapping("/frontend/detail")
    public BaseAnswer<SceneDetailFrontendVO> getSceneDetailFrontend(@Valid SceneDetailFrontendParam param) {
        SceneDetailFrontendVO sceneDetailFrontendVO = miniSceneService.getSceneDetailFrontend(param);
        return BaseAnswer.success(sceneDetailFrontendVO);
    }

    /**
     * 小程序前端获取场景需求模板
     */
    @GetMapping("/frontend/requirement/template")
    public BaseAnswer<SceneRequirementTemplateFrontendVO> getSceneRequirementTemplate(@RequestParam("sceneId") String sceneId) {
        SceneRequirementTemplateFrontendVO sceneRequirementTemplateFrontendVO = miniSceneService.getSceneRequirementTemplate(sceneId);
        return BaseAnswer.success(sceneRequirementTemplateFrontendVO);
    }

    /**
     * 小程序提交场景方案设计
     */
    @PostMapping("/frontend/requirement/submit")
    public BaseAnswer<Void> submitSceneRequirement(@Valid @RequestBody SceneRequirementSubmitParam param, @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis) {
        miniSceneService.submitSceneRequirement(param, loginIfo4Redis);
        return new BaseAnswer<>();
    }
    /*********************************** 客户端接口 STOP ***********************************/



    /*********************************** 管理后台接口 START ***********************************/

    /**
     * 管理后台小场景配置列表分页查询
     *
     * @return
     */
    @GetMapping ("/list")
    public BaseAnswer<PageData<SceneVO>> pageSceneListBack(PageSceneListParam param, @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis) {
        PageData<SceneVO> pageData = miniSceneService.pageSceneList(param,loginIfo4Redis);
        return BaseAnswer.success(pageData);
    }
    /**
     * 管理后台小场景审批列表分页查询
     *
     * @return
     */
    @GetMapping ("/listAudit")
    public BaseAnswer<PageData<SceneVO>> pageInfoListBackAudit(PageSceneListParam param, @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis) {
        PageData<SceneVO> pageData = miniSceneService.pageSceneList(param,loginIfo4Redis);
        return BaseAnswer.success(pageData);
    }

//    /**
//     * 小程序前端获取小场景
//     * */
//    @GetMapping("/mini")
//    public BaseAnswer<SceneVO> getHome(@RequestParam(name = "userId") String userId) {
//        return BaseAnswer.success(miniSceneService.getSceneMini(userId));
//    }

    /**
     * 管理后台配置小场景详情
     *
     * @return
     */
    @GetMapping("/detail")
    public BaseAnswer<SceneVO> getDetail(@RequestParam("id") String id,
                                              @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis) {
        return BaseAnswer.success(miniSceneService.getSceneDetail(id,loginIfo4Redis));
    }
    /**
     * 管理后台审批小场景详情
     *
     * @return
     */
    @GetMapping("/detailAudit")
    public BaseAnswer<SceneVO> getDetailAudit(@RequestParam("id") String id,
                                        @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis) {
        return BaseAnswer.success(miniSceneService.getSceneDetail(id,loginIfo4Redis));
    }

    /**
     * 创建小场景
     *
     * @param param
     * @param userId
     * @return
     */
    @PostMapping("/create")
    public BaseAnswer create(@Valid @RequestBody SceneParam param,
                               @RequestHeader(name = Constant.HEADER_KEY_USER_ID) String userId) {
        miniSceneService.create(param, userId);
        return new BaseAnswer<>();
    }

    /**
     * 编辑小场景
     *
     * @param param
     * @param userId
     * @return
     */
    @PostMapping("/edit")
    public BaseAnswer edit(@Valid @RequestBody SceneParam param,
                             @RequestHeader(name = Constant.HEADER_KEY_USER_ID) String userId) {
        miniSceneService.edit(param, userId);
        return new BaseAnswer<>();
    }

    /**
     * 小场景审核
     *
     * @param param
     * @param loginIfo4Redis
     * @return
     */
    @PostMapping("/audit")
    public BaseAnswer audit(@Valid @RequestBody InfoAuditParam param,
                            @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis) {
        miniSceneService.audit(param, loginIfo4Redis);
        return new BaseAnswer<>();
    }

    /**
     * 小场景下线
     *
     * @param param
     * @param loginIfo4Redis
     * @return
     */
    @PostMapping("/offline")
    public BaseAnswer offline(@Valid @RequestBody InfoOfflineParam param,
                            @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis) {
        miniSceneService.offline(param, loginIfo4Redis);
        return new BaseAnswer<>();
    }

    /**
     * 发布小场景
     */
    @PostMapping("/publish")
    public BaseAnswer publish(@Valid @RequestBody HomePublishParam param,
                              @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis) {
        miniSceneService.publish(param,loginIfo4Redis);
        return new BaseAnswer();
    }

    /**
     * 小场景下线
     *
     * @param param
     * @param loginIfo4Redis
     * @return
     */
    @DeleteMapping("/delete")
    public BaseAnswer delete(InfoOfflineParam param,
                              @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis) {
        miniSceneService.delete(param, loginIfo4Redis);
        return new BaseAnswer<>();
    }

    @GetMapping ("/directory/list")
    public BaseAnswer<List<SceneDirectoryDTO>> getSceneDirectoryList(@RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis) {
        List<SceneDirectoryDTO> result = miniSceneService.getSceneDirectoryList(loginIfo4Redis);
        return BaseAnswer.success(result);
    }

    /**
     * 创建目录
     *
     */
    @PostMapping("/directory/create")
    public BaseAnswer createDir(@Valid @RequestBody SceneDirectoryParam param,
                             @RequestHeader(name = Constant.HEADER_KEY_USER_ID) String userId) {
        miniSceneService.createDir(param, userId);
        return new BaseAnswer<>();
    }

    /**
     * 编辑目录
     */
    @PostMapping("/directory/edit")
    public BaseAnswer editDir(@Valid @RequestBody SceneDirectoryParam param,
                           @RequestHeader(name = Constant.HEADER_KEY_USER_ID) String userId) {
        miniSceneService.editDir(param, userId);
        return new BaseAnswer<>();
    }

    @DeleteMapping("/directory/delete")
    public BaseAnswer deleteDir(InfoOfflineParam param,
                             @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis) {
        miniSceneService.deleteDir(param, loginIfo4Redis);
        return new BaseAnswer<>();
    }

    @PutMapping("/directory/sort")
    public BaseAnswer sortDir(@Valid @RequestBody @NotEmpty List<SortNavigationParam> params) {
        miniSceneService.sortDir(params);
        return new BaseAnswer<>();
    }

    /**
     * 后端需求列表
     * */
    @GetMapping ("/requirement/list")
    public BaseAnswer<PageData<SceneRequirementVO>> pageRequirementListBack(PageRequirementListParam param, @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis) {
        PageData<SceneRequirementVO> pageData = miniSceneService.pageRequirementList(param,loginIfo4Redis);
        return BaseAnswer.success(pageData);
    }

    /**
     * 后端需求详情
     * */
    @GetMapping("/requirement/detail")
    public BaseAnswer<SceneRequirementVO> getRequirementDetail(@RequestParam("id") String id,
                                         @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis) {
        return BaseAnswer.success(miniSceneService.getRequirementDetail(id,loginIfo4Redis));
    }

    /**
     * 后端需求删除
     *
     */
    @DeleteMapping("/requirement/delete")
    public BaseAnswer deleteRequirement(InfoOfflineParam param,
                             @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis) {
        miniSceneService.deleteRequirement(param, loginIfo4Redis);
        return new BaseAnswer<>();
    }

    /**
     * 后端拒绝需求
     */
    @PostMapping("/requirement/reject")
    public BaseAnswer rejectRequirement(@Valid @RequestBody InfoOfflineParam param,
                                        @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis) {
        miniSceneService.rejectRequirement(param, loginIfo4Redis);
        return new BaseAnswer<>();
    }

    /**
     * 后端拒绝需求
     */
    @PostMapping("/requirement/dispatch")
    public BaseAnswer dispatchRequirement(@Valid @RequestBody RequirementDispatchParam param,
                                        @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis) {
        miniSceneService.dispatchRequirement(param, loginIfo4Redis);
        return new BaseAnswer<>();
    }


    /**
     * 后端需求模板列表
     * */
    @GetMapping ("/template/list")
    public BaseAnswer<PageData<TemplateVO>> pageTemplateListBack(PageTemplateListParam param, @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis) {
        PageData<TemplateVO> pageData = miniSceneService.pageTemplateList(param,loginIfo4Redis);
        return BaseAnswer.success(pageData);
    }


    /**
     * 后端需求模板列表
     * */
    @GetMapping ("/template/search")
    public BaseAnswer<List<TemplateVO>> searchTemplates(PageTemplateListParam param, @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis) {
        List<TemplateVO> pageData = miniSceneService.searchTemplate(param,loginIfo4Redis);
        return BaseAnswer.success(pageData);
    }
    /**
     * 后端需求模板详情
     * */
    @GetMapping("/template/detail")
    public BaseAnswer<TemplateVO> getTemplateDetail(@RequestParam("id") String id,
                                                               @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis) {
        return BaseAnswer.success(miniSceneService.getTemplateDetail(id,loginIfo4Redis));
    }

    /**
     * 创建需求模板
     *
     */
    @PostMapping("/template/create")
    public BaseAnswer createTemplate(@Valid @RequestBody TemplateParam param,
                                @RequestHeader(name = Constant.HEADER_KEY_USER_ID) String userId) {
        miniSceneService.createTemplate(param, userId);
        return new BaseAnswer<>();
    }

    /**
     * 编辑需求模板
     */
    @PostMapping("/template/edit")
    public BaseAnswer editTemplate(@Valid @RequestBody TemplateParam param,
                              @RequestHeader(name = Constant.HEADER_KEY_USER_ID) String userId) {
        miniSceneService.editTemplate(param, userId);
        return new BaseAnswer<>();
    }


    /**
     * 后端需求模板删除
     *
     */
    @DeleteMapping("/template/delete")
    public BaseAnswer deleteTemplate(InfoOfflineParam param,
                                        @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis) {
        miniSceneService.deleteTemplate(param, loginIfo4Redis);
        return new BaseAnswer<>();
    }

    /*********************************** 管理后台接口 STOP ***********************************/

}
