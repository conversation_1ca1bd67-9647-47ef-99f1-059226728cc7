package com.chinamobile.retail.controller.miniprogram;

import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.common.Constant;
import com.chinamobile.iot.sc.mode.LoginIfo4Redis;
import com.chinamobile.iot.sc.mode.PageData;
import com.chinamobile.retail.pojo.param.miniprogram.*;
import com.chinamobile.retail.pojo.vo.miniprogram.HomeVO;
import com.chinamobile.retail.pojo.vo.miniprogram.SkuCoreComponentVO;
import com.chinamobile.retail.pojo.vo.miniprogram.SpuCoreComponentVO;
import com.chinamobile.retail.service.ICoreComponentService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/7/16 09:42
 * @description TODO
 */
@RestController
@RequestMapping("/miniprogram/core")
public class CoreComponentController {

    @Resource
    private ICoreComponentService coreComponentService;

    /**
     * 管理后台核心部件配置列表分页查询
     *
     * @return
     */
    @GetMapping ("/list")
    public BaseAnswer<PageData<SpuCoreComponentVO>> pageInfoListBack(PageCoreComponentListParam param, @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis) {
        PageData<SpuCoreComponentVO> pageData = coreComponentService.pageCoreComponentList(param,loginIfo4Redis);
        return BaseAnswer.success(pageData);
    }
    /**
     * 管理后台核心部件批列表分页查询
     *
     * @return
     */
    @GetMapping ("/listAudit")
    public BaseAnswer<PageData<SpuCoreComponentVO>> pageInfoListBackAudit(PageCoreComponentListParam param, @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis) {
        PageData<SpuCoreComponentVO> pageData = coreComponentService.pageCoreComponentList(param,loginIfo4Redis);
        return BaseAnswer.success(pageData);
    }


    /**
     * 管理后台配置核心部件详情
     *
     * @return
     */
    @GetMapping("/detail")
    public BaseAnswer<SpuCoreComponentVO> getDetail(@RequestParam("spuCode") String spuCode,
                                              @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis) {
        return BaseAnswer.success(coreComponentService.getCoreComponentDetail(spuCode,loginIfo4Redis));
    }
    /**
     * 管理后台审批核心部件详情
     *
     * @return
     */
    @GetMapping("/detailAudit")
    public BaseAnswer<HomeVO> getDetailAudit(@RequestParam("spuCode") String spuCode,
                                        @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis) {
        return BaseAnswer.success(coreComponentService.getCoreComponentDetail(spuCode,loginIfo4Redis));
    }

    /**
     * 创建核心部件
     *
     * @param param
     * @param userId
     * @return
     */
    @PostMapping("/create")
    public BaseAnswer create(@Valid @RequestBody SpuCoreComponentParam param,
                               @RequestHeader(name = Constant.HEADER_KEY_USER_ID) String userId) {
        coreComponentService.create(param, userId);
        return new BaseAnswer<>();
    }

    /**
     * 编辑核心部件
     *
     * @param param
     * @param userId
     * @return
     */
    @PostMapping("/edit")
    public BaseAnswer edit(@Valid @RequestBody SpuCoreComponentParam param,
                             @RequestHeader(name = Constant.HEADER_KEY_USER_ID) String userId) {
        coreComponentService.edit(param, userId);
        return new BaseAnswer<>();
    }

    /**
     * 核心部件审核
     *
     * @param param
     * @param loginIfo4Redis
     * @return
     */
    @PostMapping("/audit")
    public BaseAnswer audit(@Valid @RequestBody InfoAuditParam param,
                            @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis) {
        coreComponentService.audit(param, loginIfo4Redis);
        return new BaseAnswer<>();
    }

    /**
     * 核心部件下线
     *
     * @param param
     * @param loginIfo4Redis
     * @return
     */
    @PostMapping("/offline")
    public BaseAnswer offline(@Valid @RequestBody InfoOfflineParam param,
                            @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis) {
        coreComponentService.offline(param, loginIfo4Redis);
        return new BaseAnswer<>();
    }

    /**
     * 发布核心部件
     */
    @PostMapping("/publish")
    public BaseAnswer publish(@Valid @RequestBody HomePublishParam param,
                              @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis) {
        coreComponentService.publish(param,loginIfo4Redis);
        return new BaseAnswer();
    }

    /**
     * 核心部件删除
     *
     * @return
     */
    @DeleteMapping("/delete")
    public BaseAnswer<Void> delete(@RequestParam InfoOfflineParam param,
                                   @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis) {
        coreComponentService.delete(param,loginIfo4Redis);
        return BaseAnswer.success(null);
    }

    /**
     * 管理后台核心部件配置搜索SPU
     *
     * @return
     */
    @GetMapping ("/searchSpu")
    public BaseAnswer<List<SpuCoreComponentVO>> searchSpu(@RequestParam("keyword") String keyword,
                                                                 @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis) {
        List<SpuCoreComponentVO> pageData = coreComponentService.searchSpu(keyword,loginIfo4Redis);
        return BaseAnswer.success(pageData);
    }

    /**
     * 管理后台核心部件配置搜索SPU
     *
     * @return
     */
    @GetMapping ("/searchSku")
    public BaseAnswer<List<SkuCoreComponentVO>> searchSku(@RequestParam("keyword") String keyword,
                                                          @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis) {
        List<SkuCoreComponentVO> pageData = coreComponentService.searchSku(keyword,loginIfo4Redis);
        return BaseAnswer.success(pageData);
    }

    
}
