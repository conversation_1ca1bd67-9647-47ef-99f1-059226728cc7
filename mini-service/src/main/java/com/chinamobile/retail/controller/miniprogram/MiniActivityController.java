package com.chinamobile.retail.controller.miniprogram;

import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.common.Constant;
import com.chinamobile.iot.sc.mode.LoginIfo4Redis;
import com.chinamobile.iot.sc.mode.PageData;
import com.chinamobile.retail.pojo.dto.Order2CInfoDTO;
import com.chinamobile.retail.pojo.entity.MiniProgramActivityWeeklyFunAward;
import com.chinamobile.retail.pojo.param.miniprogram.*;
import com.chinamobile.retail.pojo.vo.miniprogram.*;
import com.chinamobile.retail.service.IMiniActivityService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/7/16 09:42
 * @description TODO
 */
@RestController
@RequestMapping("/miniprogram/activity")
public class MiniActivityController {

    @Resource
    private IMiniActivityService miniActivityService;

    /**
     * 发布排位赛活动
     *
     * @return
     */
    @PostMapping("/rank/publish")
    public BaseAnswer<Void> publishRankActivity(@Valid @RequestBody PublishRankActivityParam param,
                                                @RequestHeader(name = Constant.HEADER_KEY_USER_ID) String userId) {
        miniActivityService.publishRankActivity(param, userId);
        return new BaseAnswer<>();
    }

    /**
     * 发布周周乐活动
     *
     * @return
     */
    @PostMapping("/weekly/publish")
    public BaseAnswer<Void> publishWeeklyActivity(@Valid @RequestBody PublishWeeklyActivityParam param,
                                                  @RequestHeader(name = Constant.HEADER_KEY_USER_ID) String userId,
                                                  @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis) {
        miniActivityService.publishWeeklyActivity(param, userId, loginIfo4Redis);
        return new BaseAnswer<>();
    }

    /**
     * 管理后台数据看板活动排名列表查询
     *
     * @return
     */
    @PostMapping("/data/rankList")
    public BaseAnswer getDataRankList(@Valid @RequestBody ActivityDataRankParam param, @RequestHeader(name = Constant.HEADER_KEY_USER_ID) String userId) {
        PageData<ActivityDataRankVO> pageData = miniActivityService.getDataRankList(param, userId);
        return BaseAnswer.success(pageData);
    }

    /**
     * 小程序活动排名列表查询
     *
     * @return
     */
    @PostMapping("/data/rankListMiniProgram")
    public BaseAnswer getDataRankListMiniProgram(@Valid @RequestBody ActivityDataRankParam param, @RequestHeader(name = Constant.HEADER_KEY_USER_ID) String userId) {
        PageData<ActivityDataRankMiniprogramVO> pageData = miniActivityService.getDataRankListMiniProgram(param, userId);
        return BaseAnswer.success(pageData);
    }

    /**
     * 管理后台配置活动列表分页查询
     *
     * @return
     */
    @PostMapping("/list/page")
    public BaseAnswer pageActivityList(@Valid @RequestBody PageActivityListParam param, @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis) {
        PageData<PageActivityVO> pageData = miniActivityService.pageActivityList(param, loginIfo4Redis);
        return BaseAnswer.success(pageData);
    }

    /**
     * 管理后台审核活动列表分页查询
     *
     * @return
     */
    @PostMapping("/list/pageAudit")
    public BaseAnswer pageActivityListAudit(@Valid @RequestBody PageActivityListParam param, @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis) {
        PageData<PageActivityVO> pageData = miniActivityService.pageActivityList(param, loginIfo4Redis);
        return BaseAnswer.success(pageData);
    }

    /**
     * 获取活动详情配置
     *
     * @param activityId
     * @return
     */
    @GetMapping("/detail")
    public BaseAnswer getActivityDetail(@RequestParam(name = "activityId") String activityId,
                                        @RequestParam(name = "flag", required = false, defaultValue = "0") Integer flag,
                                        @RequestHeader(name = Constant.HEADER_KEY_USER_ID) String userId,
                                        @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis) {
        ActivityDetailVO activityDetail = miniActivityService.getActivityDetail(activityId, flag, userId, loginIfo4Redis);
        return BaseAnswer.success(activityDetail);
    }

    /**
     * 获取活动详情审核
     *
     * @param activityId
     * @return
     */
    @GetMapping("/detailAudit")
    public BaseAnswer getActivityDetailAudit(@RequestParam(name = "activityId") String activityId,
                                             @RequestParam(name = "flag", required = false, defaultValue = "0") Integer flag,
                                             @RequestHeader(name = Constant.HEADER_KEY_USER_ID) String userId,
                                             @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis) {
        ActivityDetailVO activityDetail = miniActivityService.getActivityDetail(activityId, flag, userId, loginIfo4Redis);
        return BaseAnswer.success(activityDetail);
    }

    /**
     * 启用/停用活动
     *
     * @param param
     * @param userId
     * @return
     */
    @PostMapping("/activate")
    public BaseAnswer activate(@Valid @RequestBody ActivityActivateParam param,
                               @RequestHeader(name = Constant.HEADER_KEY_USER_ID) String userId) {
        miniActivityService.activate(param, userId);
        return new BaseAnswer<>();
    }

    /**
     * 下线活动
     *
     * @param id
     * @param userId
     * @return
     */
    @GetMapping("/offline")
    public BaseAnswer offline(@RequestParam String id,
                              @RequestHeader(name = Constant.HEADER_KEY_USER_ID) String userId) {
        miniActivityService.offline(id, userId);
        return new BaseAnswer<>();
    }

    /**
     * 活动审核
     *
     * @param param
     * @param loginIfo4Redis
     * @return
     */
    @PostMapping("/audit")
    public BaseAnswer audit(@Valid @RequestBody ActivityAuditParam param,
                            @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis) {
        miniActivityService.audit(param, loginIfo4Redis);
        return new BaseAnswer<>();
    }

    /**
     * 获取排位赛统计数据
     *
     * @return
     */
    @GetMapping("/rank/getRankActivityStatistics")
    public BaseAnswer<ActivityStatisticsVO> getRankActivityStatistics(@RequestParam String activityId, @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis) {
        return miniActivityService.getRankActivityStatistics(activityId, loginIfo4Redis);
    }


    /**
     * 获取小程序前台首页活动列表
     *
     * @return
     */
    @PostMapping("/list/frontPage")
    public BaseAnswer getListFrontPage(@Valid @RequestBody PageActivityListFrontParam param,
                                       @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis
    ) {
        PageData<PageActivityVO> pageData = miniActivityService.getListFrontPage(param, loginIfo4Redis);
        return BaseAnswer.success(pageData);
    }

    /**
     * 获取小程序前台首页活动详情-排位赛
     *
     * @return
     */
    @GetMapping("/rank/frontPage/detail")
    public BaseAnswer<PageFrontRankDetailVO> getFrontPageRankDetail(@RequestParam String activityId, @RequestHeader(name = Constant.HEADER_KEY_USER_ID) String userId) throws Exception {
        PageFrontRankDetailVO pageData = miniActivityService.getFrontPageRankDetail(activityId, userId);
        return BaseAnswer.success(pageData);
    }

    /**
     * 获取小程序前台首页活动详情-排位周周乐
     *
     * @return
     */
    @GetMapping("/weekly/frontPage/detail")
    public BaseAnswer<PageFrontWeeklyDetailVO> getFrontPageWeeklyDetail(@RequestParam String activityId, @RequestHeader(name = Constant.HEADER_KEY_USER_ID) String userId) throws Exception {

        PageFrontWeeklyDetailVO pageData = miniActivityService.getFrontPageWeeklyDetail(activityId, userId);
        return BaseAnswer.success(pageData);
    }


    /**
     * 排位周周乐抽奖
     *
     * @return
     */
    @GetMapping("/weekly/lottery")
    public BaseAnswer<MiniProgramActivityWeeklyFunAward> weeklyLottery(@RequestParam String activityId, @RequestHeader(name = Constant.HEADER_KEY_USER_ID) String userId) {
        MiniProgramActivityWeeklyFunAward pageData = miniActivityService.weeklyLottery(activityId, userId);
        return BaseAnswer.success(pageData);
    }

    /**
     * 获取已获得的实物奖品列表
     *
     * @return
     */
    @GetMapping("/getOwnAward")
    public BaseAnswer<PageData<ActivityAwardListVO>> getOwnAward(@RequestParam("pageNum") Integer pageNum,
                                                                 @RequestParam("pageSize") Integer pageSize, @RequestHeader(name = Constant.HEADER_KEY_USER_ID) String userId) {

        return miniActivityService.getOwnAward(pageNum, pageSize, userId);
    }

    /**
     * 获取用户未领取的奖品数，消息提示
     *
     * @return
     */
    @GetMapping("/getOwnAwardNotReceived")
    public BaseAnswer<Integer> getOwnAwardNotReceived(@RequestHeader(name = Constant.HEADER_KEY_USER_ID) String userId) {

        return miniActivityService.getOwnAwardNotReceived(userId);
    }


    /**
     * 选择奖品收货地址
     *
     * @return
     */
    @GetMapping("/choiceAwardAddress")
    public BaseAnswer<Void> choiceAwardAddress(@RequestParam String activityId, @RequestParam String awardId, @RequestParam String addressId, @RequestHeader(name = Constant.HEADER_KEY_USER_ID) String userId) {

        return miniActivityService.choiceAwardAddress(activityId, awardId, addressId, userId);
    }

    /**
     * 单号配置
     *
     * @return
     */
    @GetMapping("/deliveryAward")
    public BaseAnswer<Void> deliveryAward(@RequestParam String activityId, @RequestParam String awardId, @RequestParam String logisticsCode, @RequestParam String userId) {

        return miniActivityService.deliveryAward(activityId, awardId, logisticsCode, userId);
    }

    /**
     * 数据看板活动排名删除
     *
     * @return
     */
    @DeleteMapping("/data/delete")
    public BaseAnswer<Void> deleteDataRank(@RequestParam String id, @RequestHeader(name = Constant.HEADER_KEY_USER_ID) String userId) {

        return miniActivityService.deleteDataRank(id, userId);
    }

    /**
     * 活动中奖记录导出
     */
    @GetMapping("/data/export")
    public void exportDataRank(@RequestParam String activityId, @RequestHeader(name = Constant.HEADER_KEY_USER_ID) String userId) {
        miniActivityService.exportDataRank(activityId, userId);
    }

    /**
     * 活动排名订单查看
     */
    @PostMapping("/data/orderInfo")
    public BaseAnswer<PageData<Order2CInfoDTO>> getDataRankOrderInfo(@Valid @RequestBody ActivityDataRankParam param) {
        PageData<Order2CInfoDTO> pageData = miniActivityService.getDataRankOrderInfo(param, true);
        return BaseAnswer.success(pageData);
    }

    /**
     * 活动排名订单导出
     */
    @GetMapping("/data/orderInfo/export")
    public void exportDataRankOrderInfo(@RequestParam String activityId, @RequestParam String userId) {
        miniActivityService.exportDataRankOrderInfo(activityId, userId);
    }

    /**
     * 获取访问人数
     */
    @GetMapping("/data/visitCount")
    public BaseAnswer<List<ActivityAccessVO>> getDataRankVisitCount(@RequestParam String activityId, @RequestHeader(name = Constant.HEADER_KEY_USER_ID) String userId) {
        List<ActivityAccessVO> countList = miniActivityService.getDataRankVisitCount(activityId, userId);
        return BaseAnswer.success(countList);
    }

    /**
     * 获取积分供应商列表
     *
     * @return
     */
    @GetMapping("/supplier/list")
    public BaseAnswer<List<ActivityPointSupplierVO>> getPointSupplierList() {
        List<ActivityPointSupplierVO> activityPointSupplierVOS = miniActivityService.getPointSupplierList();
        return BaseAnswer.success(activityPointSupplierVOS);
    }

    /**
     * 搜索SPU信息
     *
     * @param spuCode
     * @return
     */
    @GetMapping("/search/spu")
    public BaseAnswer<List<ActivitySearchSpuVO>> searchSpuByCode(@RequestParam(value = "spuCode") String spuCode) {
        List<ActivitySearchSpuVO> activitySearchSpuVOS = miniActivityService.searchSpuByCode(spuCode);
        return BaseAnswer.success(activitySearchSpuVOS);
    }
    /**
     * 搜索SPU信息,包括未发布
     *
     * @param spuCode
     * @return
     */
    @GetMapping("/search/spuAll")
    public BaseAnswer<List<ActivitySearchSpuVO>> searchSpuAllByCode(@RequestParam(value = "spuCode") String spuCode) {
        List<ActivitySearchSpuVO> activitySearchSpuVOS = miniActivityService.searchSpuAllByCode(spuCode);
        return BaseAnswer.success(activitySearchSpuVOS);
    }

    /**
     * 搜索用户信息
     *
     * @param phone
     * @return
     */
    @GetMapping("/search/user")
    public BaseAnswer<List<ActivitySearchUserVO>> searchUserByPhone(@RequestParam(value = "phone") String phone) {
        List<ActivitySearchUserVO> activitySearchUserVOS = miniActivityService.searchUserByPhone(phone);
        return BaseAnswer.success(activitySearchUserVOS);
    }

    /**
     * 获取全国的省份信息
     *
     * @return
     */
    @GetMapping("/regions")
    public BaseAnswer<List<ActivityProvinceVO>> getAllRegions() {
        List<ActivityProvinceVO> activityProvinceVOS = miniActivityService.getAllRegions();
        return BaseAnswer.success(activityProvinceVOS);
    }

    @GetMapping("/search")
    public BaseAnswer<List<PageActivityVO>> searchMiniActivity(@RequestParam(value = "keyWord", required = false) String keyWord,
                                                               @RequestParam(value = "provinceCode") String provinceCode) {
        List<PageActivityVO> pageData = miniActivityService.searchMiniActivity(keyWord, provinceCode);
        return BaseAnswer.success(pageData);
    }

    /**
     * 排名赛手动报名
     */
    @PostMapping("/rank/enroll")
    public BaseAnswer enrollRank(@Valid @RequestBody ActivityEnrollParam param,
                                 @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis) {
        miniActivityService.enrollRank(param, loginIfo4Redis);
        return new BaseAnswer<>();
    }

    @PostMapping("/load2Redis")
    public BaseAnswer load2Redis() {
        miniActivityService.loadAllActivity2Redis();
        return new BaseAnswer<>();
    }

    /**
     * 活动删除
     *
     * @return
     */
    @DeleteMapping("/delete")
    public BaseAnswer<Void> delete(@RequestParam String id) {
        miniActivityService.delete(id);
        return BaseAnswer.success(null);
    }
}
