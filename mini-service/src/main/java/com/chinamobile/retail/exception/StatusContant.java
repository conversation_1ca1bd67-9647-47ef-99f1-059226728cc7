package com.chinamobile.retail.exception;

import com.chinamobile.iot.sc.exceptions.BaseErrorConstant;
import com.chinamobile.iot.sc.exceptions.ExcepStatus;

import static com.chinamobile.iot.sc.exceptions.ExcepStatus.createInstance;

/**
 * created by l<PERSON><PERSON><PERSON> on 2022/8/31 10:48
 */
public class StatusContant extends BaseErrorConstant {

    private static final String PREF = "70";

    public static final ExcepStatus PRODUCT_NOT_FOUND = createInstance(PREF + "001", "商品不存在");

    public static final ExcepStatus BATCH_POINT_STATUS_ERROR = createInstance(PREF + "002", "批量操作时，商品积分状态必须一致");

    public static final ExcepStatus MUST_PAUSE = createInstance(PREF + "003", "暂停时，才可配置商品积分");

    public static final ExcepStatus ROLE_NOT_FOUNT = createInstance(PREF + "004", "合伙人角色不存在");

    public static final ExcepStatus POINT_EXCEL_EXPORT_ERROR = createInstance(PREF + "005", "合伙人积分导出失败");

    public static final ExcepStatus POINT_EXCEL_IMPORT_ERROR = createInstance(PREF + "006", "合伙人积分兑换导入失败");

    public static final ExcepStatus ROLE_ERROR = createInstance(PREF + "007", "合伙人角色错误");

    public static final ExcepStatus ROLE_POINT_NOT_FOUNT = createInstance(PREF + "008", "合伙人角色积分配置不存在");

    public static final ExcepStatus SUPPLIER_ERROR = createInstance(PREF + "009", "积分供应商错误");

    public static final ExcepStatus ASSOCIATE_PRODUCT_ERROR = createInstance(PREF + "010", "关联商品错误错误");

    public static final ExcepStatus ORDER_POINT_ERROR = createInstance(PREF + "011", "订单积分错误");

    public static final ExcepStatus USER_NOT_FOUNT = createInstance(PREF + "012", "合伙人用户不存在");

    public static final ExcepStatus NO_DATA = createInstance(PREF + "013", "无数据");

    public static final ExcepStatus PONIT_PERCENT_ERROR = createInstance(PREF + "015", "积分百分比必须介于0和100之间");

    public static final ExcepStatus PARTNER_PONIT_NOT_ENOUGH = createInstance(PREF + "016", "用户可兑换积分不足");
    public static final ExcepStatus POINT_EXCHANGE_EXCEL_EXPORT_ERROR = createInstance(PREF + "017", "积分兑换失败清单导出失败");

    public static final ExcepStatus PARTNER_POINT_NOT_EXIST = createInstance(PREF + "018", "合伙人该供应商下没有积分记录");


    public static final ExcepStatus EXCHANGE_PLAN_SUBMIT_RUNNING = createInstance(PREF + "017", "当前有未完成初步核算的兑换任务，无法发起兑换，请稍后再试");

    public static final ExcepStatus EXCHANGE_PLAN_EXPIRED = createInstance(PREF + "018", "兑换任务已过期，无法提交");

    public static final ExcepStatus EXCHANGE_PLAN_USER_ERROR = createInstance(PREF + "019", "提交兑换任务用户不是发起兑换的用户，无法提交");

    public static final ExcepStatus LAKALA_CASH_ERROR = createInstance(PREF + "020", "调用拉卡拉提现接口出错");

    public static final ExcepStatus LAKALA_SIGN_ERROR = createInstance(PREF + "021", "拉卡拉验签失败");

    public static final ExcepStatus LAKALA_RESULT_CALL_ERROR = createInstance(PREF + "022", "拉卡拉结果回调异常");

    public static final ExcepStatus LAKALA_GET_TRADE_INFO_ERROR = createInstance(PREF + "023", "拉卡拉查询提现状态异常");

    public static final ExcepStatus SUPPLIER_NO_UNIQUE_ID = createInstance(PREF + "024", "积分供应商没有唯一编码，无法发起兑换");


    /**
     * 对象存储相关
     */
    public static final ExcepStatus OSS_UPLOAD_SAVE_LOCAL_ERROR = createInstance(PREF + "200", "文件转存本地错误");
    public static final ExcepStatus OSS_UPLOAD_ERROR = createInstance(PREF + "201", "上传文件失败");
    public static final ExcepStatus OSS_DEL_OR_SETEXPIRED_ERROR = createInstance(PREF + "202", "删除(或过期设置)文件失败");
    public static final ExcepStatus OSS_QUERY_ERROR = createInstance(PREF + "203", "获取对象存储地址失败");
    public static final ExcepStatus OSS_COPY_ERROR = createInstance(PREF + "204", "复制存储数据失败");
    public static final ExcepStatus UNEXIST_FILE_ERROR = createInstance(PREF + "205", "文件不存在");
    public static final ExcepStatus UNEXIST_DELFILE_ERROR = createInstance(PREF + "206", "待删除文件不存在");
    public static final ExcepStatus NO_UPLOAD_FILE = createInstance(PREF + "207", "无上传文件");
    public static final ExcepStatus OSS_DEL_ERROR = createInstance(PREF + "208", "删除文件失败！");

    public static final ExcepStatus MINI_PROGRAM_ACTIVITY_NOT_EXIST = createInstance(PREF + "301", "活动不存在");
    public static final ExcepStatus MINI_PROGRAM_ACTIVITY_DEACTIVATE_STATUS_WRONG = createInstance(PREF + "302", "非发布和已下线状态，不能删除");
    public static final ExcepStatus MINI_PROGRAM_ACTIVITY_CANNOT_EDIT = createInstance(PREF + "303", "不能编辑审核中,已通过或已下线的活动");
    public static final ExcepStatus MINI_PROGRAM_ACTIVITY_AUDIT_NO_PERMISSION = createInstance(PREF + "304", "当前角色不是业务管理员，没有审核权限");
    public static final ExcepStatus MINI_PROGRAM_ACTIVITY_AUDIT_STATUS_WRONG = createInstance(PREF + "305", "当前状态的活动无法审核");
    public static final ExcepStatus MINI_PROGRAM_ACTIVITY_RANK_NOT_EXIST = createInstance(PREF + "306", "活动信息不存在");
    public static final ExcepStatus MINI_PROGRAM_ACTIVITY_AWARD_NOT_EXIST = createInstance(PREF + "307", "活动奖品不存在");
    public static final ExcepStatus MINI_PROGRAM_ACTIVITY_USER_NOT_EXIST = createInstance(PREF + "308", "活动用户不存在");
    public static final ExcepStatus MINI_PROGRAM_ADDRESS_OVER_LIMIT = createInstance(PREF + "309", "活动用户收货地址不能超过10个");
    public static final ExcepStatus MINI_PROGRAM_ACTIVITY_USER_ADDRESS_NOT_EXIST = createInstance(PREF + "310", "活动用户收货地址不存在");
    public static final ExcepStatus MINI_PROGRAM_ACTIVITY_USER_AWARD_NOT_EXIST = createInstance(PREF + "311", "用户获奖记录不存在");
    public static final ExcepStatus MINI_PROGRAM_ACTIVITY_TYPR_ERROR = createInstance(PREF + "312", "活动类型错误");
    public static final ExcepStatus MINI_PROGRAM_ACTIVITY_USER_DRAW = createInstance(PREF + "313", "用户已抽奖");
    public static final ExcepStatus MINI_PROGRAM_ACTIVITY_NOT_UNDER_WAY = createInstance(PREF + "314", "活动状态不为进行中");

    /**资讯*/
    public static final ExcepStatus MINI_PROGRAM_INFO_NOT_EXIST = createInstance(PREF+"331","资讯不存在");
    public static final ExcepStatus MINI_PROGRAM_INFO_CANNOT_EDIT = createInstance(PREF+"332","不能编辑审核中或已通过的资讯");
    public static final ExcepStatus MINI_PROGRAM_INFO_AUDIT_NO_PERMISSION = createInstance(PREF+"333","当前角色不是业务管理员，没有审核权限");
    public static final ExcepStatus MINI_PROGRAM_INFO_AUDIT_STATUS_WRONG = createInstance(PREF+"334","当前状态的资讯无法审核");
    public static final ExcepStatus MINI_PROGRAM_USER_DELETE_WRONG = createInstance(PREF+"335","奖品已发放，无法删除");

    public static final ExcepStatus MINI_PROGRAM_KNOWLEDGE_ERROR = createInstance(PREF + "336", "当前状态，无法下架问答");
    public static final ExcepStatus MINI_PROGRAM_KNOWLEDGE_NOT_EXIST = createInstance(PREF + "337", "当前状态，无法下架问答");
    public static final ExcepStatus MINI_PROGRAM_KNOWLEDGE_NOT_EDIT = createInstance(PREF + "338", "不能编辑审核状态为通过的问答");
    public static final ExcepStatus MINI_PROGRAM_KNOWLEDGE_NOT_FOUND = createInstance(PREF + "340", "不能编辑审核状态为通过的问答");
    public static final ExcepStatus MINI_PROGRAM_KNOWLEDGE_NOT_AUDIT = createInstance(PREF + "339", "不能编辑审核状态为通过的问答");
    public static final ExcepStatus MINI_PROGRAM_KNOWLEDGE_NO_PERMISSION = createInstance(PREF + "341", "当前角色不是业务管理员，没有权限操作");
    public static final ExcepStatus MINI_PROGRAM_INFO_PUBLISH_WRONG_STATUS = createInstance(PREF + "342", "只能发布已上传状态的的素材，驳回的素材请重新编辑后发布");
    public static final ExcepStatus MINI_PROGRAM_USER_NOT_EXIST = createInstance(PREF + "343", "小程序用户不存在");

    public static final ExcepStatus MINI_PROGRAM_HOME_NOT_EXIST = createInstance(PREF+"351","首页不存在");
    public static final ExcepStatus MINI_PROGRAM_HOME_CANNOT_EDIT = createInstance(PREF+"352","不能编辑审核中或已通过的首页");
    public static final ExcepStatus MINI_PROGRAM_POPULAR_MORE = createInstance(PREF+"353","该类型首页个数已超限制，请先取消设置为首页");

    public static final ExcepStatus MINI_PROGRAM_SCENE_NOT_EXIST = createInstance(PREF+"361","小场景不存在");
    public static final ExcepStatus MINI_PROGRAM_SCENE_CANNOT_EDIT = createInstance(PREF+"362","不能编辑审核中或已通过的小场景");
    public static final ExcepStatus MINI_PROGRAM_SCENE_REQUIREMENT_TEMPLATE_NOT_EXIST = createInstance(PREF+"363","场景需求模板不存在");

    public static final ExcepStatus MINI_PROGRAM_TEMPLATE_NOT_EXIST = createInstance(PREF+"371","需求模板不存在");

    /**资讯*/
    public static final ExcepStatus MINI_PROGRAM_CORE_NOT_EXIST = createInstance(PREF+"381","核心部件不存在");
    public static final ExcepStatus MINI_PROGRAM_CORE_CANNOT_EDIT = createInstance(PREF+"382","不能编辑审核中或已通过的核心部件");

    public static final ExcepStatus MINI_PROGRAM_AGREEMENT_NOT_EXIST = createInstance(PREF+"384","用户协议不存在");
}