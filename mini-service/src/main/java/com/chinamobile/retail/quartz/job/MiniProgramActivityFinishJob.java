package com.chinamobile.retail.quartz.job;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.chinamobile.iot.sc.common.utils.BaseServiceUtils;
import com.chinamobile.retail.constant.ActivityAuditStatusEnum;
import com.chinamobile.retail.constant.ActivityStatusEnum;
import com.chinamobile.retail.dao.*;
import com.chinamobile.retail.pojo.entity.*;
import com.chinamobile.retail.quartz.GeneralJobData;
import com.chinamobile.retail.quartz.GeneralJobManager;
import com.chinamobile.retail.service.IMiniActivityService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.quartz.JobDataMap;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.scheduling.quartz.QuartzJobBean;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/7/17 15:43
 * @description 小程序活动结束任务，将活动状态变为已结束
 */
@Slf4j
@Component
public class MiniProgramActivityFinishJob extends QuartzJobBean {

    @Resource
    private MiniProgramActivityMapper miniProgramActivityMapper;

    @Resource
    private GeneralJobManager jobManager;
    @Resource
    MiniProgramActivityUserAwardMapper miniProgramActivityUserAwardMapper;

    @Resource
    private IMiniActivityService miniActivityService;

    @Resource
    MiniProgramActivityRankAwardMapper miniProgramActivityRankAwardMapper;
    @Resource
    MiniProgramActivityWeeklyFunAwardMapper miniProgramActivityWeeklyFunAwardMapper;
    @Resource
    private PartnerPointMapper partnerPointMapper;
    @Resource
    PointOperateMapper pointOperateMapper;
    public static ExecutorService executorService = new ThreadPoolExecutor(8, 16, 1, TimeUnit.MINUTES, new LinkedBlockingQueue<>(100000));


    @Transactional(rollbackFor = Exception.class)
    @Override
    public void executeInternal(JobExecutionContext jobExecutionContext) throws JobExecutionException {
        JobDataMap jobDataMap = jobExecutionContext.getJobDetail().getJobDataMap();
        String data = jobDataMap.getString("data");
        GeneralJobData<MiniProgramActivity> jobData = JSON.parseObject(data, new TypeReference<GeneralJobData<MiniProgramActivity>>() {
        });
        String activityId = jobData.getData().getId();
        MiniProgramActivity miniProgramActivity = miniProgramActivityMapper.selectByPrimaryKey(activityId);
        if (miniProgramActivity.getIsDelete() == 0 && ActivityAuditStatusEnum.PASSED.getStatus().equals(miniProgramActivity.getAuditStatus())) {
            Date now = new Date();
            miniProgramActivity.setStatus(ActivityStatusEnum.FINISH.getStatus());
            miniProgramActivity.setUpdateTime(now);
            miniProgramActivityMapper.updateByPrimaryKeySelective(miniProgramActivity);
//            miniActivityService.syncLoadActivity2Redis(miniProgramActivity.getId());
            //活动结束，更新未领取奖品状态为已失效
            List<MiniProgramActivityUserAward> miniProgramActivityUserAwards = miniProgramActivityUserAwardMapper.selectByExample(new MiniProgramActivityUserAwardExample().createCriteria().andActivityIdEqualTo(activityId).andStatusEqualTo(0).example());
            if (CollectionUtils.isNotEmpty(miniProgramActivityUserAwards)) {
                //产品规定，奖品不失效
//                miniProgramActivityUserAwards.forEach(miniProgramActivityUserAward -> {
//                    miniProgramActivityUserAward.setStatus(3);
//                    miniProgramActivityUserAward.setUpdateTime(now);
//                    miniProgramActivityUserAwardMapper.updateByPrimaryKeySelective(miniProgramActivityUserAward);
//                });
                //活动结束，自动将用户积分发给用户 需要异步进行，因为上面重新计算排名比较费时间
                executorService.execute(() -> {
                    //根据活动id，查询积分供应商id
                    //获取该活动所有用户
                    for (MiniProgramActivityUserAward user : miniProgramActivityUserAwards) {
                        //保存奖品为积分的用户的总积分
                        if (miniProgramActivity.getActivityType() == 1) {
                            //排位赛
                            MiniProgramActivityRankAward miniProgramActivityRankAward = miniProgramActivityRankAwardMapper.selectByPrimaryKey(user.getAwardId());
                            if (miniProgramActivityRankAward != null && miniProgramActivityRankAward.getType() == 1) {
                                PartnerPointExample partnerPointExample = new PartnerPointExample();
                                PartnerPointExample.Criteria partnerCriteria = partnerPointExample.createCriteria();
                                partnerCriteria.andSupplierIdEqualTo(miniProgramActivityRankAward.getSupplierId());
                                partnerCriteria.andPartnerIdEqualTo(user.getUserId());
                                partnerCriteria.andChannelEqualTo(1);
                                List<PartnerPoint> partnerPoints = partnerPointMapper.selectByExample(partnerPointExample);
                                PartnerPoint partnerPoint = null;
                                Date time = new Date();
                                boolean created = false;
                                if (CollectionUtils.isEmpty(partnerPoints)) {
                                    created = true;
                                    partnerPoint = new PartnerPoint();
                                    partnerPoint.setId(BaseServiceUtils.getId());
                                    partnerPoint.setPartnerId(user.getUserId());
                                    partnerPoint.setSupplierId(miniProgramActivityRankAward.getSupplierId());
                                    partnerPoint.setCreateTime(time);
                                    partnerPoint.setChannel(1);

                                } else {
                                    partnerPoint = partnerPoints.get(0);
                                }
                                //活动积分1就是1元
                                partnerPoint.setTotal(partnerPoint.getTotal() != null ? partnerPoint.getTotal() + miniProgramActivityRankAward.getPoints() * 1000 : miniProgramActivityRankAward.getPoints() * 1000);
                                partnerPoint.setAvailable(partnerPoint.getAvailable() != null ? (partnerPoint.getAvailable() + miniProgramActivityRankAward.getPoints() * 1000) : miniProgramActivityRankAward.getPoints() * 1000);
                                partnerPoint.setUpdateTime(time);


                                if (created) {
                                    partnerPointMapper.insertSelective(partnerPoint);
                                } else {
                                    partnerPointMapper.updateByPrimaryKeySelective(partnerPoint);
                                }
                                //添加积分记录
                                PointOperate pointOperate = new PointOperate();

                                pointOperate.setPoint(miniProgramActivityRankAward.getPoints() * 1000);
                                pointOperate.setUserId(user.getUserId());
                                pointOperate.setSupplierId(miniProgramActivityRankAward.getSupplierId());
                                pointOperate.setOperatorId(null);
                                pointOperate.setCreateTime(new Date());
                                pointOperate.setType(4);
                                pointOperate.setId(BaseServiceUtils.getId());
                                pointOperate.setActivityId(activityId);
                                pointOperate.setChannel(1);
                                pointOperateMapper.insertSelective(pointOperate);

                            }
                        } else if (miniProgramActivity.getActivityType() == 2) {
                            //周周乐
                            //排位赛
                            MiniProgramActivityWeeklyFunAward miniProgramActivityWeeklyFunAward = miniProgramActivityWeeklyFunAwardMapper.selectByPrimaryKey(user.getAwardId());
                            if (miniProgramActivityWeeklyFunAward != null && miniProgramActivityWeeklyFunAward.getType() == 1) {
                                PartnerPointExample partnerPointExample = new PartnerPointExample();
                                PartnerPointExample.Criteria partnerCriteria = partnerPointExample.createCriteria();
                                partnerCriteria.andSupplierIdEqualTo(miniProgramActivityWeeklyFunAward.getSupplierId());
                                partnerCriteria.andChannelEqualTo(1);
                                partnerCriteria.andPartnerIdEqualTo(user.getUserId());
                                List<PartnerPoint> partnerPoints = partnerPointMapper.selectByExample(partnerPointExample);
                                PartnerPoint partnerPoint = null;
                                Date time = new Date();
                                boolean created = false;
                                if (CollectionUtils.isEmpty(partnerPoints)) {
                                    created = true;
                                    partnerPoint = new PartnerPoint();
                                    partnerPoint.setId(BaseServiceUtils.getId());
                                    partnerPoint.setPartnerId(user.getUserId());
                                    partnerPoint.setSupplierId(miniProgramActivityWeeklyFunAward.getSupplierId());
                                    partnerPoint.setCreateTime(time);
                                    partnerPoint.setChannel(1);
                                } else {
                                    partnerPoint = partnerPoints.get(0);
                                }
                                partnerPoint.setTotal(partnerPoint.getTotal() != null ? partnerPoint.getTotal() + miniProgramActivityWeeklyFunAward.getPoints() * 1000 : miniProgramActivityWeeklyFunAward.getPoints() * 1000);
                                partnerPoint.setAvailable(partnerPoint.getAvailable() != null ? (partnerPoint.getAvailable() + miniProgramActivityWeeklyFunAward.getPoints() * 1000) : miniProgramActivityWeeklyFunAward.getPoints() * 1000);
                                partnerPoint.setUpdateTime(time);

                                if (created) {
                                    partnerPointMapper.insertSelective(partnerPoint);
                                } else {
                                    partnerPointMapper.updateByPrimaryKeySelective(partnerPoint);
                                }
                                //添加积分记录
                                PointOperate pointOperate = new PointOperate();

                                pointOperate.setPoint(miniProgramActivityWeeklyFunAward.getPoints() * 1000);
                                pointOperate.setUserId(user.getUserId());
                                pointOperate.setSupplierId(miniProgramActivityWeeklyFunAward.getSupplierId());
                                pointOperate.setOperatorId(null);
                                pointOperate.setCreateTime(new Date());
                                pointOperate.setType(4);
                                pointOperate.setId(BaseServiceUtils.getId());
                                pointOperate.setActivityId(activityId);
                                pointOperate.setChannel(1);
                                pointOperateMapper.insertSelective(pointOperate);
                            }
                        } else {
                            log.error("活动类型错误");

                        }

                    }
                });
            }

            log.info("执行小程序活动结束任务，活动id：{}，活动名称：{}", miniProgramActivity.getId(), miniProgramActivity.getName());
        }
        jobManager.deleteQuartzJob(jobData.getName(), jobData.getGroup());
    }
}
