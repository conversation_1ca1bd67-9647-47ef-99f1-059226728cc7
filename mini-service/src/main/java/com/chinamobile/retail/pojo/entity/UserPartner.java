package com.chinamobile.retail.pojo.entity;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;

/**
 * Created By MBG-GUI-EXTENSION https://github.com/spawpaw/mybatis-generator-gui-extension
 * Description:
 * 合作伙伴用户表
 *
 * <AUTHOR>
public class UserPartner implements Serializable {
    /**
     *
     *
     * @mbg.generated Wed Jun 04 11:36:11 CST 2025
     */
    private String userId;

    /**
     * 姓名
     *
     *
     * @mbg.generated Wed Jun 04 11:36:11 CST 2025
     */
    private String name;

    /**
     * 密码
     *
     *
     * @mbg.generated Wed Jun 04 11:36:11 CST 2025
     */
    private String pwd;

    /**
     * 电话
     *
     *
     * @mbg.generated Wed Jun 04 11:36:11 CST 2025
     */
    private String phone;

    /**
     * 备注
     *
     *
     * @mbg.generated Wed Jun 04 11:36:11 CST 2025
     */
    private String remark;

    /**
     * 角色id
     *
     *
     * @mbg.generated Wed Jun 04 11:36:11 CST 2025
     */
    private String roleId;

    /**
     *
     *
     * @mbg.generated Wed Jun 04 11:36:11 CST 2025
     */
    private Date createTime;

    /**
     *
     *
     * @mbg.generated Wed Jun 04 11:36:11 CST 2025
     */
    private Date updateTime;

    /**
     * 是否停用
     *
     *
     * @mbg.generated Wed Jun 04 11:36:11 CST 2025
     */
    private Boolean isCancel;

    /**
     * 是否注销
     *
     *
     * @mbg.generated Wed Jun 04 11:36:11 CST 2025
     */
    private Boolean isLogoff;

    /**
     * 邮箱
     *
     *
     * @mbg.generated Wed Jun 04 11:36:11 CST 2025
     */
    private String email;

    /**
     * 合作伙伴名称
     *
     *
     * @mbg.generated Wed Jun 04 11:36:11 CST 2025
     */
    private String partnerName;

    /**
     * 创建人
     *
     *
     * @mbg.generated Wed Jun 04 11:36:11 CST 2025
     */
    private String creator;

    /**
     * 是否是主合作伙伴
     *
     *
     * @mbg.generated Wed Jun 04 11:36:11 CST 2025
     */
    private Boolean isPrimary;

    /**
     * 是否发送短信
     *
     *
     * @mbg.generated Wed Jun 04 11:36:11 CST 2025
     */
    private Boolean isSend;

    /**
     * 是否是外部人员
     *
     *
     * @mbg.generated Wed Jun 04 11:36:11 CST 2025
     */
    private Boolean isExternal;

    /**
     * 账号类型，normal-正式账号，test-测试账号
     *
     *
     * @mbg.generated Wed Jun 04 11:36:11 CST 2025
     */
    private String userType;

    /**
     * 是否可以启用账号
     *
     *
     * @mbg.generated Wed Jun 04 11:36:11 CST 2025
     */
    private Boolean canEnable;

    /**
     * 产金平台状态，保理账号使用;
     * 初始化:  init
     * 审核中：checking
     * 通过：pass
     * 退回：back
     * 拒绝：reject
     * 已激活：activated
     *
     *
     * @mbg.generated Wed Jun 04 11:36:11 CST 2025
     */
    private String cjStatus;

    /**
     * 产金平台发起时间
     *
     *
     * @mbg.generated Wed Jun 04 11:36:11 CST 2025
     */
    private Date cjApplyTime;

    /**
     * 产金平台审核意见
     *
     *
     * @mbg.generated Wed Jun 04 11:36:11 CST 2025
     */
    private String cjAdvice;

    /**
     * 统一社会信用代码
     *
     *
     * @mbg.generated Wed Jun 04 11:36:11 CST 2025
     */
    private String unifiedCode;

    /**
     * 单位类型：1：非省公司 2：省公司
     *
     *
     * @mbg.generated Wed Jun 04 11:36:11 CST 2025
     */
    private String companyType;

    /**
     * 合作伙伴公司主键id
     *
     *
     * @mbg.generated Wed Jun 04 11:36:11 CST 2025
     */
    private String companyId;

    /**
     * 省域,省名称
     *
     *
     * @mbg.generated Wed Jun 04 11:36:11 CST 2025
     */
    private String province;

    /**
     * 当合作伙伴创建选择省公司是 传编码
     *
     *
     * @mbg.generated Wed Jun 04 11:36:11 CST 2025
     */
    private String beId;

    /**
     * 地市域，全地市以all表示
     *
     *
     * @mbg.generated Wed Jun 04 11:36:11 CST 2025
     */
    private String location;

    /**
     * 地市域编码，创建时是省公司的时候传，全地市以all表示
     *
     *
     * @mbg.generated Wed Jun 04 11:36:11 CST 2025
     */
    private String locationId;

    /**
     *
     * @mbg.generated Wed Jun 04 11:36:11 CST 2025
     */
    private static final long serialVersionUID = 1L;

    /**
     * This method returns the value of the database column supply_chain..user_partner.user_id
     *
     * @return the value of supply_chain..user_partner.user_id
     *
     * @mbg.generated Wed Jun 04 11:36:11 CST 2025
     */
    public String getUserId() {
        return userId;
    }

    /**
     *
     * @mbg.generated Wed Jun 04 11:36:11 CST 2025
     */
    public UserPartner withUserId(String userId) {
        this.setUserId(userId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..user_partner.user_id
     *
     * @param userId the value for supply_chain..user_partner.user_id
     *
     * @mbg.generated Wed Jun 04 11:36:11 CST 2025
     */
    public void setUserId(String userId) {
        this.userId = userId;
    }

    /**
     * This method returns the value of the database column supply_chain..user_partner.name
     *
     * @return the value of supply_chain..user_partner.name
     *
     * @mbg.generated Wed Jun 04 11:36:11 CST 2025
     */
    public String getName() {
        return name;
    }

    /**
     *
     * @mbg.generated Wed Jun 04 11:36:11 CST 2025
     */
    public UserPartner withName(String name) {
        this.setName(name);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..user_partner.name
     *
     * @param name the value for supply_chain..user_partner.name
     *
     * @mbg.generated Wed Jun 04 11:36:11 CST 2025
     */
    public void setName(String name) {
        this.name = name;
    }

    /**
     * This method returns the value of the database column supply_chain..user_partner.pwd
     *
     * @return the value of supply_chain..user_partner.pwd
     *
     * @mbg.generated Wed Jun 04 11:36:11 CST 2025
     */
    public String getPwd() {
        return pwd;
    }

    /**
     *
     * @mbg.generated Wed Jun 04 11:36:11 CST 2025
     */
    public UserPartner withPwd(String pwd) {
        this.setPwd(pwd);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..user_partner.pwd
     *
     * @param pwd the value for supply_chain..user_partner.pwd
     *
     * @mbg.generated Wed Jun 04 11:36:11 CST 2025
     */
    public void setPwd(String pwd) {
        this.pwd = pwd;
    }

    /**
     * This method returns the value of the database column supply_chain..user_partner.phone
     *
     * @return the value of supply_chain..user_partner.phone
     *
     * @mbg.generated Wed Jun 04 11:36:11 CST 2025
     */
    public String getPhone() {
        return phone;
    }

    /**
     *
     * @mbg.generated Wed Jun 04 11:36:11 CST 2025
     */
    public UserPartner withPhone(String phone) {
        this.setPhone(phone);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..user_partner.phone
     *
     * @param phone the value for supply_chain..user_partner.phone
     *
     * @mbg.generated Wed Jun 04 11:36:11 CST 2025
     */
    public void setPhone(String phone) {
        this.phone = phone;
    }

    /**
     * This method returns the value of the database column supply_chain..user_partner.remark
     *
     * @return the value of supply_chain..user_partner.remark
     *
     * @mbg.generated Wed Jun 04 11:36:11 CST 2025
     */
    public String getRemark() {
        return remark;
    }

    /**
     *
     * @mbg.generated Wed Jun 04 11:36:11 CST 2025
     */
    public UserPartner withRemark(String remark) {
        this.setRemark(remark);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..user_partner.remark
     *
     * @param remark the value for supply_chain..user_partner.remark
     *
     * @mbg.generated Wed Jun 04 11:36:11 CST 2025
     */
    public void setRemark(String remark) {
        this.remark = remark;
    }

    /**
     * This method returns the value of the database column supply_chain..user_partner.role_id
     *
     * @return the value of supply_chain..user_partner.role_id
     *
     * @mbg.generated Wed Jun 04 11:36:11 CST 2025
     */
    public String getRoleId() {
        return roleId;
    }

    /**
     *
     * @mbg.generated Wed Jun 04 11:36:11 CST 2025
     */
    public UserPartner withRoleId(String roleId) {
        this.setRoleId(roleId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..user_partner.role_id
     *
     * @param roleId the value for supply_chain..user_partner.role_id
     *
     * @mbg.generated Wed Jun 04 11:36:11 CST 2025
     */
    public void setRoleId(String roleId) {
        this.roleId = roleId;
    }

    /**
     * This method returns the value of the database column supply_chain..user_partner.create_time
     *
     * @return the value of supply_chain..user_partner.create_time
     *
     * @mbg.generated Wed Jun 04 11:36:11 CST 2025
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     *
     * @mbg.generated Wed Jun 04 11:36:11 CST 2025
     */
    public UserPartner withCreateTime(Date createTime) {
        this.setCreateTime(createTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..user_partner.create_time
     *
     * @param createTime the value for supply_chain..user_partner.create_time
     *
     * @mbg.generated Wed Jun 04 11:36:11 CST 2025
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * This method returns the value of the database column supply_chain..user_partner.update_time
     *
     * @return the value of supply_chain..user_partner.update_time
     *
     * @mbg.generated Wed Jun 04 11:36:11 CST 2025
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     *
     * @mbg.generated Wed Jun 04 11:36:11 CST 2025
     */
    public UserPartner withUpdateTime(Date updateTime) {
        this.setUpdateTime(updateTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..user_partner.update_time
     *
     * @param updateTime the value for supply_chain..user_partner.update_time
     *
     * @mbg.generated Wed Jun 04 11:36:11 CST 2025
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * This method returns the value of the database column supply_chain..user_partner.is_cancel
     *
     * @return the value of supply_chain..user_partner.is_cancel
     *
     * @mbg.generated Wed Jun 04 11:36:11 CST 2025
     */
    public Boolean getIsCancel() {
        return isCancel;
    }

    /**
     *
     * @mbg.generated Wed Jun 04 11:36:11 CST 2025
     */
    public UserPartner withIsCancel(Boolean isCancel) {
        this.setIsCancel(isCancel);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..user_partner.is_cancel
     *
     * @param isCancel the value for supply_chain..user_partner.is_cancel
     *
     * @mbg.generated Wed Jun 04 11:36:11 CST 2025
     */
    public void setIsCancel(Boolean isCancel) {
        this.isCancel = isCancel;
    }

    /**
     * This method returns the value of the database column supply_chain..user_partner.is_logoff
     *
     * @return the value of supply_chain..user_partner.is_logoff
     *
     * @mbg.generated Wed Jun 04 11:36:11 CST 2025
     */
    public Boolean getIsLogoff() {
        return isLogoff;
    }

    /**
     *
     * @mbg.generated Wed Jun 04 11:36:11 CST 2025
     */
    public UserPartner withIsLogoff(Boolean isLogoff) {
        this.setIsLogoff(isLogoff);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..user_partner.is_logoff
     *
     * @param isLogoff the value for supply_chain..user_partner.is_logoff
     *
     * @mbg.generated Wed Jun 04 11:36:11 CST 2025
     */
    public void setIsLogoff(Boolean isLogoff) {
        this.isLogoff = isLogoff;
    }

    /**
     * This method returns the value of the database column supply_chain..user_partner.email
     *
     * @return the value of supply_chain..user_partner.email
     *
     * @mbg.generated Wed Jun 04 11:36:11 CST 2025
     */
    public String getEmail() {
        return email;
    }

    /**
     *
     * @mbg.generated Wed Jun 04 11:36:11 CST 2025
     */
    public UserPartner withEmail(String email) {
        this.setEmail(email);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..user_partner.email
     *
     * @param email the value for supply_chain..user_partner.email
     *
     * @mbg.generated Wed Jun 04 11:36:11 CST 2025
     */
    public void setEmail(String email) {
        this.email = email;
    }

    /**
     * This method returns the value of the database column supply_chain..user_partner.partner_name
     *
     * @return the value of supply_chain..user_partner.partner_name
     *
     * @mbg.generated Wed Jun 04 11:36:11 CST 2025
     */
    public String getPartnerName() {
        return partnerName;
    }

    /**
     *
     * @mbg.generated Wed Jun 04 11:36:11 CST 2025
     */
    public UserPartner withPartnerName(String partnerName) {
        this.setPartnerName(partnerName);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..user_partner.partner_name
     *
     * @param partnerName the value for supply_chain..user_partner.partner_name
     *
     * @mbg.generated Wed Jun 04 11:36:11 CST 2025
     */
    public void setPartnerName(String partnerName) {
        this.partnerName = partnerName;
    }

    /**
     * This method returns the value of the database column supply_chain..user_partner.creator
     *
     * @return the value of supply_chain..user_partner.creator
     *
     * @mbg.generated Wed Jun 04 11:36:11 CST 2025
     */
    public String getCreator() {
        return creator;
    }

    /**
     *
     * @mbg.generated Wed Jun 04 11:36:11 CST 2025
     */
    public UserPartner withCreator(String creator) {
        this.setCreator(creator);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..user_partner.creator
     *
     * @param creator the value for supply_chain..user_partner.creator
     *
     * @mbg.generated Wed Jun 04 11:36:11 CST 2025
     */
    public void setCreator(String creator) {
        this.creator = creator;
    }

    /**
     * This method returns the value of the database column supply_chain..user_partner.is_primary
     *
     * @return the value of supply_chain..user_partner.is_primary
     *
     * @mbg.generated Wed Jun 04 11:36:11 CST 2025
     */
    public Boolean getIsPrimary() {
        return isPrimary;
    }

    /**
     *
     * @mbg.generated Wed Jun 04 11:36:11 CST 2025
     */
    public UserPartner withIsPrimary(Boolean isPrimary) {
        this.setIsPrimary(isPrimary);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..user_partner.is_primary
     *
     * @param isPrimary the value for supply_chain..user_partner.is_primary
     *
     * @mbg.generated Wed Jun 04 11:36:11 CST 2025
     */
    public void setIsPrimary(Boolean isPrimary) {
        this.isPrimary = isPrimary;
    }

    /**
     * This method returns the value of the database column supply_chain..user_partner.is_send
     *
     * @return the value of supply_chain..user_partner.is_send
     *
     * @mbg.generated Wed Jun 04 11:36:11 CST 2025
     */
    public Boolean getIsSend() {
        return isSend;
    }

    /**
     *
     * @mbg.generated Wed Jun 04 11:36:11 CST 2025
     */
    public UserPartner withIsSend(Boolean isSend) {
        this.setIsSend(isSend);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..user_partner.is_send
     *
     * @param isSend the value for supply_chain..user_partner.is_send
     *
     * @mbg.generated Wed Jun 04 11:36:11 CST 2025
     */
    public void setIsSend(Boolean isSend) {
        this.isSend = isSend;
    }

    /**
     * This method returns the value of the database column supply_chain..user_partner.is_external
     *
     * @return the value of supply_chain..user_partner.is_external
     *
     * @mbg.generated Wed Jun 04 11:36:11 CST 2025
     */
    public Boolean getIsExternal() {
        return isExternal;
    }

    /**
     *
     * @mbg.generated Wed Jun 04 11:36:11 CST 2025
     */
    public UserPartner withIsExternal(Boolean isExternal) {
        this.setIsExternal(isExternal);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..user_partner.is_external
     *
     * @param isExternal the value for supply_chain..user_partner.is_external
     *
     * @mbg.generated Wed Jun 04 11:36:11 CST 2025
     */
    public void setIsExternal(Boolean isExternal) {
        this.isExternal = isExternal;
    }

    /**
     * This method returns the value of the database column supply_chain..user_partner.user_type
     *
     * @return the value of supply_chain..user_partner.user_type
     *
     * @mbg.generated Wed Jun 04 11:36:11 CST 2025
     */
    public String getUserType() {
        return userType;
    }

    /**
     *
     * @mbg.generated Wed Jun 04 11:36:11 CST 2025
     */
    public UserPartner withUserType(String userType) {
        this.setUserType(userType);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..user_partner.user_type
     *
     * @param userType the value for supply_chain..user_partner.user_type
     *
     * @mbg.generated Wed Jun 04 11:36:11 CST 2025
     */
    public void setUserType(String userType) {
        this.userType = userType;
    }

    /**
     * This method returns the value of the database column supply_chain..user_partner.can_enable
     *
     * @return the value of supply_chain..user_partner.can_enable
     *
     * @mbg.generated Wed Jun 04 11:36:11 CST 2025
     */
    public Boolean getCanEnable() {
        return canEnable;
    }

    /**
     *
     * @mbg.generated Wed Jun 04 11:36:11 CST 2025
     */
    public UserPartner withCanEnable(Boolean canEnable) {
        this.setCanEnable(canEnable);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..user_partner.can_enable
     *
     * @param canEnable the value for supply_chain..user_partner.can_enable
     *
     * @mbg.generated Wed Jun 04 11:36:11 CST 2025
     */
    public void setCanEnable(Boolean canEnable) {
        this.canEnable = canEnable;
    }

    /**
     * This method returns the value of the database column supply_chain..user_partner.cj_status
     *
     * @return the value of supply_chain..user_partner.cj_status
     *
     * @mbg.generated Wed Jun 04 11:36:11 CST 2025
     */
    public String getCjStatus() {
        return cjStatus;
    }

    /**
     *
     * @mbg.generated Wed Jun 04 11:36:11 CST 2025
     */
    public UserPartner withCjStatus(String cjStatus) {
        this.setCjStatus(cjStatus);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..user_partner.cj_status
     *
     * @param cjStatus the value for supply_chain..user_partner.cj_status
     *
     * @mbg.generated Wed Jun 04 11:36:11 CST 2025
     */
    public void setCjStatus(String cjStatus) {
        this.cjStatus = cjStatus;
    }

    /**
     * This method returns the value of the database column supply_chain..user_partner.cj_apply_time
     *
     * @return the value of supply_chain..user_partner.cj_apply_time
     *
     * @mbg.generated Wed Jun 04 11:36:11 CST 2025
     */
    public Date getCjApplyTime() {
        return cjApplyTime;
    }

    /**
     *
     * @mbg.generated Wed Jun 04 11:36:11 CST 2025
     */
    public UserPartner withCjApplyTime(Date cjApplyTime) {
        this.setCjApplyTime(cjApplyTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..user_partner.cj_apply_time
     *
     * @param cjApplyTime the value for supply_chain..user_partner.cj_apply_time
     *
     * @mbg.generated Wed Jun 04 11:36:11 CST 2025
     */
    public void setCjApplyTime(Date cjApplyTime) {
        this.cjApplyTime = cjApplyTime;
    }

    /**
     * This method returns the value of the database column supply_chain..user_partner.cj_advice
     *
     * @return the value of supply_chain..user_partner.cj_advice
     *
     * @mbg.generated Wed Jun 04 11:36:11 CST 2025
     */
    public String getCjAdvice() {
        return cjAdvice;
    }

    /**
     *
     * @mbg.generated Wed Jun 04 11:36:11 CST 2025
     */
    public UserPartner withCjAdvice(String cjAdvice) {
        this.setCjAdvice(cjAdvice);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..user_partner.cj_advice
     *
     * @param cjAdvice the value for supply_chain..user_partner.cj_advice
     *
     * @mbg.generated Wed Jun 04 11:36:11 CST 2025
     */
    public void setCjAdvice(String cjAdvice) {
        this.cjAdvice = cjAdvice;
    }

    /**
     * This method returns the value of the database column supply_chain..user_partner.unified_code
     *
     * @return the value of supply_chain..user_partner.unified_code
     *
     * @mbg.generated Wed Jun 04 11:36:11 CST 2025
     */
    public String getUnifiedCode() {
        return unifiedCode;
    }

    /**
     *
     * @mbg.generated Wed Jun 04 11:36:11 CST 2025
     */
    public UserPartner withUnifiedCode(String unifiedCode) {
        this.setUnifiedCode(unifiedCode);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..user_partner.unified_code
     *
     * @param unifiedCode the value for supply_chain..user_partner.unified_code
     *
     * @mbg.generated Wed Jun 04 11:36:11 CST 2025
     */
    public void setUnifiedCode(String unifiedCode) {
        this.unifiedCode = unifiedCode;
    }

    /**
     * This method returns the value of the database column supply_chain..user_partner.company_type
     *
     * @return the value of supply_chain..user_partner.company_type
     *
     * @mbg.generated Wed Jun 04 11:36:11 CST 2025
     */
    public String getCompanyType() {
        return companyType;
    }

    /**
     *
     * @mbg.generated Wed Jun 04 11:36:11 CST 2025
     */
    public UserPartner withCompanyType(String companyType) {
        this.setCompanyType(companyType);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..user_partner.company_type
     *
     * @param companyType the value for supply_chain..user_partner.company_type
     *
     * @mbg.generated Wed Jun 04 11:36:11 CST 2025
     */
    public void setCompanyType(String companyType) {
        this.companyType = companyType;
    }

    /**
     * This method returns the value of the database column supply_chain..user_partner.company_id
     *
     * @return the value of supply_chain..user_partner.company_id
     *
     * @mbg.generated Wed Jun 04 11:36:11 CST 2025
     */
    public String getCompanyId() {
        return companyId;
    }

    /**
     *
     * @mbg.generated Wed Jun 04 11:36:11 CST 2025
     */
    public UserPartner withCompanyId(String companyId) {
        this.setCompanyId(companyId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..user_partner.company_id
     *
     * @param companyId the value for supply_chain..user_partner.company_id
     *
     * @mbg.generated Wed Jun 04 11:36:11 CST 2025
     */
    public void setCompanyId(String companyId) {
        this.companyId = companyId;
    }

    /**
     * This method returns the value of the database column supply_chain..user_partner.province
     *
     * @return the value of supply_chain..user_partner.province
     *
     * @mbg.generated Wed Jun 04 11:36:11 CST 2025
     */
    public String getProvince() {
        return province;
    }

    /**
     *
     * @mbg.generated Wed Jun 04 11:36:11 CST 2025
     */
    public UserPartner withProvince(String province) {
        this.setProvince(province);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..user_partner.province
     *
     * @param province the value for supply_chain..user_partner.province
     *
     * @mbg.generated Wed Jun 04 11:36:11 CST 2025
     */
    public void setProvince(String province) {
        this.province = province;
    }

    /**
     * This method returns the value of the database column supply_chain..user_partner.be_id
     *
     * @return the value of supply_chain..user_partner.be_id
     *
     * @mbg.generated Wed Jun 04 11:36:11 CST 2025
     */
    public String getBeId() {
        return beId;
    }

    /**
     *
     * @mbg.generated Wed Jun 04 11:36:11 CST 2025
     */
    public UserPartner withBeId(String beId) {
        this.setBeId(beId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..user_partner.be_id
     *
     * @param beId the value for supply_chain..user_partner.be_id
     *
     * @mbg.generated Wed Jun 04 11:36:11 CST 2025
     */
    public void setBeId(String beId) {
        this.beId = beId;
    }

    /**
     * This method returns the value of the database column supply_chain..user_partner.location
     *
     * @return the value of supply_chain..user_partner.location
     *
     * @mbg.generated Wed Jun 04 11:36:11 CST 2025
     */
    public String getLocation() {
        return location;
    }

    /**
     *
     * @mbg.generated Wed Jun 04 11:36:11 CST 2025
     */
    public UserPartner withLocation(String location) {
        this.setLocation(location);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..user_partner.location
     *
     * @param location the value for supply_chain..user_partner.location
     *
     * @mbg.generated Wed Jun 04 11:36:11 CST 2025
     */
    public void setLocation(String location) {
        this.location = location;
    }

    /**
     * This method returns the value of the database column supply_chain..user_partner.location_id
     *
     * @return the value of supply_chain..user_partner.location_id
     *
     * @mbg.generated Wed Jun 04 11:36:11 CST 2025
     */
    public String getLocationId() {
        return locationId;
    }

    /**
     *
     * @mbg.generated Wed Jun 04 11:36:11 CST 2025
     */
    public UserPartner withLocationId(String locationId) {
        this.setLocationId(locationId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..user_partner.location_id
     *
     * @param locationId the value for supply_chain..user_partner.location_id
     *
     * @mbg.generated Wed Jun 04 11:36:11 CST 2025
     */
    public void setLocationId(String locationId) {
        this.locationId = locationId;
    }

    /**
     *
     * @mbg.generated Wed Jun 04 11:36:11 CST 2025
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", userId=").append(userId);
        sb.append(", name=").append(name);
        sb.append(", pwd=").append(pwd);
        sb.append(", phone=").append(phone);
        sb.append(", remark=").append(remark);
        sb.append(", roleId=").append(roleId);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", isCancel=").append(isCancel);
        sb.append(", isLogoff=").append(isLogoff);
        sb.append(", email=").append(email);
        sb.append(", partnerName=").append(partnerName);
        sb.append(", creator=").append(creator);
        sb.append(", isPrimary=").append(isPrimary);
        sb.append(", isSend=").append(isSend);
        sb.append(", isExternal=").append(isExternal);
        sb.append(", userType=").append(userType);
        sb.append(", canEnable=").append(canEnable);
        sb.append(", cjStatus=").append(cjStatus);
        sb.append(", cjApplyTime=").append(cjApplyTime);
        sb.append(", cjAdvice=").append(cjAdvice);
        sb.append(", unifiedCode=").append(unifiedCode);
        sb.append(", companyType=").append(companyType);
        sb.append(", companyId=").append(companyId);
        sb.append(", province=").append(province);
        sb.append(", beId=").append(beId);
        sb.append(", location=").append(location);
        sb.append(", locationId=").append(locationId);
        sb.append("]");
        return sb.toString();
    }

    /**
     *
     * @mbg.generated Wed Jun 04 11:36:11 CST 2025
     */
    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        UserPartner other = (UserPartner) that;
        return (this.getUserId() == null ? other.getUserId() == null : this.getUserId().equals(other.getUserId()))
            && (this.getName() == null ? other.getName() == null : this.getName().equals(other.getName()))
            && (this.getPwd() == null ? other.getPwd() == null : this.getPwd().equals(other.getPwd()))
            && (this.getPhone() == null ? other.getPhone() == null : this.getPhone().equals(other.getPhone()))
            && (this.getRemark() == null ? other.getRemark() == null : this.getRemark().equals(other.getRemark()))
            && (this.getRoleId() == null ? other.getRoleId() == null : this.getRoleId().equals(other.getRoleId()))
            && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()))
            && (this.getUpdateTime() == null ? other.getUpdateTime() == null : this.getUpdateTime().equals(other.getUpdateTime()))
            && (this.getIsCancel() == null ? other.getIsCancel() == null : this.getIsCancel().equals(other.getIsCancel()))
            && (this.getIsLogoff() == null ? other.getIsLogoff() == null : this.getIsLogoff().equals(other.getIsLogoff()))
            && (this.getEmail() == null ? other.getEmail() == null : this.getEmail().equals(other.getEmail()))
            && (this.getPartnerName() == null ? other.getPartnerName() == null : this.getPartnerName().equals(other.getPartnerName()))
            && (this.getCreator() == null ? other.getCreator() == null : this.getCreator().equals(other.getCreator()))
            && (this.getIsPrimary() == null ? other.getIsPrimary() == null : this.getIsPrimary().equals(other.getIsPrimary()))
            && (this.getIsSend() == null ? other.getIsSend() == null : this.getIsSend().equals(other.getIsSend()))
            && (this.getIsExternal() == null ? other.getIsExternal() == null : this.getIsExternal().equals(other.getIsExternal()))
            && (this.getUserType() == null ? other.getUserType() == null : this.getUserType().equals(other.getUserType()))
            && (this.getCanEnable() == null ? other.getCanEnable() == null : this.getCanEnable().equals(other.getCanEnable()))
            && (this.getCjStatus() == null ? other.getCjStatus() == null : this.getCjStatus().equals(other.getCjStatus()))
            && (this.getCjApplyTime() == null ? other.getCjApplyTime() == null : this.getCjApplyTime().equals(other.getCjApplyTime()))
            && (this.getCjAdvice() == null ? other.getCjAdvice() == null : this.getCjAdvice().equals(other.getCjAdvice()))
            && (this.getUnifiedCode() == null ? other.getUnifiedCode() == null : this.getUnifiedCode().equals(other.getUnifiedCode()))
            && (this.getCompanyType() == null ? other.getCompanyType() == null : this.getCompanyType().equals(other.getCompanyType()))
            && (this.getCompanyId() == null ? other.getCompanyId() == null : this.getCompanyId().equals(other.getCompanyId()))
            && (this.getProvince() == null ? other.getProvince() == null : this.getProvince().equals(other.getProvince()))
            && (this.getBeId() == null ? other.getBeId() == null : this.getBeId().equals(other.getBeId()))
            && (this.getLocation() == null ? other.getLocation() == null : this.getLocation().equals(other.getLocation()))
            && (this.getLocationId() == null ? other.getLocationId() == null : this.getLocationId().equals(other.getLocationId()));
    }

    /**
     *
     * @mbg.generated Wed Jun 04 11:36:11 CST 2025
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getUserId() == null) ? 0 : getUserId().hashCode());
        result = prime * result + ((getName() == null) ? 0 : getName().hashCode());
        result = prime * result + ((getPwd() == null) ? 0 : getPwd().hashCode());
        result = prime * result + ((getPhone() == null) ? 0 : getPhone().hashCode());
        result = prime * result + ((getRemark() == null) ? 0 : getRemark().hashCode());
        result = prime * result + ((getRoleId() == null) ? 0 : getRoleId().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        result = prime * result + ((getUpdateTime() == null) ? 0 : getUpdateTime().hashCode());
        result = prime * result + ((getIsCancel() == null) ? 0 : getIsCancel().hashCode());
        result = prime * result + ((getIsLogoff() == null) ? 0 : getIsLogoff().hashCode());
        result = prime * result + ((getEmail() == null) ? 0 : getEmail().hashCode());
        result = prime * result + ((getPartnerName() == null) ? 0 : getPartnerName().hashCode());
        result = prime * result + ((getCreator() == null) ? 0 : getCreator().hashCode());
        result = prime * result + ((getIsPrimary() == null) ? 0 : getIsPrimary().hashCode());
        result = prime * result + ((getIsSend() == null) ? 0 : getIsSend().hashCode());
        result = prime * result + ((getIsExternal() == null) ? 0 : getIsExternal().hashCode());
        result = prime * result + ((getUserType() == null) ? 0 : getUserType().hashCode());
        result = prime * result + ((getCanEnable() == null) ? 0 : getCanEnable().hashCode());
        result = prime * result + ((getCjStatus() == null) ? 0 : getCjStatus().hashCode());
        result = prime * result + ((getCjApplyTime() == null) ? 0 : getCjApplyTime().hashCode());
        result = prime * result + ((getCjAdvice() == null) ? 0 : getCjAdvice().hashCode());
        result = prime * result + ((getUnifiedCode() == null) ? 0 : getUnifiedCode().hashCode());
        result = prime * result + ((getCompanyType() == null) ? 0 : getCompanyType().hashCode());
        result = prime * result + ((getCompanyId() == null) ? 0 : getCompanyId().hashCode());
        result = prime * result + ((getProvince() == null) ? 0 : getProvince().hashCode());
        result = prime * result + ((getBeId() == null) ? 0 : getBeId().hashCode());
        result = prime * result + ((getLocation() == null) ? 0 : getLocation().hashCode());
        result = prime * result + ((getLocationId() == null) ? 0 : getLocationId().hashCode());
        return result;
    }

    /**
     *
     * @mbg.generated Wed Jun 04 11:36:11 CST 2025
     */
    public enum Column {
        userId("user_id", "userId", "VARCHAR", false),
        name("name", "name", "VARCHAR", false),
        pwd("pwd", "pwd", "VARCHAR", false),
        phone("phone", "phone", "VARCHAR", false),
        remark("remark", "remark", "VARCHAR", false),
        roleId("role_id", "roleId", "VARCHAR", false),
        createTime("create_time", "createTime", "TIMESTAMP", false),
        updateTime("update_time", "updateTime", "TIMESTAMP", false),
        isCancel("is_cancel", "isCancel", "BIT", false),
        isLogoff("is_logoff", "isLogoff", "BIT", false),
        email("email", "email", "VARCHAR", false),
        partnerName("partner_name", "partnerName", "VARCHAR", false),
        creator("creator", "creator", "VARCHAR", false),
        isPrimary("is_primary", "isPrimary", "BIT", false),
        isSend("is_send", "isSend", "BIT", false),
        isExternal("is_external", "isExternal", "BIT", false),
        userType("user_type", "userType", "VARCHAR", false),
        canEnable("can_enable", "canEnable", "BIT", false),
        cjStatus("cj_status", "cjStatus", "VARCHAR", false),
        cjApplyTime("cj_apply_time", "cjApplyTime", "TIMESTAMP", false),
        cjAdvice("cj_advice", "cjAdvice", "VARCHAR", false),
        unifiedCode("unified_code", "unifiedCode", "VARCHAR", false),
        companyType("company_type", "companyType", "VARCHAR", false),
        companyId("company_id", "companyId", "VARCHAR", false),
        province("province", "province", "VARCHAR", false),
        beId("be_id", "beId", "VARCHAR", false),
        location("location", "location", "VARCHAR", false),
        locationId("location_id", "locationId", "VARCHAR", false);

        /**
         *
         * @mbg.generated Wed Jun 04 11:36:11 CST 2025
         */
        private static final String BEGINNING_DELIMITER = "\"";

        /**
         *
         * @mbg.generated Wed Jun 04 11:36:11 CST 2025
         */
        private static final String ENDING_DELIMITER = "\"";

        /**
         *
         * @mbg.generated Wed Jun 04 11:36:11 CST 2025
         */
        private final String column;

        /**
         *
         * @mbg.generated Wed Jun 04 11:36:11 CST 2025
         */
        private final boolean isColumnNameDelimited;

        /**
         *
         * @mbg.generated Wed Jun 04 11:36:11 CST 2025
         */
        private final String javaProperty;

        /**
         *
         * @mbg.generated Wed Jun 04 11:36:11 CST 2025
         */
        private final String jdbcType;

        /**
         *
         * @mbg.generated Wed Jun 04 11:36:11 CST 2025
         */
        public String value() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Wed Jun 04 11:36:11 CST 2025
         */
        public String getValue() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Wed Jun 04 11:36:11 CST 2025
         */
        public String getJavaProperty() {
            return this.javaProperty;
        }

        /**
         *
         * @mbg.generated Wed Jun 04 11:36:11 CST 2025
         */
        public String getJdbcType() {
            return this.jdbcType;
        }

        /**
         *
         * @mbg.generated Wed Jun 04 11:36:11 CST 2025
         */
        Column(String column, String javaProperty, String jdbcType, boolean isColumnNameDelimited) {
            this.column = column;
            this.javaProperty = javaProperty;
            this.jdbcType = jdbcType;
            this.isColumnNameDelimited = isColumnNameDelimited;
        }

        /**
         *
         * @mbg.generated Wed Jun 04 11:36:11 CST 2025
         */
        public String desc() {
            return this.getEscapedColumnName() + " DESC";
        }

        /**
         *
         * @mbg.generated Wed Jun 04 11:36:11 CST 2025
         */
        public String asc() {
            return this.getEscapedColumnName() + " ASC";
        }

        /**
         *
         * @mbg.generated Wed Jun 04 11:36:11 CST 2025
         */
        public static Column[] excludes(Column ... excludes) {
            ArrayList<Column> columns = new ArrayList<>(Arrays.asList(Column.values()));
            if (excludes != null && excludes.length > 0) {
                columns.removeAll(new ArrayList<>(Arrays.asList(excludes)));
            }
            return columns.toArray(new Column[]{});
        }

        /**
         *
         * @mbg.generated Wed Jun 04 11:36:11 CST 2025
         */
        public static Column[] all() {
            return Column.values();
        }

        /**
         *
         * @mbg.generated Wed Jun 04 11:36:11 CST 2025
         */
        public String getEscapedColumnName() {
            if (this.isColumnNameDelimited) {
                return new StringBuilder().append(BEGINNING_DELIMITER).append(this.column).append(ENDING_DELIMITER).toString();
            } else {
                return this.column;
            }
        }

        /**
         *
         * @mbg.generated Wed Jun 04 11:36:11 CST 2025
         */
        public String getAliasedEscapedColumnName() {
            return this.getEscapedColumnName();
        }
    }
}