package com.chinamobile.retail.pojo.dto;

import lombok.Data;

/**
 * @Author: YSC
 * @Date: 2021/11/9 16:05
 * @Description:
 */
@Data
public class Order2CInfoDTO {
    /**
     * 原子订单ID
     */
    private String id;
    /**
     * 下单时间
     */
    private String createTime;
    /**
     * 订单号
     */
    private String orderId;

    /**
     * 商品组/销售商品名称
     */
    private String spuOfferingName;

    /**
     * 商品组/销售商品编码
     */
    private String spuOfferingCode;

    /**
     * SPU一级销售目录
     */
    private String spuOfferingClass;
    /**
     * 商品名称(规格)
     */
    private String skuOfferingName;

    /**
     * 商品规格编码
     */
    private String skuOfferingCode;
    /**
     * 原子商品名称
     */
    private String atomOfferingName;
    /**
     * 原子商品类型
     */
    private String atomOfferingClass;
    /**
     * 原子商品编码
     */
    private String atomOfferingCode;
    /**
     * 型号
     */
    private String model;
    /**
     * 颜色
     */
    private String color;
    /**
     * 订购数量 skuQuantity*atomQuantity
     */
    private Integer quantity;
    /**
     * 单价
     */
    private Long atomPrice;
    /**
     * 单价元
     */
    private Double atomPriceYuan;
    /**
     * 订购数量(规格)
     */
    private Integer skuQuantity;
    /**
     * 价格(规格)
     */
    private Long skuPrice;
    /**
     * 合作伙伴ID
     */
    private String cooperatorId;
    /**
     * 合作伙伴名
     */
    private String partnerName;
    /**
     * 合作伙伴姓名
     */
    private String cooperatorName;

    /**
     * 订单状态
     */
    private Integer orderStatus;
    /**
     * 订单状态描述
     */
    private String orderStatusDescribe;

    /**
     * 是否允许特殊售后 0-不允许 1-允许
     */
    private Integer requestSpecialAfterStatus;

    /**
     * 订单类型  01-- 自主下单 00-- 代客下单
     */
    private String orderType;
    /**
     * 订单类型描述
     */
    private String orderTypeDescribe;
    /**
     * 判断是否是湖南类合同履约
     */
    private Boolean isHunan;

    /**
     * 是否是河南的卡+X订单
     */
    private Boolean isHenanKx;

    /**
     * 千里眼订单服务开通状态
     */
    private Integer qlyStatus;

    /**
     * 云视讯订单服务开通状态
     */
    private Integer ysxStatus;

    /**
     * 商品封面图url
     *
     */
    private String imgUrl;

    /**
     * 图片外部链接路径
     */
    private String url;

    /**
     * 行车卫士订购结果  0--开通失败  1--开通成功  2--退订失败 3--退订成功
     */
    private Integer carOpenStatus;

    /**
     * 平台软件编码
     */
    private String extSoftOfferingCode;

    /**
     * 是否是行车卫士订单
     */
    private Boolean isCarOrder;

    /**
     * 判断是不是省侧流程（目前只用于判断千里眼是否走省侧流程）
     */
    private Boolean isB2b;

    /**
     * 卡+X订单退款状态 0--申请退款 1--同意退款  2--不同意退款 3--取消退款
     */
    private Integer kxRefundStatus;

    /**
     * 接单状态 1--接单 2--拒单
     */
    private Integer allowOrderStatus;

    /**
     * 码号交付状态 0-未交付 1-已交付
     */
    private Integer deliverStatus;
    /**
     * 订单总金额
     */
    private Long totalPrice;
    /**
     * 订单总金额元
     */
    private Double totalPriceYuan;
    /**
     * 软件服务开通状态 0-开通成功，1-开通失败， 2-开通中， 3-退订成功， 4-退订失败， 5-退订中， 6-使用中
     */
    private Integer softServiceStatus;
    /**
     * 软件服务所有原子开通状态,目前业务只需要开通成功和开通失败  0-开通成功，1-开通失败
     */
    private Integer softServiceAllStatus;
    /**
     * 软件服务同步Iot状态  0--默认值，未同步；1--开通成功同步iot商城失败;2使用中同步iot商城失败
     */
    private Integer syncIotFailStatus;
    /**
     * 订单金额(元)
     */
    private Double atomOrderPrice;
}
