package com.chinamobile.retail.pojo.entity;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class MiniProgramSaleYearReportExample {
    /**
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    protected String orderByClause;

    /**
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    protected boolean distinct;

    /**
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    /**
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    public MiniProgramSaleYearReportExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    /**
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    public MiniProgramSaleYearReportExample orderBy(String orderByClause) {
        this.setOrderByClause(orderByClause);
        return this;
    }

    /**
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    public MiniProgramSaleYearReportExample orderBy(String ... orderByClauses) {
        StringBuffer sb = new StringBuffer();
        for (int i = 0; i < orderByClauses.length; i++) {
            sb.append(orderByClauses[i]);
            if (i < orderByClauses.length - 1) {
                sb.append(" , ");
            }
        }
        this.setOrderByClause(sb.toString());
        return this;
    }

    /**
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria(this);
        return criteria;
    }

    /**
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    public void setPageInfo(Integer currentPage, Integer pageSize) {
        if(pageSize<1) throw new IllegalArgumentException("页大小不能小于1！");
        this.limit=pageSize;
        if(currentPage<1) throw new IllegalArgumentException("页数不能小于1！");
        this.offset=(currentPage-1)*pageSize;
    }

    /**
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    public static Criteria newAndCreateCriteria() {
        MiniProgramSaleYearReportExample example = new MiniProgramSaleYearReportExample();
        return example.createCriteria();
    }

    /**
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    public MiniProgramSaleYearReportExample when(boolean condition, IExampleWhen then) {
        if (condition) {
            then.example(this);
        }
        return this;
    }

    /**
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    public MiniProgramSaleYearReportExample when(boolean condition, IExampleWhen then, IExampleWhen otherwise) {
        if (condition) {
            then.example(this);
        } else {
            otherwise.example(this);
        }
        return this;
    }

    /**
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    public MiniProgramSaleYearReportExample distinct(boolean distinct) {
        this.setDistinct(distinct);
        return this;
    }

    /**
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(String value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdEqualToColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(String value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualToColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(String value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(String value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualToColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdLessThan(String value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(String value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualToColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdLike(String value) {
            addCriterion("id like", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotLike(String value) {
            addCriterion("id not like", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<String> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<String> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(String value1, String value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(String value1, String value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andUserIdIsNull() {
            addCriterion("user_id is null");
            return (Criteria) this;
        }

        public Criteria andUserIdIsNotNull() {
            addCriterion("user_id is not null");
            return (Criteria) this;
        }

        public Criteria andUserIdEqualTo(String value) {
            addCriterion("user_id =", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdEqualToColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("user_id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUserIdNotEqualTo(String value) {
            addCriterion("user_id <>", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdNotEqualToColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("user_id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUserIdGreaterThan(String value) {
            addCriterion("user_id >", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdGreaterThanColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("user_id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUserIdGreaterThanOrEqualTo(String value) {
            addCriterion("user_id >=", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdGreaterThanOrEqualToColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("user_id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUserIdLessThan(String value) {
            addCriterion("user_id <", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdLessThanColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("user_id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUserIdLessThanOrEqualTo(String value) {
            addCriterion("user_id <=", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdLessThanOrEqualToColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("user_id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUserIdLike(String value) {
            addCriterion("user_id like", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdNotLike(String value) {
            addCriterion("user_id not like", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdIn(List<String> values) {
            addCriterion("user_id in", values, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdNotIn(List<String> values) {
            addCriterion("user_id not in", values, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdBetween(String value1, String value2) {
            addCriterion("user_id between", value1, value2, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdNotBetween(String value1, String value2) {
            addCriterion("user_id not between", value1, value2, "userId");
            return (Criteria) this;
        }

        public Criteria andNameIsNull() {
            addCriterion("`name` is null");
            return (Criteria) this;
        }

        public Criteria andNameIsNotNull() {
            addCriterion("`name` is not null");
            return (Criteria) this;
        }

        public Criteria andNameEqualTo(String value) {
            addCriterion("`name` =", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameEqualToColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("`name` = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andNameNotEqualTo(String value) {
            addCriterion("`name` <>", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotEqualToColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("`name` <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andNameGreaterThan(String value) {
            addCriterion("`name` >", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameGreaterThanColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("`name` > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andNameGreaterThanOrEqualTo(String value) {
            addCriterion("`name` >=", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameGreaterThanOrEqualToColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("`name` >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andNameLessThan(String value) {
            addCriterion("`name` <", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLessThanColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("`name` < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andNameLessThanOrEqualTo(String value) {
            addCriterion("`name` <=", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLessThanOrEqualToColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("`name` <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andNameLike(String value) {
            addCriterion("`name` like", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotLike(String value) {
            addCriterion("`name` not like", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameIn(List<String> values) {
            addCriterion("`name` in", values, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotIn(List<String> values) {
            addCriterion("`name` not in", values, "name");
            return (Criteria) this;
        }

        public Criteria andNameBetween(String value1, String value2) {
            addCriterion("`name` between", value1, value2, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotBetween(String value1, String value2) {
            addCriterion("`name` not between", value1, value2, "name");
            return (Criteria) this;
        }

        public Criteria andYearIsNull() {
            addCriterion("`year` is null");
            return (Criteria) this;
        }

        public Criteria andYearIsNotNull() {
            addCriterion("`year` is not null");
            return (Criteria) this;
        }

        public Criteria andYearEqualTo(Integer value) {
            addCriterion("`year` =", value, "year");
            return (Criteria) this;
        }

        public Criteria andYearEqualToColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("`year` = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andYearNotEqualTo(Integer value) {
            addCriterion("`year` <>", value, "year");
            return (Criteria) this;
        }

        public Criteria andYearNotEqualToColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("`year` <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andYearGreaterThan(Integer value) {
            addCriterion("`year` >", value, "year");
            return (Criteria) this;
        }

        public Criteria andYearGreaterThanColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("`year` > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andYearGreaterThanOrEqualTo(Integer value) {
            addCriterion("`year` >=", value, "year");
            return (Criteria) this;
        }

        public Criteria andYearGreaterThanOrEqualToColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("`year` >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andYearLessThan(Integer value) {
            addCriterion("`year` <", value, "year");
            return (Criteria) this;
        }

        public Criteria andYearLessThanColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("`year` < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andYearLessThanOrEqualTo(Integer value) {
            addCriterion("`year` <=", value, "year");
            return (Criteria) this;
        }

        public Criteria andYearLessThanOrEqualToColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("`year` <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andYearIn(List<Integer> values) {
            addCriterion("`year` in", values, "year");
            return (Criteria) this;
        }

        public Criteria andYearNotIn(List<Integer> values) {
            addCriterion("`year` not in", values, "year");
            return (Criteria) this;
        }

        public Criteria andYearBetween(Integer value1, Integer value2) {
            addCriterion("`year` between", value1, value2, "year");
            return (Criteria) this;
        }

        public Criteria andYearNotBetween(Integer value1, Integer value2) {
            addCriterion("`year` not between", value1, value2, "year");
            return (Criteria) this;
        }

        public Criteria andRegisterIndexIsNull() {
            addCriterion("register_index is null");
            return (Criteria) this;
        }

        public Criteria andRegisterIndexIsNotNull() {
            addCriterion("register_index is not null");
            return (Criteria) this;
        }

        public Criteria andRegisterIndexEqualTo(Long value) {
            addCriterion("register_index =", value, "registerIndex");
            return (Criteria) this;
        }

        public Criteria andRegisterIndexEqualToColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("register_index = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRegisterIndexNotEqualTo(Long value) {
            addCriterion("register_index <>", value, "registerIndex");
            return (Criteria) this;
        }

        public Criteria andRegisterIndexNotEqualToColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("register_index <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRegisterIndexGreaterThan(Long value) {
            addCriterion("register_index >", value, "registerIndex");
            return (Criteria) this;
        }

        public Criteria andRegisterIndexGreaterThanColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("register_index > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRegisterIndexGreaterThanOrEqualTo(Long value) {
            addCriterion("register_index >=", value, "registerIndex");
            return (Criteria) this;
        }

        public Criteria andRegisterIndexGreaterThanOrEqualToColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("register_index >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRegisterIndexLessThan(Long value) {
            addCriterion("register_index <", value, "registerIndex");
            return (Criteria) this;
        }

        public Criteria andRegisterIndexLessThanColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("register_index < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRegisterIndexLessThanOrEqualTo(Long value) {
            addCriterion("register_index <=", value, "registerIndex");
            return (Criteria) this;
        }

        public Criteria andRegisterIndexLessThanOrEqualToColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("register_index <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRegisterIndexIn(List<Long> values) {
            addCriterion("register_index in", values, "registerIndex");
            return (Criteria) this;
        }

        public Criteria andRegisterIndexNotIn(List<Long> values) {
            addCriterion("register_index not in", values, "registerIndex");
            return (Criteria) this;
        }

        public Criteria andRegisterIndexBetween(Long value1, Long value2) {
            addCriterion("register_index between", value1, value2, "registerIndex");
            return (Criteria) this;
        }

        public Criteria andRegisterIndexNotBetween(Long value1, Long value2) {
            addCriterion("register_index not between", value1, value2, "registerIndex");
            return (Criteria) this;
        }

        public Criteria andRegisterTimeIsNull() {
            addCriterion("register_time is null");
            return (Criteria) this;
        }

        public Criteria andRegisterTimeIsNotNull() {
            addCriterion("register_time is not null");
            return (Criteria) this;
        }

        public Criteria andRegisterTimeEqualTo(Date value) {
            addCriterion("register_time =", value, "registerTime");
            return (Criteria) this;
        }

        public Criteria andRegisterTimeEqualToColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("register_time = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRegisterTimeNotEqualTo(Date value) {
            addCriterion("register_time <>", value, "registerTime");
            return (Criteria) this;
        }

        public Criteria andRegisterTimeNotEqualToColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("register_time <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRegisterTimeGreaterThan(Date value) {
            addCriterion("register_time >", value, "registerTime");
            return (Criteria) this;
        }

        public Criteria andRegisterTimeGreaterThanColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("register_time > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRegisterTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("register_time >=", value, "registerTime");
            return (Criteria) this;
        }

        public Criteria andRegisterTimeGreaterThanOrEqualToColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("register_time >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRegisterTimeLessThan(Date value) {
            addCriterion("register_time <", value, "registerTime");
            return (Criteria) this;
        }

        public Criteria andRegisterTimeLessThanColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("register_time < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRegisterTimeLessThanOrEqualTo(Date value) {
            addCriterion("register_time <=", value, "registerTime");
            return (Criteria) this;
        }

        public Criteria andRegisterTimeLessThanOrEqualToColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("register_time <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRegisterTimeIn(List<Date> values) {
            addCriterion("register_time in", values, "registerTime");
            return (Criteria) this;
        }

        public Criteria andRegisterTimeNotIn(List<Date> values) {
            addCriterion("register_time not in", values, "registerTime");
            return (Criteria) this;
        }

        public Criteria andRegisterTimeBetween(Date value1, Date value2) {
            addCriterion("register_time between", value1, value2, "registerTime");
            return (Criteria) this;
        }

        public Criteria andRegisterTimeNotBetween(Date value1, Date value2) {
            addCriterion("register_time not between", value1, value2, "registerTime");
            return (Criteria) this;
        }

        public Criteria andRoleTypeIsNull() {
            addCriterion("role_type is null");
            return (Criteria) this;
        }

        public Criteria andRoleTypeIsNotNull() {
            addCriterion("role_type is not null");
            return (Criteria) this;
        }

        public Criteria andRoleTypeEqualTo(String value) {
            addCriterion("role_type =", value, "roleType");
            return (Criteria) this;
        }

        public Criteria andRoleTypeEqualToColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("role_type = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRoleTypeNotEqualTo(String value) {
            addCriterion("role_type <>", value, "roleType");
            return (Criteria) this;
        }

        public Criteria andRoleTypeNotEqualToColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("role_type <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRoleTypeGreaterThan(String value) {
            addCriterion("role_type >", value, "roleType");
            return (Criteria) this;
        }

        public Criteria andRoleTypeGreaterThanColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("role_type > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRoleTypeGreaterThanOrEqualTo(String value) {
            addCriterion("role_type >=", value, "roleType");
            return (Criteria) this;
        }

        public Criteria andRoleTypeGreaterThanOrEqualToColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("role_type >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRoleTypeLessThan(String value) {
            addCriterion("role_type <", value, "roleType");
            return (Criteria) this;
        }

        public Criteria andRoleTypeLessThanColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("role_type < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRoleTypeLessThanOrEqualTo(String value) {
            addCriterion("role_type <=", value, "roleType");
            return (Criteria) this;
        }

        public Criteria andRoleTypeLessThanOrEqualToColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("role_type <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRoleTypeLike(String value) {
            addCriterion("role_type like", value, "roleType");
            return (Criteria) this;
        }

        public Criteria andRoleTypeNotLike(String value) {
            addCriterion("role_type not like", value, "roleType");
            return (Criteria) this;
        }

        public Criteria andRoleTypeIn(List<String> values) {
            addCriterion("role_type in", values, "roleType");
            return (Criteria) this;
        }

        public Criteria andRoleTypeNotIn(List<String> values) {
            addCriterion("role_type not in", values, "roleType");
            return (Criteria) this;
        }

        public Criteria andRoleTypeBetween(String value1, String value2) {
            addCriterion("role_type between", value1, value2, "roleType");
            return (Criteria) this;
        }

        public Criteria andRoleTypeNotBetween(String value1, String value2) {
            addCriterion("role_type not between", value1, value2, "roleType");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeIsNull() {
            addCriterion("province_code is null");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeIsNotNull() {
            addCriterion("province_code is not null");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeEqualTo(String value) {
            addCriterion("province_code =", value, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeEqualToColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("province_code = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProvinceCodeNotEqualTo(String value) {
            addCriterion("province_code <>", value, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeNotEqualToColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("province_code <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProvinceCodeGreaterThan(String value) {
            addCriterion("province_code >", value, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeGreaterThanColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("province_code > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProvinceCodeGreaterThanOrEqualTo(String value) {
            addCriterion("province_code >=", value, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeGreaterThanOrEqualToColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("province_code >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProvinceCodeLessThan(String value) {
            addCriterion("province_code <", value, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeLessThanColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("province_code < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProvinceCodeLessThanOrEqualTo(String value) {
            addCriterion("province_code <=", value, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeLessThanOrEqualToColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("province_code <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProvinceCodeLike(String value) {
            addCriterion("province_code like", value, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeNotLike(String value) {
            addCriterion("province_code not like", value, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeIn(List<String> values) {
            addCriterion("province_code in", values, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeNotIn(List<String> values) {
            addCriterion("province_code not in", values, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeBetween(String value1, String value2) {
            addCriterion("province_code between", value1, value2, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeNotBetween(String value1, String value2) {
            addCriterion("province_code not between", value1, value2, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceNameIsNull() {
            addCriterion("province_name is null");
            return (Criteria) this;
        }

        public Criteria andProvinceNameIsNotNull() {
            addCriterion("province_name is not null");
            return (Criteria) this;
        }

        public Criteria andProvinceNameEqualTo(String value) {
            addCriterion("province_name =", value, "provinceName");
            return (Criteria) this;
        }

        public Criteria andProvinceNameEqualToColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("province_name = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProvinceNameNotEqualTo(String value) {
            addCriterion("province_name <>", value, "provinceName");
            return (Criteria) this;
        }

        public Criteria andProvinceNameNotEqualToColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("province_name <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProvinceNameGreaterThan(String value) {
            addCriterion("province_name >", value, "provinceName");
            return (Criteria) this;
        }

        public Criteria andProvinceNameGreaterThanColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("province_name > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProvinceNameGreaterThanOrEqualTo(String value) {
            addCriterion("province_name >=", value, "provinceName");
            return (Criteria) this;
        }

        public Criteria andProvinceNameGreaterThanOrEqualToColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("province_name >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProvinceNameLessThan(String value) {
            addCriterion("province_name <", value, "provinceName");
            return (Criteria) this;
        }

        public Criteria andProvinceNameLessThanColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("province_name < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProvinceNameLessThanOrEqualTo(String value) {
            addCriterion("province_name <=", value, "provinceName");
            return (Criteria) this;
        }

        public Criteria andProvinceNameLessThanOrEqualToColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("province_name <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProvinceNameLike(String value) {
            addCriterion("province_name like", value, "provinceName");
            return (Criteria) this;
        }

        public Criteria andProvinceNameNotLike(String value) {
            addCriterion("province_name not like", value, "provinceName");
            return (Criteria) this;
        }

        public Criteria andProvinceNameIn(List<String> values) {
            addCriterion("province_name in", values, "provinceName");
            return (Criteria) this;
        }

        public Criteria andProvinceNameNotIn(List<String> values) {
            addCriterion("province_name not in", values, "provinceName");
            return (Criteria) this;
        }

        public Criteria andProvinceNameBetween(String value1, String value2) {
            addCriterion("province_name between", value1, value2, "provinceName");
            return (Criteria) this;
        }

        public Criteria andProvinceNameNotBetween(String value1, String value2) {
            addCriterion("province_name not between", value1, value2, "provinceName");
            return (Criteria) this;
        }

        public Criteria andCityCodeIsNull() {
            addCriterion("city_code is null");
            return (Criteria) this;
        }

        public Criteria andCityCodeIsNotNull() {
            addCriterion("city_code is not null");
            return (Criteria) this;
        }

        public Criteria andCityCodeEqualTo(String value) {
            addCriterion("city_code =", value, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeEqualToColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("city_code = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCityCodeNotEqualTo(String value) {
            addCriterion("city_code <>", value, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeNotEqualToColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("city_code <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCityCodeGreaterThan(String value) {
            addCriterion("city_code >", value, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeGreaterThanColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("city_code > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCityCodeGreaterThanOrEqualTo(String value) {
            addCriterion("city_code >=", value, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeGreaterThanOrEqualToColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("city_code >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCityCodeLessThan(String value) {
            addCriterion("city_code <", value, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeLessThanColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("city_code < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCityCodeLessThanOrEqualTo(String value) {
            addCriterion("city_code <=", value, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeLessThanOrEqualToColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("city_code <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCityCodeLike(String value) {
            addCriterion("city_code like", value, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeNotLike(String value) {
            addCriterion("city_code not like", value, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeIn(List<String> values) {
            addCriterion("city_code in", values, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeNotIn(List<String> values) {
            addCriterion("city_code not in", values, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeBetween(String value1, String value2) {
            addCriterion("city_code between", value1, value2, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeNotBetween(String value1, String value2) {
            addCriterion("city_code not between", value1, value2, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityNameIsNull() {
            addCriterion("city_name is null");
            return (Criteria) this;
        }

        public Criteria andCityNameIsNotNull() {
            addCriterion("city_name is not null");
            return (Criteria) this;
        }

        public Criteria andCityNameEqualTo(String value) {
            addCriterion("city_name =", value, "cityName");
            return (Criteria) this;
        }

        public Criteria andCityNameEqualToColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("city_name = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCityNameNotEqualTo(String value) {
            addCriterion("city_name <>", value, "cityName");
            return (Criteria) this;
        }

        public Criteria andCityNameNotEqualToColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("city_name <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCityNameGreaterThan(String value) {
            addCriterion("city_name >", value, "cityName");
            return (Criteria) this;
        }

        public Criteria andCityNameGreaterThanColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("city_name > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCityNameGreaterThanOrEqualTo(String value) {
            addCriterion("city_name >=", value, "cityName");
            return (Criteria) this;
        }

        public Criteria andCityNameGreaterThanOrEqualToColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("city_name >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCityNameLessThan(String value) {
            addCriterion("city_name <", value, "cityName");
            return (Criteria) this;
        }

        public Criteria andCityNameLessThanColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("city_name < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCityNameLessThanOrEqualTo(String value) {
            addCriterion("city_name <=", value, "cityName");
            return (Criteria) this;
        }

        public Criteria andCityNameLessThanOrEqualToColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("city_name <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCityNameLike(String value) {
            addCriterion("city_name like", value, "cityName");
            return (Criteria) this;
        }

        public Criteria andCityNameNotLike(String value) {
            addCriterion("city_name not like", value, "cityName");
            return (Criteria) this;
        }

        public Criteria andCityNameIn(List<String> values) {
            addCriterion("city_name in", values, "cityName");
            return (Criteria) this;
        }

        public Criteria andCityNameNotIn(List<String> values) {
            addCriterion("city_name not in", values, "cityName");
            return (Criteria) this;
        }

        public Criteria andCityNameBetween(String value1, String value2) {
            addCriterion("city_name between", value1, value2, "cityName");
            return (Criteria) this;
        }

        public Criteria andCityNameNotBetween(String value1, String value2) {
            addCriterion("city_name not between", value1, value2, "cityName");
            return (Criteria) this;
        }

        public Criteria andFirstOrderTimeIsNull() {
            addCriterion("first_order_time is null");
            return (Criteria) this;
        }

        public Criteria andFirstOrderTimeIsNotNull() {
            addCriterion("first_order_time is not null");
            return (Criteria) this;
        }

        public Criteria andFirstOrderTimeEqualTo(Date value) {
            addCriterion("first_order_time =", value, "firstOrderTime");
            return (Criteria) this;
        }

        public Criteria andFirstOrderTimeEqualToColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("first_order_time = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andFirstOrderTimeNotEqualTo(Date value) {
            addCriterion("first_order_time <>", value, "firstOrderTime");
            return (Criteria) this;
        }

        public Criteria andFirstOrderTimeNotEqualToColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("first_order_time <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andFirstOrderTimeGreaterThan(Date value) {
            addCriterion("first_order_time >", value, "firstOrderTime");
            return (Criteria) this;
        }

        public Criteria andFirstOrderTimeGreaterThanColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("first_order_time > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andFirstOrderTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("first_order_time >=", value, "firstOrderTime");
            return (Criteria) this;
        }

        public Criteria andFirstOrderTimeGreaterThanOrEqualToColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("first_order_time >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andFirstOrderTimeLessThan(Date value) {
            addCriterion("first_order_time <", value, "firstOrderTime");
            return (Criteria) this;
        }

        public Criteria andFirstOrderTimeLessThanColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("first_order_time < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andFirstOrderTimeLessThanOrEqualTo(Date value) {
            addCriterion("first_order_time <=", value, "firstOrderTime");
            return (Criteria) this;
        }

        public Criteria andFirstOrderTimeLessThanOrEqualToColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("first_order_time <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andFirstOrderTimeIn(List<Date> values) {
            addCriterion("first_order_time in", values, "firstOrderTime");
            return (Criteria) this;
        }

        public Criteria andFirstOrderTimeNotIn(List<Date> values) {
            addCriterion("first_order_time not in", values, "firstOrderTime");
            return (Criteria) this;
        }

        public Criteria andFirstOrderTimeBetween(Date value1, Date value2) {
            addCriterion("first_order_time between", value1, value2, "firstOrderTime");
            return (Criteria) this;
        }

        public Criteria andFirstOrderTimeNotBetween(Date value1, Date value2) {
            addCriterion("first_order_time not between", value1, value2, "firstOrderTime");
            return (Criteria) this;
        }

        public Criteria andYearLastOrderTimeIsNull() {
            addCriterion("year_last_order_time is null");
            return (Criteria) this;
        }

        public Criteria andYearLastOrderTimeIsNotNull() {
            addCriterion("year_last_order_time is not null");
            return (Criteria) this;
        }

        public Criteria andYearLastOrderTimeEqualTo(Date value) {
            addCriterion("year_last_order_time =", value, "yearLastOrderTime");
            return (Criteria) this;
        }

        public Criteria andYearLastOrderTimeEqualToColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("year_last_order_time = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andYearLastOrderTimeNotEqualTo(Date value) {
            addCriterion("year_last_order_time <>", value, "yearLastOrderTime");
            return (Criteria) this;
        }

        public Criteria andYearLastOrderTimeNotEqualToColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("year_last_order_time <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andYearLastOrderTimeGreaterThan(Date value) {
            addCriterion("year_last_order_time >", value, "yearLastOrderTime");
            return (Criteria) this;
        }

        public Criteria andYearLastOrderTimeGreaterThanColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("year_last_order_time > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andYearLastOrderTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("year_last_order_time >=", value, "yearLastOrderTime");
            return (Criteria) this;
        }

        public Criteria andYearLastOrderTimeGreaterThanOrEqualToColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("year_last_order_time >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andYearLastOrderTimeLessThan(Date value) {
            addCriterion("year_last_order_time <", value, "yearLastOrderTime");
            return (Criteria) this;
        }

        public Criteria andYearLastOrderTimeLessThanColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("year_last_order_time < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andYearLastOrderTimeLessThanOrEqualTo(Date value) {
            addCriterion("year_last_order_time <=", value, "yearLastOrderTime");
            return (Criteria) this;
        }

        public Criteria andYearLastOrderTimeLessThanOrEqualToColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("year_last_order_time <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andYearLastOrderTimeIn(List<Date> values) {
            addCriterion("year_last_order_time in", values, "yearLastOrderTime");
            return (Criteria) this;
        }

        public Criteria andYearLastOrderTimeNotIn(List<Date> values) {
            addCriterion("year_last_order_time not in", values, "yearLastOrderTime");
            return (Criteria) this;
        }

        public Criteria andYearLastOrderTimeBetween(Date value1, Date value2) {
            addCriterion("year_last_order_time between", value1, value2, "yearLastOrderTime");
            return (Criteria) this;
        }

        public Criteria andYearLastOrderTimeNotBetween(Date value1, Date value2) {
            addCriterion("year_last_order_time not between", value1, value2, "yearLastOrderTime");
            return (Criteria) this;
        }

        public Criteria andFirstOrderSpuCodeIsNull() {
            addCriterion("first_order_spu_code is null");
            return (Criteria) this;
        }

        public Criteria andFirstOrderSpuCodeIsNotNull() {
            addCriterion("first_order_spu_code is not null");
            return (Criteria) this;
        }

        public Criteria andFirstOrderSpuCodeEqualTo(String value) {
            addCriterion("first_order_spu_code =", value, "firstOrderSpuCode");
            return (Criteria) this;
        }

        public Criteria andFirstOrderSpuCodeEqualToColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("first_order_spu_code = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andFirstOrderSpuCodeNotEqualTo(String value) {
            addCriterion("first_order_spu_code <>", value, "firstOrderSpuCode");
            return (Criteria) this;
        }

        public Criteria andFirstOrderSpuCodeNotEqualToColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("first_order_spu_code <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andFirstOrderSpuCodeGreaterThan(String value) {
            addCriterion("first_order_spu_code >", value, "firstOrderSpuCode");
            return (Criteria) this;
        }

        public Criteria andFirstOrderSpuCodeGreaterThanColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("first_order_spu_code > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andFirstOrderSpuCodeGreaterThanOrEqualTo(String value) {
            addCriterion("first_order_spu_code >=", value, "firstOrderSpuCode");
            return (Criteria) this;
        }

        public Criteria andFirstOrderSpuCodeGreaterThanOrEqualToColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("first_order_spu_code >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andFirstOrderSpuCodeLessThan(String value) {
            addCriterion("first_order_spu_code <", value, "firstOrderSpuCode");
            return (Criteria) this;
        }

        public Criteria andFirstOrderSpuCodeLessThanColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("first_order_spu_code < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andFirstOrderSpuCodeLessThanOrEqualTo(String value) {
            addCriterion("first_order_spu_code <=", value, "firstOrderSpuCode");
            return (Criteria) this;
        }

        public Criteria andFirstOrderSpuCodeLessThanOrEqualToColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("first_order_spu_code <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andFirstOrderSpuCodeLike(String value) {
            addCriterion("first_order_spu_code like", value, "firstOrderSpuCode");
            return (Criteria) this;
        }

        public Criteria andFirstOrderSpuCodeNotLike(String value) {
            addCriterion("first_order_spu_code not like", value, "firstOrderSpuCode");
            return (Criteria) this;
        }

        public Criteria andFirstOrderSpuCodeIn(List<String> values) {
            addCriterion("first_order_spu_code in", values, "firstOrderSpuCode");
            return (Criteria) this;
        }

        public Criteria andFirstOrderSpuCodeNotIn(List<String> values) {
            addCriterion("first_order_spu_code not in", values, "firstOrderSpuCode");
            return (Criteria) this;
        }

        public Criteria andFirstOrderSpuCodeBetween(String value1, String value2) {
            addCriterion("first_order_spu_code between", value1, value2, "firstOrderSpuCode");
            return (Criteria) this;
        }

        public Criteria andFirstOrderSpuCodeNotBetween(String value1, String value2) {
            addCriterion("first_order_spu_code not between", value1, value2, "firstOrderSpuCode");
            return (Criteria) this;
        }

        public Criteria andFirstOrderSpuNameIsNull() {
            addCriterion("first_order_spu_name is null");
            return (Criteria) this;
        }

        public Criteria andFirstOrderSpuNameIsNotNull() {
            addCriterion("first_order_spu_name is not null");
            return (Criteria) this;
        }

        public Criteria andFirstOrderSpuNameEqualTo(String value) {
            addCriterion("first_order_spu_name =", value, "firstOrderSpuName");
            return (Criteria) this;
        }

        public Criteria andFirstOrderSpuNameEqualToColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("first_order_spu_name = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andFirstOrderSpuNameNotEqualTo(String value) {
            addCriterion("first_order_spu_name <>", value, "firstOrderSpuName");
            return (Criteria) this;
        }

        public Criteria andFirstOrderSpuNameNotEqualToColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("first_order_spu_name <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andFirstOrderSpuNameGreaterThan(String value) {
            addCriterion("first_order_spu_name >", value, "firstOrderSpuName");
            return (Criteria) this;
        }

        public Criteria andFirstOrderSpuNameGreaterThanColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("first_order_spu_name > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andFirstOrderSpuNameGreaterThanOrEqualTo(String value) {
            addCriterion("first_order_spu_name >=", value, "firstOrderSpuName");
            return (Criteria) this;
        }

        public Criteria andFirstOrderSpuNameGreaterThanOrEqualToColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("first_order_spu_name >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andFirstOrderSpuNameLessThan(String value) {
            addCriterion("first_order_spu_name <", value, "firstOrderSpuName");
            return (Criteria) this;
        }

        public Criteria andFirstOrderSpuNameLessThanColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("first_order_spu_name < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andFirstOrderSpuNameLessThanOrEqualTo(String value) {
            addCriterion("first_order_spu_name <=", value, "firstOrderSpuName");
            return (Criteria) this;
        }

        public Criteria andFirstOrderSpuNameLessThanOrEqualToColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("first_order_spu_name <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andFirstOrderSpuNameLike(String value) {
            addCriterion("first_order_spu_name like", value, "firstOrderSpuName");
            return (Criteria) this;
        }

        public Criteria andFirstOrderSpuNameNotLike(String value) {
            addCriterion("first_order_spu_name not like", value, "firstOrderSpuName");
            return (Criteria) this;
        }

        public Criteria andFirstOrderSpuNameIn(List<String> values) {
            addCriterion("first_order_spu_name in", values, "firstOrderSpuName");
            return (Criteria) this;
        }

        public Criteria andFirstOrderSpuNameNotIn(List<String> values) {
            addCriterion("first_order_spu_name not in", values, "firstOrderSpuName");
            return (Criteria) this;
        }

        public Criteria andFirstOrderSpuNameBetween(String value1, String value2) {
            addCriterion("first_order_spu_name between", value1, value2, "firstOrderSpuName");
            return (Criteria) this;
        }

        public Criteria andFirstOrderSpuNameNotBetween(String value1, String value2) {
            addCriterion("first_order_spu_name not between", value1, value2, "firstOrderSpuName");
            return (Criteria) this;
        }

        public Criteria andOrderTotalPriceIsNull() {
            addCriterion("order_total_price is null");
            return (Criteria) this;
        }

        public Criteria andOrderTotalPriceIsNotNull() {
            addCriterion("order_total_price is not null");
            return (Criteria) this;
        }

        public Criteria andOrderTotalPriceEqualTo(Long value) {
            addCriterion("order_total_price =", value, "orderTotalPrice");
            return (Criteria) this;
        }

        public Criteria andOrderTotalPriceEqualToColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("order_total_price = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderTotalPriceNotEqualTo(Long value) {
            addCriterion("order_total_price <>", value, "orderTotalPrice");
            return (Criteria) this;
        }

        public Criteria andOrderTotalPriceNotEqualToColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("order_total_price <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderTotalPriceGreaterThan(Long value) {
            addCriterion("order_total_price >", value, "orderTotalPrice");
            return (Criteria) this;
        }

        public Criteria andOrderTotalPriceGreaterThanColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("order_total_price > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderTotalPriceGreaterThanOrEqualTo(Long value) {
            addCriterion("order_total_price >=", value, "orderTotalPrice");
            return (Criteria) this;
        }

        public Criteria andOrderTotalPriceGreaterThanOrEqualToColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("order_total_price >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderTotalPriceLessThan(Long value) {
            addCriterion("order_total_price <", value, "orderTotalPrice");
            return (Criteria) this;
        }

        public Criteria andOrderTotalPriceLessThanColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("order_total_price < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderTotalPriceLessThanOrEqualTo(Long value) {
            addCriterion("order_total_price <=", value, "orderTotalPrice");
            return (Criteria) this;
        }

        public Criteria andOrderTotalPriceLessThanOrEqualToColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("order_total_price <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderTotalPriceIn(List<Long> values) {
            addCriterion("order_total_price in", values, "orderTotalPrice");
            return (Criteria) this;
        }

        public Criteria andOrderTotalPriceNotIn(List<Long> values) {
            addCriterion("order_total_price not in", values, "orderTotalPrice");
            return (Criteria) this;
        }

        public Criteria andOrderTotalPriceBetween(Long value1, Long value2) {
            addCriterion("order_total_price between", value1, value2, "orderTotalPrice");
            return (Criteria) this;
        }

        public Criteria andOrderTotalPriceNotBetween(Long value1, Long value2) {
            addCriterion("order_total_price not between", value1, value2, "orderTotalPrice");
            return (Criteria) this;
        }

        public Criteria andOrderTotalCountIsNull() {
            addCriterion("order_total_count is null");
            return (Criteria) this;
        }

        public Criteria andOrderTotalCountIsNotNull() {
            addCriterion("order_total_count is not null");
            return (Criteria) this;
        }

        public Criteria andOrderTotalCountEqualTo(Long value) {
            addCriterion("order_total_count =", value, "orderTotalCount");
            return (Criteria) this;
        }

        public Criteria andOrderTotalCountEqualToColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("order_total_count = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderTotalCountNotEqualTo(Long value) {
            addCriterion("order_total_count <>", value, "orderTotalCount");
            return (Criteria) this;
        }

        public Criteria andOrderTotalCountNotEqualToColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("order_total_count <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderTotalCountGreaterThan(Long value) {
            addCriterion("order_total_count >", value, "orderTotalCount");
            return (Criteria) this;
        }

        public Criteria andOrderTotalCountGreaterThanColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("order_total_count > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderTotalCountGreaterThanOrEqualTo(Long value) {
            addCriterion("order_total_count >=", value, "orderTotalCount");
            return (Criteria) this;
        }

        public Criteria andOrderTotalCountGreaterThanOrEqualToColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("order_total_count >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderTotalCountLessThan(Long value) {
            addCriterion("order_total_count <", value, "orderTotalCount");
            return (Criteria) this;
        }

        public Criteria andOrderTotalCountLessThanColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("order_total_count < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderTotalCountLessThanOrEqualTo(Long value) {
            addCriterion("order_total_count <=", value, "orderTotalCount");
            return (Criteria) this;
        }

        public Criteria andOrderTotalCountLessThanOrEqualToColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("order_total_count <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderTotalCountIn(List<Long> values) {
            addCriterion("order_total_count in", values, "orderTotalCount");
            return (Criteria) this;
        }

        public Criteria andOrderTotalCountNotIn(List<Long> values) {
            addCriterion("order_total_count not in", values, "orderTotalCount");
            return (Criteria) this;
        }

        public Criteria andOrderTotalCountBetween(Long value1, Long value2) {
            addCriterion("order_total_count between", value1, value2, "orderTotalCount");
            return (Criteria) this;
        }

        public Criteria andOrderTotalCountNotBetween(Long value1, Long value2) {
            addCriterion("order_total_count not between", value1, value2, "orderTotalCount");
            return (Criteria) this;
        }

        public Criteria andOrderTotalCountPrepayIsNull() {
            addCriterion("order_total_count_prepay is null");
            return (Criteria) this;
        }

        public Criteria andOrderTotalCountPrepayIsNotNull() {
            addCriterion("order_total_count_prepay is not null");
            return (Criteria) this;
        }

        public Criteria andOrderTotalCountPrepayEqualTo(Long value) {
            addCriterion("order_total_count_prepay =", value, "orderTotalCountPrepay");
            return (Criteria) this;
        }

        public Criteria andOrderTotalCountPrepayEqualToColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("order_total_count_prepay = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderTotalCountPrepayNotEqualTo(Long value) {
            addCriterion("order_total_count_prepay <>", value, "orderTotalCountPrepay");
            return (Criteria) this;
        }

        public Criteria andOrderTotalCountPrepayNotEqualToColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("order_total_count_prepay <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderTotalCountPrepayGreaterThan(Long value) {
            addCriterion("order_total_count_prepay >", value, "orderTotalCountPrepay");
            return (Criteria) this;
        }

        public Criteria andOrderTotalCountPrepayGreaterThanColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("order_total_count_prepay > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderTotalCountPrepayGreaterThanOrEqualTo(Long value) {
            addCriterion("order_total_count_prepay >=", value, "orderTotalCountPrepay");
            return (Criteria) this;
        }

        public Criteria andOrderTotalCountPrepayGreaterThanOrEqualToColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("order_total_count_prepay >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderTotalCountPrepayLessThan(Long value) {
            addCriterion("order_total_count_prepay <", value, "orderTotalCountPrepay");
            return (Criteria) this;
        }

        public Criteria andOrderTotalCountPrepayLessThanColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("order_total_count_prepay < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderTotalCountPrepayLessThanOrEqualTo(Long value) {
            addCriterion("order_total_count_prepay <=", value, "orderTotalCountPrepay");
            return (Criteria) this;
        }

        public Criteria andOrderTotalCountPrepayLessThanOrEqualToColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("order_total_count_prepay <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderTotalCountPrepayIn(List<Long> values) {
            addCriterion("order_total_count_prepay in", values, "orderTotalCountPrepay");
            return (Criteria) this;
        }

        public Criteria andOrderTotalCountPrepayNotIn(List<Long> values) {
            addCriterion("order_total_count_prepay not in", values, "orderTotalCountPrepay");
            return (Criteria) this;
        }

        public Criteria andOrderTotalCountPrepayBetween(Long value1, Long value2) {
            addCriterion("order_total_count_prepay between", value1, value2, "orderTotalCountPrepay");
            return (Criteria) this;
        }

        public Criteria andOrderTotalCountPrepayNotBetween(Long value1, Long value2) {
            addCriterion("order_total_count_prepay not between", value1, value2, "orderTotalCountPrepay");
            return (Criteria) this;
        }

        public Criteria andOrderTotalCountAfterpayIsNull() {
            addCriterion("order_total_count_afterpay is null");
            return (Criteria) this;
        }

        public Criteria andOrderTotalCountAfterpayIsNotNull() {
            addCriterion("order_total_count_afterpay is not null");
            return (Criteria) this;
        }

        public Criteria andOrderTotalCountAfterpayEqualTo(Long value) {
            addCriterion("order_total_count_afterpay =", value, "orderTotalCountAfterpay");
            return (Criteria) this;
        }

        public Criteria andOrderTotalCountAfterpayEqualToColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("order_total_count_afterpay = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderTotalCountAfterpayNotEqualTo(Long value) {
            addCriterion("order_total_count_afterpay <>", value, "orderTotalCountAfterpay");
            return (Criteria) this;
        }

        public Criteria andOrderTotalCountAfterpayNotEqualToColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("order_total_count_afterpay <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderTotalCountAfterpayGreaterThan(Long value) {
            addCriterion("order_total_count_afterpay >", value, "orderTotalCountAfterpay");
            return (Criteria) this;
        }

        public Criteria andOrderTotalCountAfterpayGreaterThanColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("order_total_count_afterpay > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderTotalCountAfterpayGreaterThanOrEqualTo(Long value) {
            addCriterion("order_total_count_afterpay >=", value, "orderTotalCountAfterpay");
            return (Criteria) this;
        }

        public Criteria andOrderTotalCountAfterpayGreaterThanOrEqualToColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("order_total_count_afterpay >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderTotalCountAfterpayLessThan(Long value) {
            addCriterion("order_total_count_afterpay <", value, "orderTotalCountAfterpay");
            return (Criteria) this;
        }

        public Criteria andOrderTotalCountAfterpayLessThanColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("order_total_count_afterpay < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderTotalCountAfterpayLessThanOrEqualTo(Long value) {
            addCriterion("order_total_count_afterpay <=", value, "orderTotalCountAfterpay");
            return (Criteria) this;
        }

        public Criteria andOrderTotalCountAfterpayLessThanOrEqualToColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("order_total_count_afterpay <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderTotalCountAfterpayIn(List<Long> values) {
            addCriterion("order_total_count_afterpay in", values, "orderTotalCountAfterpay");
            return (Criteria) this;
        }

        public Criteria andOrderTotalCountAfterpayNotIn(List<Long> values) {
            addCriterion("order_total_count_afterpay not in", values, "orderTotalCountAfterpay");
            return (Criteria) this;
        }

        public Criteria andOrderTotalCountAfterpayBetween(Long value1, Long value2) {
            addCriterion("order_total_count_afterpay between", value1, value2, "orderTotalCountAfterpay");
            return (Criteria) this;
        }

        public Criteria andOrderTotalCountAfterpayNotBetween(Long value1, Long value2) {
            addCriterion("order_total_count_afterpay not between", value1, value2, "orderTotalCountAfterpay");
            return (Criteria) this;
        }

        public Criteria andOrderTotalCountMixIsNull() {
            addCriterion("order_total_count_mix is null");
            return (Criteria) this;
        }

        public Criteria andOrderTotalCountMixIsNotNull() {
            addCriterion("order_total_count_mix is not null");
            return (Criteria) this;
        }

        public Criteria andOrderTotalCountMixEqualTo(Long value) {
            addCriterion("order_total_count_mix =", value, "orderTotalCountMix");
            return (Criteria) this;
        }

        public Criteria andOrderTotalCountMixEqualToColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("order_total_count_mix = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderTotalCountMixNotEqualTo(Long value) {
            addCriterion("order_total_count_mix <>", value, "orderTotalCountMix");
            return (Criteria) this;
        }

        public Criteria andOrderTotalCountMixNotEqualToColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("order_total_count_mix <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderTotalCountMixGreaterThan(Long value) {
            addCriterion("order_total_count_mix >", value, "orderTotalCountMix");
            return (Criteria) this;
        }

        public Criteria andOrderTotalCountMixGreaterThanColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("order_total_count_mix > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderTotalCountMixGreaterThanOrEqualTo(Long value) {
            addCriterion("order_total_count_mix >=", value, "orderTotalCountMix");
            return (Criteria) this;
        }

        public Criteria andOrderTotalCountMixGreaterThanOrEqualToColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("order_total_count_mix >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderTotalCountMixLessThan(Long value) {
            addCriterion("order_total_count_mix <", value, "orderTotalCountMix");
            return (Criteria) this;
        }

        public Criteria andOrderTotalCountMixLessThanColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("order_total_count_mix < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderTotalCountMixLessThanOrEqualTo(Long value) {
            addCriterion("order_total_count_mix <=", value, "orderTotalCountMix");
            return (Criteria) this;
        }

        public Criteria andOrderTotalCountMixLessThanOrEqualToColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("order_total_count_mix <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderTotalCountMixIn(List<Long> values) {
            addCriterion("order_total_count_mix in", values, "orderTotalCountMix");
            return (Criteria) this;
        }

        public Criteria andOrderTotalCountMixNotIn(List<Long> values) {
            addCriterion("order_total_count_mix not in", values, "orderTotalCountMix");
            return (Criteria) this;
        }

        public Criteria andOrderTotalCountMixBetween(Long value1, Long value2) {
            addCriterion("order_total_count_mix between", value1, value2, "orderTotalCountMix");
            return (Criteria) this;
        }

        public Criteria andOrderTotalCountMixNotBetween(Long value1, Long value2) {
            addCriterion("order_total_count_mix not between", value1, value2, "orderTotalCountMix");
            return (Criteria) this;
        }

        public Criteria andOrderTotalPointIsNull() {
            addCriterion("order_total_point is null");
            return (Criteria) this;
        }

        public Criteria andOrderTotalPointIsNotNull() {
            addCriterion("order_total_point is not null");
            return (Criteria) this;
        }

        public Criteria andOrderTotalPointEqualTo(Long value) {
            addCriterion("order_total_point =", value, "orderTotalPoint");
            return (Criteria) this;
        }

        public Criteria andOrderTotalPointEqualToColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("order_total_point = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderTotalPointNotEqualTo(Long value) {
            addCriterion("order_total_point <>", value, "orderTotalPoint");
            return (Criteria) this;
        }

        public Criteria andOrderTotalPointNotEqualToColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("order_total_point <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderTotalPointGreaterThan(Long value) {
            addCriterion("order_total_point >", value, "orderTotalPoint");
            return (Criteria) this;
        }

        public Criteria andOrderTotalPointGreaterThanColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("order_total_point > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderTotalPointGreaterThanOrEqualTo(Long value) {
            addCriterion("order_total_point >=", value, "orderTotalPoint");
            return (Criteria) this;
        }

        public Criteria andOrderTotalPointGreaterThanOrEqualToColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("order_total_point >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderTotalPointLessThan(Long value) {
            addCriterion("order_total_point <", value, "orderTotalPoint");
            return (Criteria) this;
        }

        public Criteria andOrderTotalPointLessThanColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("order_total_point < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderTotalPointLessThanOrEqualTo(Long value) {
            addCriterion("order_total_point <=", value, "orderTotalPoint");
            return (Criteria) this;
        }

        public Criteria andOrderTotalPointLessThanOrEqualToColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("order_total_point <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderTotalPointIn(List<Long> values) {
            addCriterion("order_total_point in", values, "orderTotalPoint");
            return (Criteria) this;
        }

        public Criteria andOrderTotalPointNotIn(List<Long> values) {
            addCriterion("order_total_point not in", values, "orderTotalPoint");
            return (Criteria) this;
        }

        public Criteria andOrderTotalPointBetween(Long value1, Long value2) {
            addCriterion("order_total_point between", value1, value2, "orderTotalPoint");
            return (Criteria) this;
        }

        public Criteria andOrderTotalPointNotBetween(Long value1, Long value2) {
            addCriterion("order_total_point not between", value1, value2, "orderTotalPoint");
            return (Criteria) this;
        }

        public Criteria andSpuTotalCountIsNull() {
            addCriterion("spu_total_count is null");
            return (Criteria) this;
        }

        public Criteria andSpuTotalCountIsNotNull() {
            addCriterion("spu_total_count is not null");
            return (Criteria) this;
        }

        public Criteria andSpuTotalCountEqualTo(Long value) {
            addCriterion("spu_total_count =", value, "spuTotalCount");
            return (Criteria) this;
        }

        public Criteria andSpuTotalCountEqualToColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("spu_total_count = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpuTotalCountNotEqualTo(Long value) {
            addCriterion("spu_total_count <>", value, "spuTotalCount");
            return (Criteria) this;
        }

        public Criteria andSpuTotalCountNotEqualToColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("spu_total_count <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpuTotalCountGreaterThan(Long value) {
            addCriterion("spu_total_count >", value, "spuTotalCount");
            return (Criteria) this;
        }

        public Criteria andSpuTotalCountGreaterThanColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("spu_total_count > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpuTotalCountGreaterThanOrEqualTo(Long value) {
            addCriterion("spu_total_count >=", value, "spuTotalCount");
            return (Criteria) this;
        }

        public Criteria andSpuTotalCountGreaterThanOrEqualToColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("spu_total_count >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpuTotalCountLessThan(Long value) {
            addCriterion("spu_total_count <", value, "spuTotalCount");
            return (Criteria) this;
        }

        public Criteria andSpuTotalCountLessThanColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("spu_total_count < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpuTotalCountLessThanOrEqualTo(Long value) {
            addCriterion("spu_total_count <=", value, "spuTotalCount");
            return (Criteria) this;
        }

        public Criteria andSpuTotalCountLessThanOrEqualToColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("spu_total_count <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpuTotalCountIn(List<Long> values) {
            addCriterion("spu_total_count in", values, "spuTotalCount");
            return (Criteria) this;
        }

        public Criteria andSpuTotalCountNotIn(List<Long> values) {
            addCriterion("spu_total_count not in", values, "spuTotalCount");
            return (Criteria) this;
        }

        public Criteria andSpuTotalCountBetween(Long value1, Long value2) {
            addCriterion("spu_total_count between", value1, value2, "spuTotalCount");
            return (Criteria) this;
        }

        public Criteria andSpuTotalCountNotBetween(Long value1, Long value2) {
            addCriterion("spu_total_count not between", value1, value2, "spuTotalCount");
            return (Criteria) this;
        }

        public Criteria andSaleRankingIsNull() {
            addCriterion("sale_ranking is null");
            return (Criteria) this;
        }

        public Criteria andSaleRankingIsNotNull() {
            addCriterion("sale_ranking is not null");
            return (Criteria) this;
        }

        public Criteria andSaleRankingEqualTo(Long value) {
            addCriterion("sale_ranking =", value, "saleRanking");
            return (Criteria) this;
        }

        public Criteria andSaleRankingEqualToColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("sale_ranking = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSaleRankingNotEqualTo(Long value) {
            addCriterion("sale_ranking <>", value, "saleRanking");
            return (Criteria) this;
        }

        public Criteria andSaleRankingNotEqualToColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("sale_ranking <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSaleRankingGreaterThan(Long value) {
            addCriterion("sale_ranking >", value, "saleRanking");
            return (Criteria) this;
        }

        public Criteria andSaleRankingGreaterThanColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("sale_ranking > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSaleRankingGreaterThanOrEqualTo(Long value) {
            addCriterion("sale_ranking >=", value, "saleRanking");
            return (Criteria) this;
        }

        public Criteria andSaleRankingGreaterThanOrEqualToColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("sale_ranking >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSaleRankingLessThan(Long value) {
            addCriterion("sale_ranking <", value, "saleRanking");
            return (Criteria) this;
        }

        public Criteria andSaleRankingLessThanColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("sale_ranking < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSaleRankingLessThanOrEqualTo(Long value) {
            addCriterion("sale_ranking <=", value, "saleRanking");
            return (Criteria) this;
        }

        public Criteria andSaleRankingLessThanOrEqualToColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("sale_ranking <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSaleRankingIn(List<Long> values) {
            addCriterion("sale_ranking in", values, "saleRanking");
            return (Criteria) this;
        }

        public Criteria andSaleRankingNotIn(List<Long> values) {
            addCriterion("sale_ranking not in", values, "saleRanking");
            return (Criteria) this;
        }

        public Criteria andSaleRankingBetween(Long value1, Long value2) {
            addCriterion("sale_ranking between", value1, value2, "saleRanking");
            return (Criteria) this;
        }

        public Criteria andSaleRankingNotBetween(Long value1, Long value2) {
            addCriterion("sale_ranking not between", value1, value2, "saleRanking");
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuCodeIsNull() {
            addCriterion("best_sale_spu_code is null");
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuCodeIsNotNull() {
            addCriterion("best_sale_spu_code is not null");
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuCodeEqualTo(String value) {
            addCriterion("best_sale_spu_code =", value, "bestSaleSpuCode");
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuCodeEqualToColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("best_sale_spu_code = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuCodeNotEqualTo(String value) {
            addCriterion("best_sale_spu_code <>", value, "bestSaleSpuCode");
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuCodeNotEqualToColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("best_sale_spu_code <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuCodeGreaterThan(String value) {
            addCriterion("best_sale_spu_code >", value, "bestSaleSpuCode");
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuCodeGreaterThanColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("best_sale_spu_code > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuCodeGreaterThanOrEqualTo(String value) {
            addCriterion("best_sale_spu_code >=", value, "bestSaleSpuCode");
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuCodeGreaterThanOrEqualToColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("best_sale_spu_code >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuCodeLessThan(String value) {
            addCriterion("best_sale_spu_code <", value, "bestSaleSpuCode");
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuCodeLessThanColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("best_sale_spu_code < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuCodeLessThanOrEqualTo(String value) {
            addCriterion("best_sale_spu_code <=", value, "bestSaleSpuCode");
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuCodeLessThanOrEqualToColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("best_sale_spu_code <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuCodeLike(String value) {
            addCriterion("best_sale_spu_code like", value, "bestSaleSpuCode");
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuCodeNotLike(String value) {
            addCriterion("best_sale_spu_code not like", value, "bestSaleSpuCode");
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuCodeIn(List<String> values) {
            addCriterion("best_sale_spu_code in", values, "bestSaleSpuCode");
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuCodeNotIn(List<String> values) {
            addCriterion("best_sale_spu_code not in", values, "bestSaleSpuCode");
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuCodeBetween(String value1, String value2) {
            addCriterion("best_sale_spu_code between", value1, value2, "bestSaleSpuCode");
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuCodeNotBetween(String value1, String value2) {
            addCriterion("best_sale_spu_code not between", value1, value2, "bestSaleSpuCode");
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuNameIsNull() {
            addCriterion("best_sale_spu_name is null");
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuNameIsNotNull() {
            addCriterion("best_sale_spu_name is not null");
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuNameEqualTo(String value) {
            addCriterion("best_sale_spu_name =", value, "bestSaleSpuName");
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuNameEqualToColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("best_sale_spu_name = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuNameNotEqualTo(String value) {
            addCriterion("best_sale_spu_name <>", value, "bestSaleSpuName");
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuNameNotEqualToColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("best_sale_spu_name <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuNameGreaterThan(String value) {
            addCriterion("best_sale_spu_name >", value, "bestSaleSpuName");
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuNameGreaterThanColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("best_sale_spu_name > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuNameGreaterThanOrEqualTo(String value) {
            addCriterion("best_sale_spu_name >=", value, "bestSaleSpuName");
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuNameGreaterThanOrEqualToColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("best_sale_spu_name >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuNameLessThan(String value) {
            addCriterion("best_sale_spu_name <", value, "bestSaleSpuName");
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuNameLessThanColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("best_sale_spu_name < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuNameLessThanOrEqualTo(String value) {
            addCriterion("best_sale_spu_name <=", value, "bestSaleSpuName");
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuNameLessThanOrEqualToColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("best_sale_spu_name <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuNameLike(String value) {
            addCriterion("best_sale_spu_name like", value, "bestSaleSpuName");
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuNameNotLike(String value) {
            addCriterion("best_sale_spu_name not like", value, "bestSaleSpuName");
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuNameIn(List<String> values) {
            addCriterion("best_sale_spu_name in", values, "bestSaleSpuName");
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuNameNotIn(List<String> values) {
            addCriterion("best_sale_spu_name not in", values, "bestSaleSpuName");
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuNameBetween(String value1, String value2) {
            addCriterion("best_sale_spu_name between", value1, value2, "bestSaleSpuName");
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuNameNotBetween(String value1, String value2) {
            addCriterion("best_sale_spu_name not between", value1, value2, "bestSaleSpuName");
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuOrderCountIsNull() {
            addCriterion("best_sale_spu_order_count is null");
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuOrderCountIsNotNull() {
            addCriterion("best_sale_spu_order_count is not null");
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuOrderCountEqualTo(Long value) {
            addCriterion("best_sale_spu_order_count =", value, "bestSaleSpuOrderCount");
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuOrderCountEqualToColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("best_sale_spu_order_count = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuOrderCountNotEqualTo(Long value) {
            addCriterion("best_sale_spu_order_count <>", value, "bestSaleSpuOrderCount");
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuOrderCountNotEqualToColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("best_sale_spu_order_count <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuOrderCountGreaterThan(Long value) {
            addCriterion("best_sale_spu_order_count >", value, "bestSaleSpuOrderCount");
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuOrderCountGreaterThanColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("best_sale_spu_order_count > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuOrderCountGreaterThanOrEqualTo(Long value) {
            addCriterion("best_sale_spu_order_count >=", value, "bestSaleSpuOrderCount");
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuOrderCountGreaterThanOrEqualToColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("best_sale_spu_order_count >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuOrderCountLessThan(Long value) {
            addCriterion("best_sale_spu_order_count <", value, "bestSaleSpuOrderCount");
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuOrderCountLessThanColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("best_sale_spu_order_count < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuOrderCountLessThanOrEqualTo(Long value) {
            addCriterion("best_sale_spu_order_count <=", value, "bestSaleSpuOrderCount");
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuOrderCountLessThanOrEqualToColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("best_sale_spu_order_count <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuOrderCountIn(List<Long> values) {
            addCriterion("best_sale_spu_order_count in", values, "bestSaleSpuOrderCount");
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuOrderCountNotIn(List<Long> values) {
            addCriterion("best_sale_spu_order_count not in", values, "bestSaleSpuOrderCount");
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuOrderCountBetween(Long value1, Long value2) {
            addCriterion("best_sale_spu_order_count between", value1, value2, "bestSaleSpuOrderCount");
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuOrderCountNotBetween(Long value1, Long value2) {
            addCriterion("best_sale_spu_order_count not between", value1, value2, "bestSaleSpuOrderCount");
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuCodeGroupIsNull() {
            addCriterion("best_sale_spu_code_group is null");
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuCodeGroupIsNotNull() {
            addCriterion("best_sale_spu_code_group is not null");
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuCodeGroupEqualTo(String value) {
            addCriterion("best_sale_spu_code_group =", value, "bestSaleSpuCodeGroup");
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuCodeGroupEqualToColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("best_sale_spu_code_group = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuCodeGroupNotEqualTo(String value) {
            addCriterion("best_sale_spu_code_group <>", value, "bestSaleSpuCodeGroup");
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuCodeGroupNotEqualToColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("best_sale_spu_code_group <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuCodeGroupGreaterThan(String value) {
            addCriterion("best_sale_spu_code_group >", value, "bestSaleSpuCodeGroup");
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuCodeGroupGreaterThanColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("best_sale_spu_code_group > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuCodeGroupGreaterThanOrEqualTo(String value) {
            addCriterion("best_sale_spu_code_group >=", value, "bestSaleSpuCodeGroup");
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuCodeGroupGreaterThanOrEqualToColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("best_sale_spu_code_group >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuCodeGroupLessThan(String value) {
            addCriterion("best_sale_spu_code_group <", value, "bestSaleSpuCodeGroup");
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuCodeGroupLessThanColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("best_sale_spu_code_group < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuCodeGroupLessThanOrEqualTo(String value) {
            addCriterion("best_sale_spu_code_group <=", value, "bestSaleSpuCodeGroup");
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuCodeGroupLessThanOrEqualToColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("best_sale_spu_code_group <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuCodeGroupLike(String value) {
            addCriterion("best_sale_spu_code_group like", value, "bestSaleSpuCodeGroup");
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuCodeGroupNotLike(String value) {
            addCriterion("best_sale_spu_code_group not like", value, "bestSaleSpuCodeGroup");
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuCodeGroupIn(List<String> values) {
            addCriterion("best_sale_spu_code_group in", values, "bestSaleSpuCodeGroup");
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuCodeGroupNotIn(List<String> values) {
            addCriterion("best_sale_spu_code_group not in", values, "bestSaleSpuCodeGroup");
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuCodeGroupBetween(String value1, String value2) {
            addCriterion("best_sale_spu_code_group between", value1, value2, "bestSaleSpuCodeGroup");
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuCodeGroupNotBetween(String value1, String value2) {
            addCriterion("best_sale_spu_code_group not between", value1, value2, "bestSaleSpuCodeGroup");
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuNameGroupIsNull() {
            addCriterion("best_sale_spu_name_group is null");
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuNameGroupIsNotNull() {
            addCriterion("best_sale_spu_name_group is not null");
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuNameGroupEqualTo(String value) {
            addCriterion("best_sale_spu_name_group =", value, "bestSaleSpuNameGroup");
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuNameGroupEqualToColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("best_sale_spu_name_group = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuNameGroupNotEqualTo(String value) {
            addCriterion("best_sale_spu_name_group <>", value, "bestSaleSpuNameGroup");
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuNameGroupNotEqualToColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("best_sale_spu_name_group <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuNameGroupGreaterThan(String value) {
            addCriterion("best_sale_spu_name_group >", value, "bestSaleSpuNameGroup");
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuNameGroupGreaterThanColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("best_sale_spu_name_group > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuNameGroupGreaterThanOrEqualTo(String value) {
            addCriterion("best_sale_spu_name_group >=", value, "bestSaleSpuNameGroup");
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuNameGroupGreaterThanOrEqualToColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("best_sale_spu_name_group >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuNameGroupLessThan(String value) {
            addCriterion("best_sale_spu_name_group <", value, "bestSaleSpuNameGroup");
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuNameGroupLessThanColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("best_sale_spu_name_group < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuNameGroupLessThanOrEqualTo(String value) {
            addCriterion("best_sale_spu_name_group <=", value, "bestSaleSpuNameGroup");
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuNameGroupLessThanOrEqualToColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("best_sale_spu_name_group <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuNameGroupLike(String value) {
            addCriterion("best_sale_spu_name_group like", value, "bestSaleSpuNameGroup");
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuNameGroupNotLike(String value) {
            addCriterion("best_sale_spu_name_group not like", value, "bestSaleSpuNameGroup");
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuNameGroupIn(List<String> values) {
            addCriterion("best_sale_spu_name_group in", values, "bestSaleSpuNameGroup");
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuNameGroupNotIn(List<String> values) {
            addCriterion("best_sale_spu_name_group not in", values, "bestSaleSpuNameGroup");
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuNameGroupBetween(String value1, String value2) {
            addCriterion("best_sale_spu_name_group between", value1, value2, "bestSaleSpuNameGroup");
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuNameGroupNotBetween(String value1, String value2) {
            addCriterion("best_sale_spu_name_group not between", value1, value2, "bestSaleSpuNameGroup");
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuCountGroupIsNull() {
            addCriterion("best_sale_spu_count_group is null");
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuCountGroupIsNotNull() {
            addCriterion("best_sale_spu_count_group is not null");
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuCountGroupEqualTo(Long value) {
            addCriterion("best_sale_spu_count_group =", value, "bestSaleSpuCountGroup");
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuCountGroupEqualToColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("best_sale_spu_count_group = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuCountGroupNotEqualTo(Long value) {
            addCriterion("best_sale_spu_count_group <>", value, "bestSaleSpuCountGroup");
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuCountGroupNotEqualToColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("best_sale_spu_count_group <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuCountGroupGreaterThan(Long value) {
            addCriterion("best_sale_spu_count_group >", value, "bestSaleSpuCountGroup");
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuCountGroupGreaterThanColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("best_sale_spu_count_group > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuCountGroupGreaterThanOrEqualTo(Long value) {
            addCriterion("best_sale_spu_count_group >=", value, "bestSaleSpuCountGroup");
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuCountGroupGreaterThanOrEqualToColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("best_sale_spu_count_group >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuCountGroupLessThan(Long value) {
            addCriterion("best_sale_spu_count_group <", value, "bestSaleSpuCountGroup");
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuCountGroupLessThanColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("best_sale_spu_count_group < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuCountGroupLessThanOrEqualTo(Long value) {
            addCriterion("best_sale_spu_count_group <=", value, "bestSaleSpuCountGroup");
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuCountGroupLessThanOrEqualToColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("best_sale_spu_count_group <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuCountGroupIn(List<Long> values) {
            addCriterion("best_sale_spu_count_group in", values, "bestSaleSpuCountGroup");
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuCountGroupNotIn(List<Long> values) {
            addCriterion("best_sale_spu_count_group not in", values, "bestSaleSpuCountGroup");
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuCountGroupBetween(Long value1, Long value2) {
            addCriterion("best_sale_spu_count_group between", value1, value2, "bestSaleSpuCountGroup");
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuCountGroupNotBetween(Long value1, Long value2) {
            addCriterion("best_sale_spu_count_group not between", value1, value2, "bestSaleSpuCountGroup");
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuCodeIndividualIsNull() {
            addCriterion("best_sale_spu_code_individual is null");
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuCodeIndividualIsNotNull() {
            addCriterion("best_sale_spu_code_individual is not null");
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuCodeIndividualEqualTo(String value) {
            addCriterion("best_sale_spu_code_individual =", value, "bestSaleSpuCodeIndividual");
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuCodeIndividualEqualToColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("best_sale_spu_code_individual = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuCodeIndividualNotEqualTo(String value) {
            addCriterion("best_sale_spu_code_individual <>", value, "bestSaleSpuCodeIndividual");
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuCodeIndividualNotEqualToColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("best_sale_spu_code_individual <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuCodeIndividualGreaterThan(String value) {
            addCriterion("best_sale_spu_code_individual >", value, "bestSaleSpuCodeIndividual");
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuCodeIndividualGreaterThanColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("best_sale_spu_code_individual > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuCodeIndividualGreaterThanOrEqualTo(String value) {
            addCriterion("best_sale_spu_code_individual >=", value, "bestSaleSpuCodeIndividual");
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuCodeIndividualGreaterThanOrEqualToColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("best_sale_spu_code_individual >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuCodeIndividualLessThan(String value) {
            addCriterion("best_sale_spu_code_individual <", value, "bestSaleSpuCodeIndividual");
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuCodeIndividualLessThanColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("best_sale_spu_code_individual < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuCodeIndividualLessThanOrEqualTo(String value) {
            addCriterion("best_sale_spu_code_individual <=", value, "bestSaleSpuCodeIndividual");
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuCodeIndividualLessThanOrEqualToColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("best_sale_spu_code_individual <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuCodeIndividualLike(String value) {
            addCriterion("best_sale_spu_code_individual like", value, "bestSaleSpuCodeIndividual");
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuCodeIndividualNotLike(String value) {
            addCriterion("best_sale_spu_code_individual not like", value, "bestSaleSpuCodeIndividual");
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuCodeIndividualIn(List<String> values) {
            addCriterion("best_sale_spu_code_individual in", values, "bestSaleSpuCodeIndividual");
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuCodeIndividualNotIn(List<String> values) {
            addCriterion("best_sale_spu_code_individual not in", values, "bestSaleSpuCodeIndividual");
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuCodeIndividualBetween(String value1, String value2) {
            addCriterion("best_sale_spu_code_individual between", value1, value2, "bestSaleSpuCodeIndividual");
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuCodeIndividualNotBetween(String value1, String value2) {
            addCriterion("best_sale_spu_code_individual not between", value1, value2, "bestSaleSpuCodeIndividual");
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuNameIndividualIsNull() {
            addCriterion("best_sale_spu_name_individual is null");
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuNameIndividualIsNotNull() {
            addCriterion("best_sale_spu_name_individual is not null");
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuNameIndividualEqualTo(String value) {
            addCriterion("best_sale_spu_name_individual =", value, "bestSaleSpuNameIndividual");
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuNameIndividualEqualToColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("best_sale_spu_name_individual = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuNameIndividualNotEqualTo(String value) {
            addCriterion("best_sale_spu_name_individual <>", value, "bestSaleSpuNameIndividual");
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuNameIndividualNotEqualToColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("best_sale_spu_name_individual <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuNameIndividualGreaterThan(String value) {
            addCriterion("best_sale_spu_name_individual >", value, "bestSaleSpuNameIndividual");
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuNameIndividualGreaterThanColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("best_sale_spu_name_individual > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuNameIndividualGreaterThanOrEqualTo(String value) {
            addCriterion("best_sale_spu_name_individual >=", value, "bestSaleSpuNameIndividual");
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuNameIndividualGreaterThanOrEqualToColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("best_sale_spu_name_individual >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuNameIndividualLessThan(String value) {
            addCriterion("best_sale_spu_name_individual <", value, "bestSaleSpuNameIndividual");
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuNameIndividualLessThanColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("best_sale_spu_name_individual < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuNameIndividualLessThanOrEqualTo(String value) {
            addCriterion("best_sale_spu_name_individual <=", value, "bestSaleSpuNameIndividual");
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuNameIndividualLessThanOrEqualToColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("best_sale_spu_name_individual <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuNameIndividualLike(String value) {
            addCriterion("best_sale_spu_name_individual like", value, "bestSaleSpuNameIndividual");
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuNameIndividualNotLike(String value) {
            addCriterion("best_sale_spu_name_individual not like", value, "bestSaleSpuNameIndividual");
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuNameIndividualIn(List<String> values) {
            addCriterion("best_sale_spu_name_individual in", values, "bestSaleSpuNameIndividual");
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuNameIndividualNotIn(List<String> values) {
            addCriterion("best_sale_spu_name_individual not in", values, "bestSaleSpuNameIndividual");
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuNameIndividualBetween(String value1, String value2) {
            addCriterion("best_sale_spu_name_individual between", value1, value2, "bestSaleSpuNameIndividual");
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuNameIndividualNotBetween(String value1, String value2) {
            addCriterion("best_sale_spu_name_individual not between", value1, value2, "bestSaleSpuNameIndividual");
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuCountIndividualIsNull() {
            addCriterion("best_sale_spu_count_individual is null");
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuCountIndividualIsNotNull() {
            addCriterion("best_sale_spu_count_individual is not null");
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuCountIndividualEqualTo(Long value) {
            addCriterion("best_sale_spu_count_individual =", value, "bestSaleSpuCountIndividual");
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuCountIndividualEqualToColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("best_sale_spu_count_individual = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuCountIndividualNotEqualTo(Long value) {
            addCriterion("best_sale_spu_count_individual <>", value, "bestSaleSpuCountIndividual");
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuCountIndividualNotEqualToColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("best_sale_spu_count_individual <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuCountIndividualGreaterThan(Long value) {
            addCriterion("best_sale_spu_count_individual >", value, "bestSaleSpuCountIndividual");
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuCountIndividualGreaterThanColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("best_sale_spu_count_individual > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuCountIndividualGreaterThanOrEqualTo(Long value) {
            addCriterion("best_sale_spu_count_individual >=", value, "bestSaleSpuCountIndividual");
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuCountIndividualGreaterThanOrEqualToColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("best_sale_spu_count_individual >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuCountIndividualLessThan(Long value) {
            addCriterion("best_sale_spu_count_individual <", value, "bestSaleSpuCountIndividual");
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuCountIndividualLessThanColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("best_sale_spu_count_individual < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuCountIndividualLessThanOrEqualTo(Long value) {
            addCriterion("best_sale_spu_count_individual <=", value, "bestSaleSpuCountIndividual");
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuCountIndividualLessThanOrEqualToColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("best_sale_spu_count_individual <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuCountIndividualIn(List<Long> values) {
            addCriterion("best_sale_spu_count_individual in", values, "bestSaleSpuCountIndividual");
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuCountIndividualNotIn(List<Long> values) {
            addCriterion("best_sale_spu_count_individual not in", values, "bestSaleSpuCountIndividual");
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuCountIndividualBetween(Long value1, Long value2) {
            addCriterion("best_sale_spu_count_individual between", value1, value2, "bestSaleSpuCountIndividual");
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuCountIndividualNotBetween(Long value1, Long value2) {
            addCriterion("best_sale_spu_count_individual not between", value1, value2, "bestSaleSpuCountIndividual");
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuPromptIsNull() {
            addCriterion("best_sale_spu_prompt is null");
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuPromptIsNotNull() {
            addCriterion("best_sale_spu_prompt is not null");
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuPromptEqualTo(String value) {
            addCriterion("best_sale_spu_prompt =", value, "bestSaleSpuPrompt");
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuPromptEqualToColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("best_sale_spu_prompt = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuPromptNotEqualTo(String value) {
            addCriterion("best_sale_spu_prompt <>", value, "bestSaleSpuPrompt");
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuPromptNotEqualToColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("best_sale_spu_prompt <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuPromptGreaterThan(String value) {
            addCriterion("best_sale_spu_prompt >", value, "bestSaleSpuPrompt");
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuPromptGreaterThanColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("best_sale_spu_prompt > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuPromptGreaterThanOrEqualTo(String value) {
            addCriterion("best_sale_spu_prompt >=", value, "bestSaleSpuPrompt");
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuPromptGreaterThanOrEqualToColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("best_sale_spu_prompt >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuPromptLessThan(String value) {
            addCriterion("best_sale_spu_prompt <", value, "bestSaleSpuPrompt");
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuPromptLessThanColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("best_sale_spu_prompt < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuPromptLessThanOrEqualTo(String value) {
            addCriterion("best_sale_spu_prompt <=", value, "bestSaleSpuPrompt");
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuPromptLessThanOrEqualToColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("best_sale_spu_prompt <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuPromptLike(String value) {
            addCriterion("best_sale_spu_prompt like", value, "bestSaleSpuPrompt");
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuPromptNotLike(String value) {
            addCriterion("best_sale_spu_prompt not like", value, "bestSaleSpuPrompt");
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuPromptIn(List<String> values) {
            addCriterion("best_sale_spu_prompt in", values, "bestSaleSpuPrompt");
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuPromptNotIn(List<String> values) {
            addCriterion("best_sale_spu_prompt not in", values, "bestSaleSpuPrompt");
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuPromptBetween(String value1, String value2) {
            addCriterion("best_sale_spu_prompt between", value1, value2, "bestSaleSpuPrompt");
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuPromptNotBetween(String value1, String value2) {
            addCriterion("best_sale_spu_prompt not between", value1, value2, "bestSaleSpuPrompt");
            return (Criteria) this;
        }

        public Criteria andYearLatestOrderTimeIsNull() {
            addCriterion("year_latest_order_time is null");
            return (Criteria) this;
        }

        public Criteria andYearLatestOrderTimeIsNotNull() {
            addCriterion("year_latest_order_time is not null");
            return (Criteria) this;
        }

        public Criteria andYearLatestOrderTimeEqualTo(Date value) {
            addCriterion("year_latest_order_time =", value, "yearLatestOrderTime");
            return (Criteria) this;
        }

        public Criteria andYearLatestOrderTimeEqualToColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("year_latest_order_time = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andYearLatestOrderTimeNotEqualTo(Date value) {
            addCriterion("year_latest_order_time <>", value, "yearLatestOrderTime");
            return (Criteria) this;
        }

        public Criteria andYearLatestOrderTimeNotEqualToColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("year_latest_order_time <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andYearLatestOrderTimeGreaterThan(Date value) {
            addCriterion("year_latest_order_time >", value, "yearLatestOrderTime");
            return (Criteria) this;
        }

        public Criteria andYearLatestOrderTimeGreaterThanColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("year_latest_order_time > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andYearLatestOrderTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("year_latest_order_time >=", value, "yearLatestOrderTime");
            return (Criteria) this;
        }

        public Criteria andYearLatestOrderTimeGreaterThanOrEqualToColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("year_latest_order_time >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andYearLatestOrderTimeLessThan(Date value) {
            addCriterion("year_latest_order_time <", value, "yearLatestOrderTime");
            return (Criteria) this;
        }

        public Criteria andYearLatestOrderTimeLessThanColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("year_latest_order_time < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andYearLatestOrderTimeLessThanOrEqualTo(Date value) {
            addCriterion("year_latest_order_time <=", value, "yearLatestOrderTime");
            return (Criteria) this;
        }

        public Criteria andYearLatestOrderTimeLessThanOrEqualToColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("year_latest_order_time <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andYearLatestOrderTimeIn(List<Date> values) {
            addCriterion("year_latest_order_time in", values, "yearLatestOrderTime");
            return (Criteria) this;
        }

        public Criteria andYearLatestOrderTimeNotIn(List<Date> values) {
            addCriterion("year_latest_order_time not in", values, "yearLatestOrderTime");
            return (Criteria) this;
        }

        public Criteria andYearLatestOrderTimeBetween(Date value1, Date value2) {
            addCriterion("year_latest_order_time between", value1, value2, "yearLatestOrderTime");
            return (Criteria) this;
        }

        public Criteria andYearLatestOrderTimeNotBetween(Date value1, Date value2) {
            addCriterion("year_latest_order_time not between", value1, value2, "yearLatestOrderTime");
            return (Criteria) this;
        }

        public Criteria andJoinMallActivityCountIsNull() {
            addCriterion("join_mall_activity_count is null");
            return (Criteria) this;
        }

        public Criteria andJoinMallActivityCountIsNotNull() {
            addCriterion("join_mall_activity_count is not null");
            return (Criteria) this;
        }

        public Criteria andJoinMallActivityCountEqualTo(Integer value) {
            addCriterion("join_mall_activity_count =", value, "joinMallActivityCount");
            return (Criteria) this;
        }

        public Criteria andJoinMallActivityCountEqualToColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("join_mall_activity_count = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andJoinMallActivityCountNotEqualTo(Integer value) {
            addCriterion("join_mall_activity_count <>", value, "joinMallActivityCount");
            return (Criteria) this;
        }

        public Criteria andJoinMallActivityCountNotEqualToColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("join_mall_activity_count <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andJoinMallActivityCountGreaterThan(Integer value) {
            addCriterion("join_mall_activity_count >", value, "joinMallActivityCount");
            return (Criteria) this;
        }

        public Criteria andJoinMallActivityCountGreaterThanColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("join_mall_activity_count > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andJoinMallActivityCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("join_mall_activity_count >=", value, "joinMallActivityCount");
            return (Criteria) this;
        }

        public Criteria andJoinMallActivityCountGreaterThanOrEqualToColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("join_mall_activity_count >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andJoinMallActivityCountLessThan(Integer value) {
            addCriterion("join_mall_activity_count <", value, "joinMallActivityCount");
            return (Criteria) this;
        }

        public Criteria andJoinMallActivityCountLessThanColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("join_mall_activity_count < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andJoinMallActivityCountLessThanOrEqualTo(Integer value) {
            addCriterion("join_mall_activity_count <=", value, "joinMallActivityCount");
            return (Criteria) this;
        }

        public Criteria andJoinMallActivityCountLessThanOrEqualToColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("join_mall_activity_count <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andJoinMallActivityCountIn(List<Integer> values) {
            addCriterion("join_mall_activity_count in", values, "joinMallActivityCount");
            return (Criteria) this;
        }

        public Criteria andJoinMallActivityCountNotIn(List<Integer> values) {
            addCriterion("join_mall_activity_count not in", values, "joinMallActivityCount");
            return (Criteria) this;
        }

        public Criteria andJoinMallActivityCountBetween(Integer value1, Integer value2) {
            addCriterion("join_mall_activity_count between", value1, value2, "joinMallActivityCount");
            return (Criteria) this;
        }

        public Criteria andJoinMallActivityCountNotBetween(Integer value1, Integer value2) {
            addCriterion("join_mall_activity_count not between", value1, value2, "joinMallActivityCount");
            return (Criteria) this;
        }

        public Criteria andJoinMallActivityRewardCountIsNull() {
            addCriterion("join_mall_activity_reward_count is null");
            return (Criteria) this;
        }

        public Criteria andJoinMallActivityRewardCountIsNotNull() {
            addCriterion("join_mall_activity_reward_count is not null");
            return (Criteria) this;
        }

        public Criteria andJoinMallActivityRewardCountEqualTo(Integer value) {
            addCriterion("join_mall_activity_reward_count =", value, "joinMallActivityRewardCount");
            return (Criteria) this;
        }

        public Criteria andJoinMallActivityRewardCountEqualToColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("join_mall_activity_reward_count = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andJoinMallActivityRewardCountNotEqualTo(Integer value) {
            addCriterion("join_mall_activity_reward_count <>", value, "joinMallActivityRewardCount");
            return (Criteria) this;
        }

        public Criteria andJoinMallActivityRewardCountNotEqualToColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("join_mall_activity_reward_count <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andJoinMallActivityRewardCountGreaterThan(Integer value) {
            addCriterion("join_mall_activity_reward_count >", value, "joinMallActivityRewardCount");
            return (Criteria) this;
        }

        public Criteria andJoinMallActivityRewardCountGreaterThanColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("join_mall_activity_reward_count > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andJoinMallActivityRewardCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("join_mall_activity_reward_count >=", value, "joinMallActivityRewardCount");
            return (Criteria) this;
        }

        public Criteria andJoinMallActivityRewardCountGreaterThanOrEqualToColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("join_mall_activity_reward_count >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andJoinMallActivityRewardCountLessThan(Integer value) {
            addCriterion("join_mall_activity_reward_count <", value, "joinMallActivityRewardCount");
            return (Criteria) this;
        }

        public Criteria andJoinMallActivityRewardCountLessThanColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("join_mall_activity_reward_count < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andJoinMallActivityRewardCountLessThanOrEqualTo(Integer value) {
            addCriterion("join_mall_activity_reward_count <=", value, "joinMallActivityRewardCount");
            return (Criteria) this;
        }

        public Criteria andJoinMallActivityRewardCountLessThanOrEqualToColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("join_mall_activity_reward_count <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andJoinMallActivityRewardCountIn(List<Integer> values) {
            addCriterion("join_mall_activity_reward_count in", values, "joinMallActivityRewardCount");
            return (Criteria) this;
        }

        public Criteria andJoinMallActivityRewardCountNotIn(List<Integer> values) {
            addCriterion("join_mall_activity_reward_count not in", values, "joinMallActivityRewardCount");
            return (Criteria) this;
        }

        public Criteria andJoinMallActivityRewardCountBetween(Integer value1, Integer value2) {
            addCriterion("join_mall_activity_reward_count between", value1, value2, "joinMallActivityRewardCount");
            return (Criteria) this;
        }

        public Criteria andJoinMallActivityRewardCountNotBetween(Integer value1, Integer value2) {
            addCriterion("join_mall_activity_reward_count not between", value1, value2, "joinMallActivityRewardCount");
            return (Criteria) this;
        }

        public Criteria andJoinMallBestActivityNameIsNull() {
            addCriterion("join_mall_best_activity_name is null");
            return (Criteria) this;
        }

        public Criteria andJoinMallBestActivityNameIsNotNull() {
            addCriterion("join_mall_best_activity_name is not null");
            return (Criteria) this;
        }

        public Criteria andJoinMallBestActivityNameEqualTo(String value) {
            addCriterion("join_mall_best_activity_name =", value, "joinMallBestActivityName");
            return (Criteria) this;
        }

        public Criteria andJoinMallBestActivityNameEqualToColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("join_mall_best_activity_name = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andJoinMallBestActivityNameNotEqualTo(String value) {
            addCriterion("join_mall_best_activity_name <>", value, "joinMallBestActivityName");
            return (Criteria) this;
        }

        public Criteria andJoinMallBestActivityNameNotEqualToColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("join_mall_best_activity_name <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andJoinMallBestActivityNameGreaterThan(String value) {
            addCriterion("join_mall_best_activity_name >", value, "joinMallBestActivityName");
            return (Criteria) this;
        }

        public Criteria andJoinMallBestActivityNameGreaterThanColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("join_mall_best_activity_name > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andJoinMallBestActivityNameGreaterThanOrEqualTo(String value) {
            addCriterion("join_mall_best_activity_name >=", value, "joinMallBestActivityName");
            return (Criteria) this;
        }

        public Criteria andJoinMallBestActivityNameGreaterThanOrEqualToColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("join_mall_best_activity_name >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andJoinMallBestActivityNameLessThan(String value) {
            addCriterion("join_mall_best_activity_name <", value, "joinMallBestActivityName");
            return (Criteria) this;
        }

        public Criteria andJoinMallBestActivityNameLessThanColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("join_mall_best_activity_name < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andJoinMallBestActivityNameLessThanOrEqualTo(String value) {
            addCriterion("join_mall_best_activity_name <=", value, "joinMallBestActivityName");
            return (Criteria) this;
        }

        public Criteria andJoinMallBestActivityNameLessThanOrEqualToColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("join_mall_best_activity_name <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andJoinMallBestActivityNameLike(String value) {
            addCriterion("join_mall_best_activity_name like", value, "joinMallBestActivityName");
            return (Criteria) this;
        }

        public Criteria andJoinMallBestActivityNameNotLike(String value) {
            addCriterion("join_mall_best_activity_name not like", value, "joinMallBestActivityName");
            return (Criteria) this;
        }

        public Criteria andJoinMallBestActivityNameIn(List<String> values) {
            addCriterion("join_mall_best_activity_name in", values, "joinMallBestActivityName");
            return (Criteria) this;
        }

        public Criteria andJoinMallBestActivityNameNotIn(List<String> values) {
            addCriterion("join_mall_best_activity_name not in", values, "joinMallBestActivityName");
            return (Criteria) this;
        }

        public Criteria andJoinMallBestActivityNameBetween(String value1, String value2) {
            addCriterion("join_mall_best_activity_name between", value1, value2, "joinMallBestActivityName");
            return (Criteria) this;
        }

        public Criteria andJoinMallBestActivityNameNotBetween(String value1, String value2) {
            addCriterion("join_mall_best_activity_name not between", value1, value2, "joinMallBestActivityName");
            return (Criteria) this;
        }

        public Criteria andJoinMallBestActivityRankingIsNull() {
            addCriterion("join_mall_best_activity_ranking is null");
            return (Criteria) this;
        }

        public Criteria andJoinMallBestActivityRankingIsNotNull() {
            addCriterion("join_mall_best_activity_ranking is not null");
            return (Criteria) this;
        }

        public Criteria andJoinMallBestActivityRankingEqualTo(Long value) {
            addCriterion("join_mall_best_activity_ranking =", value, "joinMallBestActivityRanking");
            return (Criteria) this;
        }

        public Criteria andJoinMallBestActivityRankingEqualToColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("join_mall_best_activity_ranking = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andJoinMallBestActivityRankingNotEqualTo(Long value) {
            addCriterion("join_mall_best_activity_ranking <>", value, "joinMallBestActivityRanking");
            return (Criteria) this;
        }

        public Criteria andJoinMallBestActivityRankingNotEqualToColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("join_mall_best_activity_ranking <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andJoinMallBestActivityRankingGreaterThan(Long value) {
            addCriterion("join_mall_best_activity_ranking >", value, "joinMallBestActivityRanking");
            return (Criteria) this;
        }

        public Criteria andJoinMallBestActivityRankingGreaterThanColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("join_mall_best_activity_ranking > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andJoinMallBestActivityRankingGreaterThanOrEqualTo(Long value) {
            addCriterion("join_mall_best_activity_ranking >=", value, "joinMallBestActivityRanking");
            return (Criteria) this;
        }

        public Criteria andJoinMallBestActivityRankingGreaterThanOrEqualToColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("join_mall_best_activity_ranking >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andJoinMallBestActivityRankingLessThan(Long value) {
            addCriterion("join_mall_best_activity_ranking <", value, "joinMallBestActivityRanking");
            return (Criteria) this;
        }

        public Criteria andJoinMallBestActivityRankingLessThanColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("join_mall_best_activity_ranking < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andJoinMallBestActivityRankingLessThanOrEqualTo(Long value) {
            addCriterion("join_mall_best_activity_ranking <=", value, "joinMallBestActivityRanking");
            return (Criteria) this;
        }

        public Criteria andJoinMallBestActivityRankingLessThanOrEqualToColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("join_mall_best_activity_ranking <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andJoinMallBestActivityRankingIn(List<Long> values) {
            addCriterion("join_mall_best_activity_ranking in", values, "joinMallBestActivityRanking");
            return (Criteria) this;
        }

        public Criteria andJoinMallBestActivityRankingNotIn(List<Long> values) {
            addCriterion("join_mall_best_activity_ranking not in", values, "joinMallBestActivityRanking");
            return (Criteria) this;
        }

        public Criteria andJoinMallBestActivityRankingBetween(Long value1, Long value2) {
            addCriterion("join_mall_best_activity_ranking between", value1, value2, "joinMallBestActivityRanking");
            return (Criteria) this;
        }

        public Criteria andJoinMallBestActivityRankingNotBetween(Long value1, Long value2) {
            addCriterion("join_mall_best_activity_ranking not between", value1, value2, "joinMallBestActivityRanking");
            return (Criteria) this;
        }

        public Criteria andCustomerCountIsNull() {
            addCriterion("customer_count is null");
            return (Criteria) this;
        }

        public Criteria andCustomerCountIsNotNull() {
            addCriterion("customer_count is not null");
            return (Criteria) this;
        }

        public Criteria andCustomerCountEqualTo(Long value) {
            addCriterion("customer_count =", value, "customerCount");
            return (Criteria) this;
        }

        public Criteria andCustomerCountEqualToColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("customer_count = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCustomerCountNotEqualTo(Long value) {
            addCriterion("customer_count <>", value, "customerCount");
            return (Criteria) this;
        }

        public Criteria andCustomerCountNotEqualToColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("customer_count <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCustomerCountGreaterThan(Long value) {
            addCriterion("customer_count >", value, "customerCount");
            return (Criteria) this;
        }

        public Criteria andCustomerCountGreaterThanColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("customer_count > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCustomerCountGreaterThanOrEqualTo(Long value) {
            addCriterion("customer_count >=", value, "customerCount");
            return (Criteria) this;
        }

        public Criteria andCustomerCountGreaterThanOrEqualToColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("customer_count >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCustomerCountLessThan(Long value) {
            addCriterion("customer_count <", value, "customerCount");
            return (Criteria) this;
        }

        public Criteria andCustomerCountLessThanColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("customer_count < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCustomerCountLessThanOrEqualTo(Long value) {
            addCriterion("customer_count <=", value, "customerCount");
            return (Criteria) this;
        }

        public Criteria andCustomerCountLessThanOrEqualToColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("customer_count <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCustomerCountIn(List<Long> values) {
            addCriterion("customer_count in", values, "customerCount");
            return (Criteria) this;
        }

        public Criteria andCustomerCountNotIn(List<Long> values) {
            addCriterion("customer_count not in", values, "customerCount");
            return (Criteria) this;
        }

        public Criteria andCustomerCountBetween(Long value1, Long value2) {
            addCriterion("customer_count between", value1, value2, "customerCount");
            return (Criteria) this;
        }

        public Criteria andCustomerCountNotBetween(Long value1, Long value2) {
            addCriterion("customer_count not between", value1, value2, "customerCount");
            return (Criteria) this;
        }

        public Criteria andCustomerCountGroupIsNull() {
            addCriterion("customer_count_group is null");
            return (Criteria) this;
        }

        public Criteria andCustomerCountGroupIsNotNull() {
            addCriterion("customer_count_group is not null");
            return (Criteria) this;
        }

        public Criteria andCustomerCountGroupEqualTo(Long value) {
            addCriterion("customer_count_group =", value, "customerCountGroup");
            return (Criteria) this;
        }

        public Criteria andCustomerCountGroupEqualToColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("customer_count_group = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCustomerCountGroupNotEqualTo(Long value) {
            addCriterion("customer_count_group <>", value, "customerCountGroup");
            return (Criteria) this;
        }

        public Criteria andCustomerCountGroupNotEqualToColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("customer_count_group <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCustomerCountGroupGreaterThan(Long value) {
            addCriterion("customer_count_group >", value, "customerCountGroup");
            return (Criteria) this;
        }

        public Criteria andCustomerCountGroupGreaterThanColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("customer_count_group > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCustomerCountGroupGreaterThanOrEqualTo(Long value) {
            addCriterion("customer_count_group >=", value, "customerCountGroup");
            return (Criteria) this;
        }

        public Criteria andCustomerCountGroupGreaterThanOrEqualToColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("customer_count_group >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCustomerCountGroupLessThan(Long value) {
            addCriterion("customer_count_group <", value, "customerCountGroup");
            return (Criteria) this;
        }

        public Criteria andCustomerCountGroupLessThanColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("customer_count_group < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCustomerCountGroupLessThanOrEqualTo(Long value) {
            addCriterion("customer_count_group <=", value, "customerCountGroup");
            return (Criteria) this;
        }

        public Criteria andCustomerCountGroupLessThanOrEqualToColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("customer_count_group <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCustomerCountGroupIn(List<Long> values) {
            addCriterion("customer_count_group in", values, "customerCountGroup");
            return (Criteria) this;
        }

        public Criteria andCustomerCountGroupNotIn(List<Long> values) {
            addCriterion("customer_count_group not in", values, "customerCountGroup");
            return (Criteria) this;
        }

        public Criteria andCustomerCountGroupBetween(Long value1, Long value2) {
            addCriterion("customer_count_group between", value1, value2, "customerCountGroup");
            return (Criteria) this;
        }

        public Criteria andCustomerCountGroupNotBetween(Long value1, Long value2) {
            addCriterion("customer_count_group not between", value1, value2, "customerCountGroup");
            return (Criteria) this;
        }

        public Criteria andCustomerCountIndividualIsNull() {
            addCriterion("customer_count_individual is null");
            return (Criteria) this;
        }

        public Criteria andCustomerCountIndividualIsNotNull() {
            addCriterion("customer_count_individual is not null");
            return (Criteria) this;
        }

        public Criteria andCustomerCountIndividualEqualTo(Long value) {
            addCriterion("customer_count_individual =", value, "customerCountIndividual");
            return (Criteria) this;
        }

        public Criteria andCustomerCountIndividualEqualToColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("customer_count_individual = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCustomerCountIndividualNotEqualTo(Long value) {
            addCriterion("customer_count_individual <>", value, "customerCountIndividual");
            return (Criteria) this;
        }

        public Criteria andCustomerCountIndividualNotEqualToColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("customer_count_individual <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCustomerCountIndividualGreaterThan(Long value) {
            addCriterion("customer_count_individual >", value, "customerCountIndividual");
            return (Criteria) this;
        }

        public Criteria andCustomerCountIndividualGreaterThanColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("customer_count_individual > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCustomerCountIndividualGreaterThanOrEqualTo(Long value) {
            addCriterion("customer_count_individual >=", value, "customerCountIndividual");
            return (Criteria) this;
        }

        public Criteria andCustomerCountIndividualGreaterThanOrEqualToColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("customer_count_individual >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCustomerCountIndividualLessThan(Long value) {
            addCriterion("customer_count_individual <", value, "customerCountIndividual");
            return (Criteria) this;
        }

        public Criteria andCustomerCountIndividualLessThanColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("customer_count_individual < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCustomerCountIndividualLessThanOrEqualTo(Long value) {
            addCriterion("customer_count_individual <=", value, "customerCountIndividual");
            return (Criteria) this;
        }

        public Criteria andCustomerCountIndividualLessThanOrEqualToColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("customer_count_individual <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCustomerCountIndividualIn(List<Long> values) {
            addCriterion("customer_count_individual in", values, "customerCountIndividual");
            return (Criteria) this;
        }

        public Criteria andCustomerCountIndividualNotIn(List<Long> values) {
            addCriterion("customer_count_individual not in", values, "customerCountIndividual");
            return (Criteria) this;
        }

        public Criteria andCustomerCountIndividualBetween(Long value1, Long value2) {
            addCriterion("customer_count_individual between", value1, value2, "customerCountIndividual");
            return (Criteria) this;
        }

        public Criteria andCustomerCountIndividualNotBetween(Long value1, Long value2) {
            addCriterion("customer_count_individual not between", value1, value2, "customerCountIndividual");
            return (Criteria) this;
        }

        public Criteria andCustomerTotalCountIsNull() {
            addCriterion("customer_total_count is null");
            return (Criteria) this;
        }

        public Criteria andCustomerTotalCountIsNotNull() {
            addCriterion("customer_total_count is not null");
            return (Criteria) this;
        }

        public Criteria andCustomerTotalCountEqualTo(Long value) {
            addCriterion("customer_total_count =", value, "customerTotalCount");
            return (Criteria) this;
        }

        public Criteria andCustomerTotalCountEqualToColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("customer_total_count = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCustomerTotalCountNotEqualTo(Long value) {
            addCriterion("customer_total_count <>", value, "customerTotalCount");
            return (Criteria) this;
        }

        public Criteria andCustomerTotalCountNotEqualToColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("customer_total_count <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCustomerTotalCountGreaterThan(Long value) {
            addCriterion("customer_total_count >", value, "customerTotalCount");
            return (Criteria) this;
        }

        public Criteria andCustomerTotalCountGreaterThanColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("customer_total_count > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCustomerTotalCountGreaterThanOrEqualTo(Long value) {
            addCriterion("customer_total_count >=", value, "customerTotalCount");
            return (Criteria) this;
        }

        public Criteria andCustomerTotalCountGreaterThanOrEqualToColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("customer_total_count >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCustomerTotalCountLessThan(Long value) {
            addCriterion("customer_total_count <", value, "customerTotalCount");
            return (Criteria) this;
        }

        public Criteria andCustomerTotalCountLessThanColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("customer_total_count < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCustomerTotalCountLessThanOrEqualTo(Long value) {
            addCriterion("customer_total_count <=", value, "customerTotalCount");
            return (Criteria) this;
        }

        public Criteria andCustomerTotalCountLessThanOrEqualToColumn(MiniProgramSaleYearReport.Column column) {
            addCriterion(new StringBuilder("customer_total_count <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCustomerTotalCountIn(List<Long> values) {
            addCriterion("customer_total_count in", values, "customerTotalCount");
            return (Criteria) this;
        }

        public Criteria andCustomerTotalCountNotIn(List<Long> values) {
            addCriterion("customer_total_count not in", values, "customerTotalCount");
            return (Criteria) this;
        }

        public Criteria andCustomerTotalCountBetween(Long value1, Long value2) {
            addCriterion("customer_total_count between", value1, value2, "customerTotalCount");
            return (Criteria) this;
        }

        public Criteria andCustomerTotalCountNotBetween(Long value1, Long value2) {
            addCriterion("customer_total_count not between", value1, value2, "customerTotalCount");
            return (Criteria) this;
        }

        public Criteria andIdLikeInsensitive(String value) {
            addCriterion("upper(id) like", value.toUpperCase(), "id");
            return (Criteria) this;
        }

        public Criteria andUserIdLikeInsensitive(String value) {
            addCriterion("upper(user_id) like", value.toUpperCase(), "userId");
            return (Criteria) this;
        }

        public Criteria andNameLikeInsensitive(String value) {
            addCriterion("upper(`name`) like", value.toUpperCase(), "name");
            return (Criteria) this;
        }

        public Criteria andRoleTypeLikeInsensitive(String value) {
            addCriterion("upper(role_type) like", value.toUpperCase(), "roleType");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeLikeInsensitive(String value) {
            addCriterion("upper(province_code) like", value.toUpperCase(), "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceNameLikeInsensitive(String value) {
            addCriterion("upper(province_name) like", value.toUpperCase(), "provinceName");
            return (Criteria) this;
        }

        public Criteria andCityCodeLikeInsensitive(String value) {
            addCriterion("upper(city_code) like", value.toUpperCase(), "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityNameLikeInsensitive(String value) {
            addCriterion("upper(city_name) like", value.toUpperCase(), "cityName");
            return (Criteria) this;
        }

        public Criteria andFirstOrderSpuCodeLikeInsensitive(String value) {
            addCriterion("upper(first_order_spu_code) like", value.toUpperCase(), "firstOrderSpuCode");
            return (Criteria) this;
        }

        public Criteria andFirstOrderSpuNameLikeInsensitive(String value) {
            addCriterion("upper(first_order_spu_name) like", value.toUpperCase(), "firstOrderSpuName");
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuCodeLikeInsensitive(String value) {
            addCriterion("upper(best_sale_spu_code) like", value.toUpperCase(), "bestSaleSpuCode");
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuNameLikeInsensitive(String value) {
            addCriterion("upper(best_sale_spu_name) like", value.toUpperCase(), "bestSaleSpuName");
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuCodeGroupLikeInsensitive(String value) {
            addCriterion("upper(best_sale_spu_code_group) like", value.toUpperCase(), "bestSaleSpuCodeGroup");
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuNameGroupLikeInsensitive(String value) {
            addCriterion("upper(best_sale_spu_name_group) like", value.toUpperCase(), "bestSaleSpuNameGroup");
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuCodeIndividualLikeInsensitive(String value) {
            addCriterion("upper(best_sale_spu_code_individual) like", value.toUpperCase(), "bestSaleSpuCodeIndividual");
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuNameIndividualLikeInsensitive(String value) {
            addCriterion("upper(best_sale_spu_name_individual) like", value.toUpperCase(), "bestSaleSpuNameIndividual");
            return (Criteria) this;
        }

        public Criteria andBestSaleSpuPromptLikeInsensitive(String value) {
            addCriterion("upper(best_sale_spu_prompt) like", value.toUpperCase(), "bestSaleSpuPrompt");
            return (Criteria) this;
        }

        public Criteria andJoinMallBestActivityNameLikeInsensitive(String value) {
            addCriterion("upper(join_mall_best_activity_name) like", value.toUpperCase(), "joinMallBestActivityName");
            return (Criteria) this;
        }
    }

    /**
     *
     * @mbg.generated do_not_delete_during_merge Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    public static class Criteria extends GeneratedCriteria {
        /**
         *
         * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
         */
        private MiniProgramSaleYearReportExample example;

        /**
         *
         * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
         */
        protected Criteria(MiniProgramSaleYearReportExample example) {
            super();
            this.example = example;
        }

        /**
         *
         * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
         */
        public MiniProgramSaleYearReportExample example() {
            return this.example;
        }

        /**
         *
         * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
         */
        @Deprecated
        public Criteria andIf(boolean ifAdd, ICriteriaAdd add) {
            if (ifAdd) {
                add.add(this);
            }
            return this;
        }

        /**
         *
         * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
         */
        public Criteria when(boolean condition, ICriteriaWhen then) {
            if (condition) {
                then.criteria(this);
            }
            return this;
        }

        /**
         *
         * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
         */
        public Criteria when(boolean condition, ICriteriaWhen then, ICriteriaWhen otherwise) {
            if (condition) {
                then.criteria(this);
            } else {
                otherwise.criteria(this);
            }
            return this;
        }

        @Deprecated
        public interface ICriteriaAdd {
            /**
             *
             * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
             */
            Criteria add(Criteria add);
        }
    }

    /**
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }

    public interface ICriteriaWhen {
        /**
         *
         * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
         */
        void criteria(Criteria criteria);
    }

    public interface IExampleWhen {
        /**
         *
         * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
         */
        void example(com.chinamobile.retail.pojo.entity.MiniProgramSaleYearReportExample example);
    }
}