package com.chinamobile.retail.pojo.entity;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;

/**
 * Created By MBG-GUI-EXTENSION https://github.com/spawpaw/mybatis-generator-gui-extension
 * Description:
 * 商品组/销售商品
 *
 * <AUTHOR>
public class SpuOfferingInfo implements Serializable {
    /**
     * 主键ID
     *
     *
     * @mbg.generated Tue Feb 11 14:51:23 CST 2025
     */
    private String id;

    /**
     * 产品经理唯一标识
     *
     *
     * @mbg.generated Tue Feb 11 14:51:23 CST 2025
     */
    private String operId;

    /**
     * 商品组/销售商品编码
     *
     *
     * @mbg.generated Tue Feb 11 14:51:23 CST 2025
     */
    private String offeringCode;

    /**
     * 商品组/销售商品名称
     *
     *
     * @mbg.generated Tue Feb 11 14:51:23 CST 2025
     */
    private String offeringName;

    /**
     * 商品状态
0：测试
1：发布;
测试状态，表示仅用于咨询，未上架销售的商品。
发布状态，表示已上架，可用于销售的商品。
发布状态暂不启用。
     *
     *
     * @mbg.generated Tue Feb 11 14:51:23 CST 2025
     */
    private String offeringStatus;

    /**
     * 操作类型
A:新增
     *
     *
     * @mbg.generated Tue Feb 11 14:51:23 CST 2025
     */
    private String operType;

    /**
     * 销售对象
G:集团客户
P:个人客户
     *
     *
     * @mbg.generated Tue Feb 11 14:51:23 CST 2025
     */
    private String saleObject;

    /**
     * 商品详情页链接,listPlatform=0时取系统默认生成的物联网商城SPU商品详情页链接https://mall.iot.10086.cn/mall/#/resource/offering/%OFFERINGCODE%?jump=y，OFFERINGCODE为销售商品code,listPlatform=1时取系统默认生成的视联网商城SPU商品详情页链接https://mall.iot.10086.cn/viot/#/resource/offering/%OFFERINGCODE%?jump=y，OFFERINGCODE为销售商品code
     *
     *
     * @mbg.generated Tue Feb 11 14:51:23 CST 2025
     */
    private String url;

    /**
     * 商品封面图url
     *
     *
     * @mbg.generated Tue Feb 11 14:51:23 CST 2025
     */
    private String imgUrl;

    /**
     * 商品标签
     *
     *
     * @mbg.generated Tue Feb 11 14:51:23 CST 2025
     */
    private String tag;

    /**
     * 创建时间
     *
     *
     * @mbg.generated Tue Feb 11 14:51:23 CST 2025
     */
    private Date createTime;

    /**
     * 更新时间
     *
     *
     * @mbg.generated Tue Feb 11 14:51:23 CST 2025
     */
    private Date updateTime;

    /**
     * 删除时间
     *
     *
     * @mbg.generated Tue Feb 11 14:51:23 CST 2025
     */
    private Date deleteTime;

    /**
     * 上架平台 0：物联网商城 1：视联网商城
     *
     *
     * @mbg.generated Tue Feb 11 14:51:23 CST 2025
     */
    private String listPlatform;

    /**
     * 库存模式 0:运营统管 1:非运管统筹
     *
     *
     * @mbg.generated Tue Feb 11 14:51:23 CST 2025
     */
    private String inventoryType;

    /**
     * DICT范式产品线条 “00：和对讲”,“01：云视讯”
     *
     *
     * @mbg.generated Tue Feb 11 14:51:23 CST 2025
     */
    private String dictProductlines;

    /**
     * 商品关键字,如有多组以“,”分隔进行传值
     *
     *
     * @mbg.generated Tue Feb 11 14:51:23 CST 2025
     */
    private String productKeywords;

    /**
     * 是否隐秘上架,0：是
     * 1：否
     *
     *
     * @mbg.generated Tue Feb 11 14:51:23 CST 2025
     */
    private String secretlyListed;

    /**
     * 产品简介
     *
     *
     * @mbg.generated Tue Feb 11 14:51:23 CST 2025
     */
    private String productDescription;

    /**
     *
     * @mbg.generated Tue Feb 11 14:51:23 CST 2025
     */
    private static final long serialVersionUID = 1L;

    /**
     * This method returns the value of the database column supply_chain..spu_offering_info.id
     *
     * @return the value of supply_chain..spu_offering_info.id
     *
     * @mbg.generated Tue Feb 11 14:51:23 CST 2025
     */
    public String getId() {
        return id;
    }

    /**
     *
     * @mbg.generated Tue Feb 11 14:51:23 CST 2025
     */
    public SpuOfferingInfo withId(String id) {
        this.setId(id);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..spu_offering_info.id
     *
     * @param id the value for supply_chain..spu_offering_info.id
     *
     * @mbg.generated Tue Feb 11 14:51:23 CST 2025
     */
    public void setId(String id) {
        this.id = id;
    }

    /**
     * This method returns the value of the database column supply_chain..spu_offering_info.oper_id
     *
     * @return the value of supply_chain..spu_offering_info.oper_id
     *
     * @mbg.generated Tue Feb 11 14:51:23 CST 2025
     */
    public String getOperId() {
        return operId;
    }

    /**
     *
     * @mbg.generated Tue Feb 11 14:51:23 CST 2025
     */
    public SpuOfferingInfo withOperId(String operId) {
        this.setOperId(operId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..spu_offering_info.oper_id
     *
     * @param operId the value for supply_chain..spu_offering_info.oper_id
     *
     * @mbg.generated Tue Feb 11 14:51:23 CST 2025
     */
    public void setOperId(String operId) {
        this.operId = operId;
    }

    /**
     * This method returns the value of the database column supply_chain..spu_offering_info.offering_code
     *
     * @return the value of supply_chain..spu_offering_info.offering_code
     *
     * @mbg.generated Tue Feb 11 14:51:23 CST 2025
     */
    public String getOfferingCode() {
        return offeringCode;
    }

    /**
     *
     * @mbg.generated Tue Feb 11 14:51:23 CST 2025
     */
    public SpuOfferingInfo withOfferingCode(String offeringCode) {
        this.setOfferingCode(offeringCode);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..spu_offering_info.offering_code
     *
     * @param offeringCode the value for supply_chain..spu_offering_info.offering_code
     *
     * @mbg.generated Tue Feb 11 14:51:23 CST 2025
     */
    public void setOfferingCode(String offeringCode) {
        this.offeringCode = offeringCode;
    }

    /**
     * This method returns the value of the database column supply_chain..spu_offering_info.offering_name
     *
     * @return the value of supply_chain..spu_offering_info.offering_name
     *
     * @mbg.generated Tue Feb 11 14:51:23 CST 2025
     */
    public String getOfferingName() {
        return offeringName;
    }

    /**
     *
     * @mbg.generated Tue Feb 11 14:51:23 CST 2025
     */
    public SpuOfferingInfo withOfferingName(String offeringName) {
        this.setOfferingName(offeringName);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..spu_offering_info.offering_name
     *
     * @param offeringName the value for supply_chain..spu_offering_info.offering_name
     *
     * @mbg.generated Tue Feb 11 14:51:23 CST 2025
     */
    public void setOfferingName(String offeringName) {
        this.offeringName = offeringName;
    }

    /**
     * This method returns the value of the database column supply_chain..spu_offering_info.offering_status
     *
     * @return the value of supply_chain..spu_offering_info.offering_status
     *
     * @mbg.generated Tue Feb 11 14:51:23 CST 2025
     */
    public String getOfferingStatus() {
        return offeringStatus;
    }

    /**
     *
     * @mbg.generated Tue Feb 11 14:51:23 CST 2025
     */
    public SpuOfferingInfo withOfferingStatus(String offeringStatus) {
        this.setOfferingStatus(offeringStatus);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..spu_offering_info.offering_status
     *
     * @param offeringStatus the value for supply_chain..spu_offering_info.offering_status
     *
     * @mbg.generated Tue Feb 11 14:51:23 CST 2025
     */
    public void setOfferingStatus(String offeringStatus) {
        this.offeringStatus = offeringStatus;
    }

    /**
     * This method returns the value of the database column supply_chain..spu_offering_info.oper_type
     *
     * @return the value of supply_chain..spu_offering_info.oper_type
     *
     * @mbg.generated Tue Feb 11 14:51:23 CST 2025
     */
    public String getOperType() {
        return operType;
    }

    /**
     *
     * @mbg.generated Tue Feb 11 14:51:23 CST 2025
     */
    public SpuOfferingInfo withOperType(String operType) {
        this.setOperType(operType);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..spu_offering_info.oper_type
     *
     * @param operType the value for supply_chain..spu_offering_info.oper_type
     *
     * @mbg.generated Tue Feb 11 14:51:23 CST 2025
     */
    public void setOperType(String operType) {
        this.operType = operType;
    }

    /**
     * This method returns the value of the database column supply_chain..spu_offering_info.sale_object
     *
     * @return the value of supply_chain..spu_offering_info.sale_object
     *
     * @mbg.generated Tue Feb 11 14:51:23 CST 2025
     */
    public String getSaleObject() {
        return saleObject;
    }

    /**
     *
     * @mbg.generated Tue Feb 11 14:51:23 CST 2025
     */
    public SpuOfferingInfo withSaleObject(String saleObject) {
        this.setSaleObject(saleObject);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..spu_offering_info.sale_object
     *
     * @param saleObject the value for supply_chain..spu_offering_info.sale_object
     *
     * @mbg.generated Tue Feb 11 14:51:23 CST 2025
     */
    public void setSaleObject(String saleObject) {
        this.saleObject = saleObject;
    }

    /**
     * This method returns the value of the database column supply_chain..spu_offering_info.url
     *
     * @return the value of supply_chain..spu_offering_info.url
     *
     * @mbg.generated Tue Feb 11 14:51:23 CST 2025
     */
    public String getUrl() {
        return url;
    }

    /**
     *
     * @mbg.generated Tue Feb 11 14:51:23 CST 2025
     */
    public SpuOfferingInfo withUrl(String url) {
        this.setUrl(url);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..spu_offering_info.url
     *
     * @param url the value for supply_chain..spu_offering_info.url
     *
     * @mbg.generated Tue Feb 11 14:51:23 CST 2025
     */
    public void setUrl(String url) {
        this.url = url;
    }

    /**
     * This method returns the value of the database column supply_chain..spu_offering_info.img_url
     *
     * @return the value of supply_chain..spu_offering_info.img_url
     *
     * @mbg.generated Tue Feb 11 14:51:23 CST 2025
     */
    public String getImgUrl() {
        return imgUrl;
    }

    /**
     *
     * @mbg.generated Tue Feb 11 14:51:23 CST 2025
     */
    public SpuOfferingInfo withImgUrl(String imgUrl) {
        this.setImgUrl(imgUrl);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..spu_offering_info.img_url
     *
     * @param imgUrl the value for supply_chain..spu_offering_info.img_url
     *
     * @mbg.generated Tue Feb 11 14:51:23 CST 2025
     */
    public void setImgUrl(String imgUrl) {
        this.imgUrl = imgUrl;
    }

    /**
     * This method returns the value of the database column supply_chain..spu_offering_info.tag
     *
     * @return the value of supply_chain..spu_offering_info.tag
     *
     * @mbg.generated Tue Feb 11 14:51:23 CST 2025
     */
    public String getTag() {
        return tag;
    }

    /**
     *
     * @mbg.generated Tue Feb 11 14:51:23 CST 2025
     */
    public SpuOfferingInfo withTag(String tag) {
        this.setTag(tag);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..spu_offering_info.tag
     *
     * @param tag the value for supply_chain..spu_offering_info.tag
     *
     * @mbg.generated Tue Feb 11 14:51:23 CST 2025
     */
    public void setTag(String tag) {
        this.tag = tag;
    }

    /**
     * This method returns the value of the database column supply_chain..spu_offering_info.create_time
     *
     * @return the value of supply_chain..spu_offering_info.create_time
     *
     * @mbg.generated Tue Feb 11 14:51:23 CST 2025
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     *
     * @mbg.generated Tue Feb 11 14:51:23 CST 2025
     */
    public SpuOfferingInfo withCreateTime(Date createTime) {
        this.setCreateTime(createTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..spu_offering_info.create_time
     *
     * @param createTime the value for supply_chain..spu_offering_info.create_time
     *
     * @mbg.generated Tue Feb 11 14:51:23 CST 2025
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * This method returns the value of the database column supply_chain..spu_offering_info.update_time
     *
     * @return the value of supply_chain..spu_offering_info.update_time
     *
     * @mbg.generated Tue Feb 11 14:51:23 CST 2025
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     *
     * @mbg.generated Tue Feb 11 14:51:23 CST 2025
     */
    public SpuOfferingInfo withUpdateTime(Date updateTime) {
        this.setUpdateTime(updateTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..spu_offering_info.update_time
     *
     * @param updateTime the value for supply_chain..spu_offering_info.update_time
     *
     * @mbg.generated Tue Feb 11 14:51:23 CST 2025
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * This method returns the value of the database column supply_chain..spu_offering_info.delete_time
     *
     * @return the value of supply_chain..spu_offering_info.delete_time
     *
     * @mbg.generated Tue Feb 11 14:51:23 CST 2025
     */
    public Date getDeleteTime() {
        return deleteTime;
    }

    /**
     *
     * @mbg.generated Tue Feb 11 14:51:23 CST 2025
     */
    public SpuOfferingInfo withDeleteTime(Date deleteTime) {
        this.setDeleteTime(deleteTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..spu_offering_info.delete_time
     *
     * @param deleteTime the value for supply_chain..spu_offering_info.delete_time
     *
     * @mbg.generated Tue Feb 11 14:51:23 CST 2025
     */
    public void setDeleteTime(Date deleteTime) {
        this.deleteTime = deleteTime;
    }

    /**
     * This method returns the value of the database column supply_chain..spu_offering_info.list_platform
     *
     * @return the value of supply_chain..spu_offering_info.list_platform
     *
     * @mbg.generated Tue Feb 11 14:51:23 CST 2025
     */
    public String getListPlatform() {
        return listPlatform;
    }

    /**
     *
     * @mbg.generated Tue Feb 11 14:51:23 CST 2025
     */
    public SpuOfferingInfo withListPlatform(String listPlatform) {
        this.setListPlatform(listPlatform);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..spu_offering_info.list_platform
     *
     * @param listPlatform the value for supply_chain..spu_offering_info.list_platform
     *
     * @mbg.generated Tue Feb 11 14:51:23 CST 2025
     */
    public void setListPlatform(String listPlatform) {
        this.listPlatform = listPlatform;
    }

    /**
     * This method returns the value of the database column supply_chain..spu_offering_info.inventory_type
     *
     * @return the value of supply_chain..spu_offering_info.inventory_type
     *
     * @mbg.generated Tue Feb 11 14:51:23 CST 2025
     */
    public String getInventoryType() {
        return inventoryType;
    }

    /**
     *
     * @mbg.generated Tue Feb 11 14:51:23 CST 2025
     */
    public SpuOfferingInfo withInventoryType(String inventoryType) {
        this.setInventoryType(inventoryType);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..spu_offering_info.inventory_type
     *
     * @param inventoryType the value for supply_chain..spu_offering_info.inventory_type
     *
     * @mbg.generated Tue Feb 11 14:51:23 CST 2025
     */
    public void setInventoryType(String inventoryType) {
        this.inventoryType = inventoryType;
    }

    /**
     * This method returns the value of the database column supply_chain..spu_offering_info.dict_productLines
     *
     * @return the value of supply_chain..spu_offering_info.dict_productLines
     *
     * @mbg.generated Tue Feb 11 14:51:23 CST 2025
     */
    public String getDictProductlines() {
        return dictProductlines;
    }

    /**
     *
     * @mbg.generated Tue Feb 11 14:51:23 CST 2025
     */
    public SpuOfferingInfo withDictProductlines(String dictProductlines) {
        this.setDictProductlines(dictProductlines);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..spu_offering_info.dict_productLines
     *
     * @param dictProductlines the value for supply_chain..spu_offering_info.dict_productLines
     *
     * @mbg.generated Tue Feb 11 14:51:23 CST 2025
     */
    public void setDictProductlines(String dictProductlines) {
        this.dictProductlines = dictProductlines;
    }

    /**
     * This method returns the value of the database column supply_chain..spu_offering_info.product_keywords
     *
     * @return the value of supply_chain..spu_offering_info.product_keywords
     *
     * @mbg.generated Tue Feb 11 14:51:23 CST 2025
     */
    public String getProductKeywords() {
        return productKeywords;
    }

    /**
     *
     * @mbg.generated Tue Feb 11 14:51:23 CST 2025
     */
    public SpuOfferingInfo withProductKeywords(String productKeywords) {
        this.setProductKeywords(productKeywords);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..spu_offering_info.product_keywords
     *
     * @param productKeywords the value for supply_chain..spu_offering_info.product_keywords
     *
     * @mbg.generated Tue Feb 11 14:51:23 CST 2025
     */
    public void setProductKeywords(String productKeywords) {
        this.productKeywords = productKeywords;
    }

    /**
     * This method returns the value of the database column supply_chain..spu_offering_info.secretly_listed
     *
     * @return the value of supply_chain..spu_offering_info.secretly_listed
     *
     * @mbg.generated Tue Feb 11 14:51:23 CST 2025
     */
    public String getSecretlyListed() {
        return secretlyListed;
    }

    /**
     *
     * @mbg.generated Tue Feb 11 14:51:23 CST 2025
     */
    public SpuOfferingInfo withSecretlyListed(String secretlyListed) {
        this.setSecretlyListed(secretlyListed);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..spu_offering_info.secretly_listed
     *
     * @param secretlyListed the value for supply_chain..spu_offering_info.secretly_listed
     *
     * @mbg.generated Tue Feb 11 14:51:23 CST 2025
     */
    public void setSecretlyListed(String secretlyListed) {
        this.secretlyListed = secretlyListed;
    }

    /**
     * This method returns the value of the database column supply_chain..spu_offering_info.product_description
     *
     * @return the value of supply_chain..spu_offering_info.product_description
     *
     * @mbg.generated Tue Feb 11 14:51:23 CST 2025
     */
    public String getProductDescription() {
        return productDescription;
    }

    /**
     *
     * @mbg.generated Tue Feb 11 14:51:23 CST 2025
     */
    public SpuOfferingInfo withProductDescription(String productDescription) {
        this.setProductDescription(productDescription);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..spu_offering_info.product_description
     *
     * @param productDescription the value for supply_chain..spu_offering_info.product_description
     *
     * @mbg.generated Tue Feb 11 14:51:23 CST 2025
     */
    public void setProductDescription(String productDescription) {
        this.productDescription = productDescription;
    }

    /**
     *
     * @mbg.generated Tue Feb 11 14:51:23 CST 2025
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", operId=").append(operId);
        sb.append(", offeringCode=").append(offeringCode);
        sb.append(", offeringName=").append(offeringName);
        sb.append(", offeringStatus=").append(offeringStatus);
        sb.append(", operType=").append(operType);
        sb.append(", saleObject=").append(saleObject);
        sb.append(", url=").append(url);
        sb.append(", imgUrl=").append(imgUrl);
        sb.append(", tag=").append(tag);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", deleteTime=").append(deleteTime);
        sb.append(", listPlatform=").append(listPlatform);
        sb.append(", inventoryType=").append(inventoryType);
        sb.append(", dictProductlines=").append(dictProductlines);
        sb.append(", productKeywords=").append(productKeywords);
        sb.append(", secretlyListed=").append(secretlyListed);
        sb.append(", productDescription=").append(productDescription);
        sb.append("]");
        return sb.toString();
    }

    /**
     *
     * @mbg.generated Tue Feb 11 14:51:23 CST 2025
     */
    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        SpuOfferingInfo other = (SpuOfferingInfo) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getOperId() == null ? other.getOperId() == null : this.getOperId().equals(other.getOperId()))
            && (this.getOfferingCode() == null ? other.getOfferingCode() == null : this.getOfferingCode().equals(other.getOfferingCode()))
            && (this.getOfferingName() == null ? other.getOfferingName() == null : this.getOfferingName().equals(other.getOfferingName()))
            && (this.getOfferingStatus() == null ? other.getOfferingStatus() == null : this.getOfferingStatus().equals(other.getOfferingStatus()))
            && (this.getOperType() == null ? other.getOperType() == null : this.getOperType().equals(other.getOperType()))
            && (this.getSaleObject() == null ? other.getSaleObject() == null : this.getSaleObject().equals(other.getSaleObject()))
            && (this.getUrl() == null ? other.getUrl() == null : this.getUrl().equals(other.getUrl()))
            && (this.getImgUrl() == null ? other.getImgUrl() == null : this.getImgUrl().equals(other.getImgUrl()))
            && (this.getTag() == null ? other.getTag() == null : this.getTag().equals(other.getTag()))
            && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()))
            && (this.getUpdateTime() == null ? other.getUpdateTime() == null : this.getUpdateTime().equals(other.getUpdateTime()))
            && (this.getDeleteTime() == null ? other.getDeleteTime() == null : this.getDeleteTime().equals(other.getDeleteTime()))
            && (this.getListPlatform() == null ? other.getListPlatform() == null : this.getListPlatform().equals(other.getListPlatform()))
            && (this.getInventoryType() == null ? other.getInventoryType() == null : this.getInventoryType().equals(other.getInventoryType()))
            && (this.getDictProductlines() == null ? other.getDictProductlines() == null : this.getDictProductlines().equals(other.getDictProductlines()))
            && (this.getProductKeywords() == null ? other.getProductKeywords() == null : this.getProductKeywords().equals(other.getProductKeywords()))
            && (this.getSecretlyListed() == null ? other.getSecretlyListed() == null : this.getSecretlyListed().equals(other.getSecretlyListed()))
            && (this.getProductDescription() == null ? other.getProductDescription() == null : this.getProductDescription().equals(other.getProductDescription()));
    }

    /**
     *
     * @mbg.generated Tue Feb 11 14:51:23 CST 2025
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getOperId() == null) ? 0 : getOperId().hashCode());
        result = prime * result + ((getOfferingCode() == null) ? 0 : getOfferingCode().hashCode());
        result = prime * result + ((getOfferingName() == null) ? 0 : getOfferingName().hashCode());
        result = prime * result + ((getOfferingStatus() == null) ? 0 : getOfferingStatus().hashCode());
        result = prime * result + ((getOperType() == null) ? 0 : getOperType().hashCode());
        result = prime * result + ((getSaleObject() == null) ? 0 : getSaleObject().hashCode());
        result = prime * result + ((getUrl() == null) ? 0 : getUrl().hashCode());
        result = prime * result + ((getImgUrl() == null) ? 0 : getImgUrl().hashCode());
        result = prime * result + ((getTag() == null) ? 0 : getTag().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        result = prime * result + ((getUpdateTime() == null) ? 0 : getUpdateTime().hashCode());
        result = prime * result + ((getDeleteTime() == null) ? 0 : getDeleteTime().hashCode());
        result = prime * result + ((getListPlatform() == null) ? 0 : getListPlatform().hashCode());
        result = prime * result + ((getInventoryType() == null) ? 0 : getInventoryType().hashCode());
        result = prime * result + ((getDictProductlines() == null) ? 0 : getDictProductlines().hashCode());
        result = prime * result + ((getProductKeywords() == null) ? 0 : getProductKeywords().hashCode());
        result = prime * result + ((getSecretlyListed() == null) ? 0 : getSecretlyListed().hashCode());
        result = prime * result + ((getProductDescription() == null) ? 0 : getProductDescription().hashCode());
        return result;
    }

    /**
     *
     * @mbg.generated Tue Feb 11 14:51:23 CST 2025
     */
    public enum Column {
        id("id", "id", "VARCHAR", false),
        operId("oper_id", "operId", "VARCHAR", false),
        offeringCode("offering_code", "offeringCode", "VARCHAR", false),
        offeringName("offering_name", "offeringName", "VARCHAR", false),
        offeringStatus("offering_status", "offeringStatus", "VARCHAR", false),
        operType("oper_type", "operType", "VARCHAR", false),
        saleObject("sale_object", "saleObject", "VARCHAR", false),
        url("url", "url", "VARCHAR", false),
        imgUrl("img_url", "imgUrl", "VARCHAR", false),
        tag("tag", "tag", "VARCHAR", false),
        createTime("create_time", "createTime", "TIMESTAMP", false),
        updateTime("update_time", "updateTime", "TIMESTAMP", false),
        deleteTime("delete_time", "deleteTime", "TIMESTAMP", false),
        listPlatform("list_platform", "listPlatform", "VARCHAR", false),
        inventoryType("inventory_type", "inventoryType", "VARCHAR", false),
        dictProductlines("dict_productLines", "dictProductlines", "VARCHAR", false),
        productKeywords("product_keywords", "productKeywords", "VARCHAR", false),
        secretlyListed("secretly_listed", "secretlyListed", "VARCHAR", false),
        productDescription("product_description", "productDescription", "VARCHAR", false);

        /**
         *
         * @mbg.generated Tue Feb 11 14:51:23 CST 2025
         */
        private static final String BEGINNING_DELIMITER = "\"";

        /**
         *
         * @mbg.generated Tue Feb 11 14:51:23 CST 2025
         */
        private static final String ENDING_DELIMITER = "\"";

        /**
         *
         * @mbg.generated Tue Feb 11 14:51:23 CST 2025
         */
        private final String column;

        /**
         *
         * @mbg.generated Tue Feb 11 14:51:23 CST 2025
         */
        private final boolean isColumnNameDelimited;

        /**
         *
         * @mbg.generated Tue Feb 11 14:51:23 CST 2025
         */
        private final String javaProperty;

        /**
         *
         * @mbg.generated Tue Feb 11 14:51:23 CST 2025
         */
        private final String jdbcType;

        /**
         *
         * @mbg.generated Tue Feb 11 14:51:23 CST 2025
         */
        public String value() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Tue Feb 11 14:51:23 CST 2025
         */
        public String getValue() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Tue Feb 11 14:51:23 CST 2025
         */
        public String getJavaProperty() {
            return this.javaProperty;
        }

        /**
         *
         * @mbg.generated Tue Feb 11 14:51:23 CST 2025
         */
        public String getJdbcType() {
            return this.jdbcType;
        }

        /**
         *
         * @mbg.generated Tue Feb 11 14:51:23 CST 2025
         */
        Column(String column, String javaProperty, String jdbcType, boolean isColumnNameDelimited) {
            this.column = column;
            this.javaProperty = javaProperty;
            this.jdbcType = jdbcType;
            this.isColumnNameDelimited = isColumnNameDelimited;
        }

        /**
         *
         * @mbg.generated Tue Feb 11 14:51:23 CST 2025
         */
        public String desc() {
            return this.getEscapedColumnName() + " DESC";
        }

        /**
         *
         * @mbg.generated Tue Feb 11 14:51:23 CST 2025
         */
        public String asc() {
            return this.getEscapedColumnName() + " ASC";
        }

        /**
         *
         * @mbg.generated Tue Feb 11 14:51:23 CST 2025
         */
        public static Column[] excludes(Column ... excludes) {
            ArrayList<Column> columns = new ArrayList<>(Arrays.asList(Column.values()));
            if (excludes != null && excludes.length > 0) {
                columns.removeAll(new ArrayList<>(Arrays.asList(excludes)));
            }
            return columns.toArray(new Column[]{});
        }

        /**
         *
         * @mbg.generated Tue Feb 11 14:51:23 CST 2025
         */
        public static Column[] all() {
            return Column.values();
        }

        /**
         *
         * @mbg.generated Tue Feb 11 14:51:23 CST 2025
         */
        public String getEscapedColumnName() {
            if (this.isColumnNameDelimited) {
                return new StringBuilder().append(BEGINNING_DELIMITER).append(this.column).append(ENDING_DELIMITER).toString();
            } else {
                return this.column;
            }
        }

        /**
         *
         * @mbg.generated Tue Feb 11 14:51:23 CST 2025
         */
        public String getAliasedEscapedColumnName() {
            return this.getEscapedColumnName();
        }
    }
}