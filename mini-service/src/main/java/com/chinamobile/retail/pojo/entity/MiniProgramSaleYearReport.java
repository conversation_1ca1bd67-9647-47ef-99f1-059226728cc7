package com.chinamobile.retail.pojo.entity;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;

/**
 * Created By MBG-GUI-EXTENSION https://github.com/spawpaw/mybatis-generator-gui-extension
 * Description:
 * 小程序销售年度报告
 *
 * <AUTHOR>
public class MiniProgramSaleYearReport implements Serializable {
    /**
     * 主键
     *
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    private String id;

    /**
     * 用户id
     *
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    private String userId;

    /**
     * 用户名称
     *
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    private String name;

    /**
     * 年份
     *
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    private Integer year;

    /**
     * 注册顺序（第多少位用户）
     *
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    private Long registerIndex;

    /**
     * 注册时间
     *
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    private Date registerTime;

    /**
     * 用户角色，0：普通用户  1：一级分销员  2：二级分销员 3:渠道商 4:客户经理
     *
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    private String roleType;

    /**
     * 省编码
     *
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    private String provinceCode;

    /**
     * 省名称
     *
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    private String provinceName;

    /**
     * 城市编码
     *
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    private String cityCode;

    /**
     * 城市名称
     *
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    private String cityName;

    /**
     * 第一次订单时间
     *
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    private Date firstOrderTime;

    /**
     * 年度最后一单时间
     *
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    private Date yearLastOrderTime;

    /**
     * 第一次订单商品编码
     *
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    private String firstOrderSpuCode;

    /**
     * 第一次订单商品名称
     *
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    private String firstOrderSpuName;

    /**
     * 年度销售订单金额
     *
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    private Long orderTotalPrice;

    /**
     * 年度销售订单笔数
     *
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    private Long orderTotalCount;

    /**
     * 年度预付费订单数量
     *
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    private Long orderTotalCountPrepay;

    /**
     * 后付费订单数量
     *
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    private Long orderTotalCountAfterpay;

    /**
     * 融合营销订单数量
     *
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    private Long orderTotalCountMix;

    /**
     * 年度获得推广积分
     *
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    private Long orderTotalPoint;

    /**
     * 年度推广商品款数
     *
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    private Long spuTotalCount;

    /**
     * 年度销售排名（超过X%）
     *
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    private Long saleRanking;

    /**
     * 最擅长销售商品spu编码
     *
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    private String bestSaleSpuCode;

    /**
     * 最擅长销售商品spu名称
     *
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    private String bestSaleSpuName;

    /**
     * 最擅长销售商品订单次数
     *
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    private Long bestSaleSpuOrderCount;

    /**
     * 集团客户购买的最多产品编码
     *
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    private String bestSaleSpuCodeGroup;

    /**
     * 集团客户购买的最多产品名称
     *
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    private String bestSaleSpuNameGroup;

    /**
     * 集团客户购买的最多产品次数
     *
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    private Long bestSaleSpuCountGroup;

    /**
     * 个人客户购买的最多产品编码
     *
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    private String bestSaleSpuCodeIndividual;

    /**
     * 个人客户购买的最多产品名称
     *
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    private String bestSaleSpuNameIndividual;

    /**
     * 个人客户购买的最多产品次数
     *
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    private Long bestSaleSpuCountIndividual;

    /**
     * 最擅长销售商品提示语
     *
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    private String bestSaleSpuPrompt;

    /**
     * 最晚一笔订单成交时间
     *
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    private Date yearLatestOrderTime;

    /**
     * 参与过商城活动次数
     *
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    private Integer joinMallActivityCount;

    /**
     * 活动中奖次数
     *
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    private Integer joinMallActivityRewardCount;

    /**
     * 最好成绩的活动名称
     *
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    private String joinMallBestActivityName;

    /**
     * 最好成绩的活动排名
     *
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    private Long joinMallBestActivityRanking;

    /**
     * 发展客户总数
     *
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    private Long customerCount;

    /**
     * 发展集团客户数
     *
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    private Long customerCountGroup;

    /**
     * 个人客户数
     *
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    private Long customerCountIndividual;

    /**
     * 发展客户总数
     *
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    private Long customerTotalCount;

    /**
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    private static final long serialVersionUID = 1L;

    /**
     * This method returns the value of the database column supply_chain..mini_program_sale_year_report.id
     *
     * @return the value of supply_chain..mini_program_sale_year_report.id
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    public String getId() {
        return id;
    }

    /**
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    public MiniProgramSaleYearReport withId(String id) {
        this.setId(id);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_sale_year_report.id
     *
     * @param id the value for supply_chain..mini_program_sale_year_report.id
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..mini_program_sale_year_report.user_id
     *
     * @return the value of supply_chain..mini_program_sale_year_report.user_id
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    public String getUserId() {
        return userId;
    }

    /**
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    public MiniProgramSaleYearReport withUserId(String userId) {
        this.setUserId(userId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_sale_year_report.user_id
     *
     * @param userId the value for supply_chain..mini_program_sale_year_report.user_id
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    public void setUserId(String userId) {
        this.userId = userId == null ? null : userId.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..mini_program_sale_year_report.name
     *
     * @return the value of supply_chain..mini_program_sale_year_report.name
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    public String getName() {
        return name;
    }

    /**
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    public MiniProgramSaleYearReport withName(String name) {
        this.setName(name);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_sale_year_report.name
     *
     * @param name the value for supply_chain..mini_program_sale_year_report.name
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    public void setName(String name) {
        this.name = name == null ? null : name.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..mini_program_sale_year_report.year
     *
     * @return the value of supply_chain..mini_program_sale_year_report.year
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    public Integer getYear() {
        return year;
    }

    /**
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    public MiniProgramSaleYearReport withYear(Integer year) {
        this.setYear(year);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_sale_year_report.year
     *
     * @param year the value for supply_chain..mini_program_sale_year_report.year
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    public void setYear(Integer year) {
        this.year = year;
    }

    /**
     * This method returns the value of the database column supply_chain..mini_program_sale_year_report.register_index
     *
     * @return the value of supply_chain..mini_program_sale_year_report.register_index
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    public Long getRegisterIndex() {
        return registerIndex;
    }

    /**
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    public MiniProgramSaleYearReport withRegisterIndex(Long registerIndex) {
        this.setRegisterIndex(registerIndex);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_sale_year_report.register_index
     *
     * @param registerIndex the value for supply_chain..mini_program_sale_year_report.register_index
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    public void setRegisterIndex(Long registerIndex) {
        this.registerIndex = registerIndex;
    }

    /**
     * This method returns the value of the database column supply_chain..mini_program_sale_year_report.register_time
     *
     * @return the value of supply_chain..mini_program_sale_year_report.register_time
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    public Date getRegisterTime() {
        return registerTime;
    }

    /**
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    public MiniProgramSaleYearReport withRegisterTime(Date registerTime) {
        this.setRegisterTime(registerTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_sale_year_report.register_time
     *
     * @param registerTime the value for supply_chain..mini_program_sale_year_report.register_time
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    public void setRegisterTime(Date registerTime) {
        this.registerTime = registerTime;
    }

    /**
     * This method returns the value of the database column supply_chain..mini_program_sale_year_report.role_type
     *
     * @return the value of supply_chain..mini_program_sale_year_report.role_type
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    public String getRoleType() {
        return roleType;
    }

    /**
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    public MiniProgramSaleYearReport withRoleType(String roleType) {
        this.setRoleType(roleType);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_sale_year_report.role_type
     *
     * @param roleType the value for supply_chain..mini_program_sale_year_report.role_type
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    public void setRoleType(String roleType) {
        this.roleType = roleType == null ? null : roleType.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..mini_program_sale_year_report.province_code
     *
     * @return the value of supply_chain..mini_program_sale_year_report.province_code
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    public String getProvinceCode() {
        return provinceCode;
    }

    /**
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    public MiniProgramSaleYearReport withProvinceCode(String provinceCode) {
        this.setProvinceCode(provinceCode);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_sale_year_report.province_code
     *
     * @param provinceCode the value for supply_chain..mini_program_sale_year_report.province_code
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    public void setProvinceCode(String provinceCode) {
        this.provinceCode = provinceCode == null ? null : provinceCode.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..mini_program_sale_year_report.province_name
     *
     * @return the value of supply_chain..mini_program_sale_year_report.province_name
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    public String getProvinceName() {
        return provinceName;
    }

    /**
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    public MiniProgramSaleYearReport withProvinceName(String provinceName) {
        this.setProvinceName(provinceName);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_sale_year_report.province_name
     *
     * @param provinceName the value for supply_chain..mini_program_sale_year_report.province_name
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    public void setProvinceName(String provinceName) {
        this.provinceName = provinceName == null ? null : provinceName.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..mini_program_sale_year_report.city_code
     *
     * @return the value of supply_chain..mini_program_sale_year_report.city_code
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    public String getCityCode() {
        return cityCode;
    }

    /**
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    public MiniProgramSaleYearReport withCityCode(String cityCode) {
        this.setCityCode(cityCode);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_sale_year_report.city_code
     *
     * @param cityCode the value for supply_chain..mini_program_sale_year_report.city_code
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    public void setCityCode(String cityCode) {
        this.cityCode = cityCode == null ? null : cityCode.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..mini_program_sale_year_report.city_name
     *
     * @return the value of supply_chain..mini_program_sale_year_report.city_name
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    public String getCityName() {
        return cityName;
    }

    /**
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    public MiniProgramSaleYearReport withCityName(String cityName) {
        this.setCityName(cityName);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_sale_year_report.city_name
     *
     * @param cityName the value for supply_chain..mini_program_sale_year_report.city_name
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    public void setCityName(String cityName) {
        this.cityName = cityName == null ? null : cityName.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..mini_program_sale_year_report.first_order_time
     *
     * @return the value of supply_chain..mini_program_sale_year_report.first_order_time
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    public Date getFirstOrderTime() {
        return firstOrderTime;
    }

    /**
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    public MiniProgramSaleYearReport withFirstOrderTime(Date firstOrderTime) {
        this.setFirstOrderTime(firstOrderTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_sale_year_report.first_order_time
     *
     * @param firstOrderTime the value for supply_chain..mini_program_sale_year_report.first_order_time
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    public void setFirstOrderTime(Date firstOrderTime) {
        this.firstOrderTime = firstOrderTime;
    }

    /**
     * This method returns the value of the database column supply_chain..mini_program_sale_year_report.year_last_order_time
     *
     * @return the value of supply_chain..mini_program_sale_year_report.year_last_order_time
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    public Date getYearLastOrderTime() {
        return yearLastOrderTime;
    }

    /**
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    public MiniProgramSaleYearReport withYearLastOrderTime(Date yearLastOrderTime) {
        this.setYearLastOrderTime(yearLastOrderTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_sale_year_report.year_last_order_time
     *
     * @param yearLastOrderTime the value for supply_chain..mini_program_sale_year_report.year_last_order_time
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    public void setYearLastOrderTime(Date yearLastOrderTime) {
        this.yearLastOrderTime = yearLastOrderTime;
    }

    /**
     * This method returns the value of the database column supply_chain..mini_program_sale_year_report.first_order_spu_code
     *
     * @return the value of supply_chain..mini_program_sale_year_report.first_order_spu_code
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    public String getFirstOrderSpuCode() {
        return firstOrderSpuCode;
    }

    /**
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    public MiniProgramSaleYearReport withFirstOrderSpuCode(String firstOrderSpuCode) {
        this.setFirstOrderSpuCode(firstOrderSpuCode);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_sale_year_report.first_order_spu_code
     *
     * @param firstOrderSpuCode the value for supply_chain..mini_program_sale_year_report.first_order_spu_code
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    public void setFirstOrderSpuCode(String firstOrderSpuCode) {
        this.firstOrderSpuCode = firstOrderSpuCode == null ? null : firstOrderSpuCode.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..mini_program_sale_year_report.first_order_spu_name
     *
     * @return the value of supply_chain..mini_program_sale_year_report.first_order_spu_name
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    public String getFirstOrderSpuName() {
        return firstOrderSpuName;
    }

    /**
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    public MiniProgramSaleYearReport withFirstOrderSpuName(String firstOrderSpuName) {
        this.setFirstOrderSpuName(firstOrderSpuName);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_sale_year_report.first_order_spu_name
     *
     * @param firstOrderSpuName the value for supply_chain..mini_program_sale_year_report.first_order_spu_name
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    public void setFirstOrderSpuName(String firstOrderSpuName) {
        this.firstOrderSpuName = firstOrderSpuName == null ? null : firstOrderSpuName.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..mini_program_sale_year_report.order_total_price
     *
     * @return the value of supply_chain..mini_program_sale_year_report.order_total_price
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    public Long getOrderTotalPrice() {
        return orderTotalPrice;
    }

    /**
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    public MiniProgramSaleYearReport withOrderTotalPrice(Long orderTotalPrice) {
        this.setOrderTotalPrice(orderTotalPrice);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_sale_year_report.order_total_price
     *
     * @param orderTotalPrice the value for supply_chain..mini_program_sale_year_report.order_total_price
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    public void setOrderTotalPrice(Long orderTotalPrice) {
        this.orderTotalPrice = orderTotalPrice;
    }

    /**
     * This method returns the value of the database column supply_chain..mini_program_sale_year_report.order_total_count
     *
     * @return the value of supply_chain..mini_program_sale_year_report.order_total_count
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    public Long getOrderTotalCount() {
        return orderTotalCount;
    }

    /**
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    public MiniProgramSaleYearReport withOrderTotalCount(Long orderTotalCount) {
        this.setOrderTotalCount(orderTotalCount);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_sale_year_report.order_total_count
     *
     * @param orderTotalCount the value for supply_chain..mini_program_sale_year_report.order_total_count
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    public void setOrderTotalCount(Long orderTotalCount) {
        this.orderTotalCount = orderTotalCount;
    }

    /**
     * This method returns the value of the database column supply_chain..mini_program_sale_year_report.order_total_count_prepay
     *
     * @return the value of supply_chain..mini_program_sale_year_report.order_total_count_prepay
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    public Long getOrderTotalCountPrepay() {
        return orderTotalCountPrepay;
    }

    /**
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    public MiniProgramSaleYearReport withOrderTotalCountPrepay(Long orderTotalCountPrepay) {
        this.setOrderTotalCountPrepay(orderTotalCountPrepay);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_sale_year_report.order_total_count_prepay
     *
     * @param orderTotalCountPrepay the value for supply_chain..mini_program_sale_year_report.order_total_count_prepay
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    public void setOrderTotalCountPrepay(Long orderTotalCountPrepay) {
        this.orderTotalCountPrepay = orderTotalCountPrepay;
    }

    /**
     * This method returns the value of the database column supply_chain..mini_program_sale_year_report.order_total_count_afterpay
     *
     * @return the value of supply_chain..mini_program_sale_year_report.order_total_count_afterpay
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    public Long getOrderTotalCountAfterpay() {
        return orderTotalCountAfterpay;
    }

    /**
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    public MiniProgramSaleYearReport withOrderTotalCountAfterpay(Long orderTotalCountAfterpay) {
        this.setOrderTotalCountAfterpay(orderTotalCountAfterpay);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_sale_year_report.order_total_count_afterpay
     *
     * @param orderTotalCountAfterpay the value for supply_chain..mini_program_sale_year_report.order_total_count_afterpay
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    public void setOrderTotalCountAfterpay(Long orderTotalCountAfterpay) {
        this.orderTotalCountAfterpay = orderTotalCountAfterpay;
    }

    /**
     * This method returns the value of the database column supply_chain..mini_program_sale_year_report.order_total_count_mix
     *
     * @return the value of supply_chain..mini_program_sale_year_report.order_total_count_mix
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    public Long getOrderTotalCountMix() {
        return orderTotalCountMix;
    }

    /**
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    public MiniProgramSaleYearReport withOrderTotalCountMix(Long orderTotalCountMix) {
        this.setOrderTotalCountMix(orderTotalCountMix);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_sale_year_report.order_total_count_mix
     *
     * @param orderTotalCountMix the value for supply_chain..mini_program_sale_year_report.order_total_count_mix
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    public void setOrderTotalCountMix(Long orderTotalCountMix) {
        this.orderTotalCountMix = orderTotalCountMix;
    }

    /**
     * This method returns the value of the database column supply_chain..mini_program_sale_year_report.order_total_point
     *
     * @return the value of supply_chain..mini_program_sale_year_report.order_total_point
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    public Long getOrderTotalPoint() {
        return orderTotalPoint;
    }

    /**
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    public MiniProgramSaleYearReport withOrderTotalPoint(Long orderTotalPoint) {
        this.setOrderTotalPoint(orderTotalPoint);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_sale_year_report.order_total_point
     *
     * @param orderTotalPoint the value for supply_chain..mini_program_sale_year_report.order_total_point
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    public void setOrderTotalPoint(Long orderTotalPoint) {
        this.orderTotalPoint = orderTotalPoint;
    }

    /**
     * This method returns the value of the database column supply_chain..mini_program_sale_year_report.spu_total_count
     *
     * @return the value of supply_chain..mini_program_sale_year_report.spu_total_count
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    public Long getSpuTotalCount() {
        return spuTotalCount;
    }

    /**
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    public MiniProgramSaleYearReport withSpuTotalCount(Long spuTotalCount) {
        this.setSpuTotalCount(spuTotalCount);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_sale_year_report.spu_total_count
     *
     * @param spuTotalCount the value for supply_chain..mini_program_sale_year_report.spu_total_count
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    public void setSpuTotalCount(Long spuTotalCount) {
        this.spuTotalCount = spuTotalCount;
    }

    /**
     * This method returns the value of the database column supply_chain..mini_program_sale_year_report.sale_ranking
     *
     * @return the value of supply_chain..mini_program_sale_year_report.sale_ranking
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    public Long getSaleRanking() {
        return saleRanking;
    }

    /**
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    public MiniProgramSaleYearReport withSaleRanking(Long saleRanking) {
        this.setSaleRanking(saleRanking);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_sale_year_report.sale_ranking
     *
     * @param saleRanking the value for supply_chain..mini_program_sale_year_report.sale_ranking
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    public void setSaleRanking(Long saleRanking) {
        this.saleRanking = saleRanking;
    }

    /**
     * This method returns the value of the database column supply_chain..mini_program_sale_year_report.best_sale_spu_code
     *
     * @return the value of supply_chain..mini_program_sale_year_report.best_sale_spu_code
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    public String getBestSaleSpuCode() {
        return bestSaleSpuCode;
    }

    /**
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    public MiniProgramSaleYearReport withBestSaleSpuCode(String bestSaleSpuCode) {
        this.setBestSaleSpuCode(bestSaleSpuCode);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_sale_year_report.best_sale_spu_code
     *
     * @param bestSaleSpuCode the value for supply_chain..mini_program_sale_year_report.best_sale_spu_code
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    public void setBestSaleSpuCode(String bestSaleSpuCode) {
        this.bestSaleSpuCode = bestSaleSpuCode == null ? null : bestSaleSpuCode.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..mini_program_sale_year_report.best_sale_spu_name
     *
     * @return the value of supply_chain..mini_program_sale_year_report.best_sale_spu_name
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    public String getBestSaleSpuName() {
        return bestSaleSpuName;
    }

    /**
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    public MiniProgramSaleYearReport withBestSaleSpuName(String bestSaleSpuName) {
        this.setBestSaleSpuName(bestSaleSpuName);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_sale_year_report.best_sale_spu_name
     *
     * @param bestSaleSpuName the value for supply_chain..mini_program_sale_year_report.best_sale_spu_name
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    public void setBestSaleSpuName(String bestSaleSpuName) {
        this.bestSaleSpuName = bestSaleSpuName == null ? null : bestSaleSpuName.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..mini_program_sale_year_report.best_sale_spu_order_count
     *
     * @return the value of supply_chain..mini_program_sale_year_report.best_sale_spu_order_count
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    public Long getBestSaleSpuOrderCount() {
        return bestSaleSpuOrderCount;
    }

    /**
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    public MiniProgramSaleYearReport withBestSaleSpuOrderCount(Long bestSaleSpuOrderCount) {
        this.setBestSaleSpuOrderCount(bestSaleSpuOrderCount);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_sale_year_report.best_sale_spu_order_count
     *
     * @param bestSaleSpuOrderCount the value for supply_chain..mini_program_sale_year_report.best_sale_spu_order_count
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    public void setBestSaleSpuOrderCount(Long bestSaleSpuOrderCount) {
        this.bestSaleSpuOrderCount = bestSaleSpuOrderCount;
    }

    /**
     * This method returns the value of the database column supply_chain..mini_program_sale_year_report.best_sale_spu_code_group
     *
     * @return the value of supply_chain..mini_program_sale_year_report.best_sale_spu_code_group
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    public String getBestSaleSpuCodeGroup() {
        return bestSaleSpuCodeGroup;
    }

    /**
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    public MiniProgramSaleYearReport withBestSaleSpuCodeGroup(String bestSaleSpuCodeGroup) {
        this.setBestSaleSpuCodeGroup(bestSaleSpuCodeGroup);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_sale_year_report.best_sale_spu_code_group
     *
     * @param bestSaleSpuCodeGroup the value for supply_chain..mini_program_sale_year_report.best_sale_spu_code_group
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    public void setBestSaleSpuCodeGroup(String bestSaleSpuCodeGroup) {
        this.bestSaleSpuCodeGroup = bestSaleSpuCodeGroup == null ? null : bestSaleSpuCodeGroup.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..mini_program_sale_year_report.best_sale_spu_name_group
     *
     * @return the value of supply_chain..mini_program_sale_year_report.best_sale_spu_name_group
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    public String getBestSaleSpuNameGroup() {
        return bestSaleSpuNameGroup;
    }

    /**
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    public MiniProgramSaleYearReport withBestSaleSpuNameGroup(String bestSaleSpuNameGroup) {
        this.setBestSaleSpuNameGroup(bestSaleSpuNameGroup);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_sale_year_report.best_sale_spu_name_group
     *
     * @param bestSaleSpuNameGroup the value for supply_chain..mini_program_sale_year_report.best_sale_spu_name_group
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    public void setBestSaleSpuNameGroup(String bestSaleSpuNameGroup) {
        this.bestSaleSpuNameGroup = bestSaleSpuNameGroup == null ? null : bestSaleSpuNameGroup.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..mini_program_sale_year_report.best_sale_spu_count_group
     *
     * @return the value of supply_chain..mini_program_sale_year_report.best_sale_spu_count_group
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    public Long getBestSaleSpuCountGroup() {
        return bestSaleSpuCountGroup;
    }

    /**
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    public MiniProgramSaleYearReport withBestSaleSpuCountGroup(Long bestSaleSpuCountGroup) {
        this.setBestSaleSpuCountGroup(bestSaleSpuCountGroup);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_sale_year_report.best_sale_spu_count_group
     *
     * @param bestSaleSpuCountGroup the value for supply_chain..mini_program_sale_year_report.best_sale_spu_count_group
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    public void setBestSaleSpuCountGroup(Long bestSaleSpuCountGroup) {
        this.bestSaleSpuCountGroup = bestSaleSpuCountGroup;
    }

    /**
     * This method returns the value of the database column supply_chain..mini_program_sale_year_report.best_sale_spu_code_individual
     *
     * @return the value of supply_chain..mini_program_sale_year_report.best_sale_spu_code_individual
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    public String getBestSaleSpuCodeIndividual() {
        return bestSaleSpuCodeIndividual;
    }

    /**
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    public MiniProgramSaleYearReport withBestSaleSpuCodeIndividual(String bestSaleSpuCodeIndividual) {
        this.setBestSaleSpuCodeIndividual(bestSaleSpuCodeIndividual);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_sale_year_report.best_sale_spu_code_individual
     *
     * @param bestSaleSpuCodeIndividual the value for supply_chain..mini_program_sale_year_report.best_sale_spu_code_individual
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    public void setBestSaleSpuCodeIndividual(String bestSaleSpuCodeIndividual) {
        this.bestSaleSpuCodeIndividual = bestSaleSpuCodeIndividual == null ? null : bestSaleSpuCodeIndividual.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..mini_program_sale_year_report.best_sale_spu_name_individual
     *
     * @return the value of supply_chain..mini_program_sale_year_report.best_sale_spu_name_individual
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    public String getBestSaleSpuNameIndividual() {
        return bestSaleSpuNameIndividual;
    }

    /**
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    public MiniProgramSaleYearReport withBestSaleSpuNameIndividual(String bestSaleSpuNameIndividual) {
        this.setBestSaleSpuNameIndividual(bestSaleSpuNameIndividual);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_sale_year_report.best_sale_spu_name_individual
     *
     * @param bestSaleSpuNameIndividual the value for supply_chain..mini_program_sale_year_report.best_sale_spu_name_individual
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    public void setBestSaleSpuNameIndividual(String bestSaleSpuNameIndividual) {
        this.bestSaleSpuNameIndividual = bestSaleSpuNameIndividual == null ? null : bestSaleSpuNameIndividual.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..mini_program_sale_year_report.best_sale_spu_count_individual
     *
     * @return the value of supply_chain..mini_program_sale_year_report.best_sale_spu_count_individual
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    public Long getBestSaleSpuCountIndividual() {
        return bestSaleSpuCountIndividual;
    }

    /**
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    public MiniProgramSaleYearReport withBestSaleSpuCountIndividual(Long bestSaleSpuCountIndividual) {
        this.setBestSaleSpuCountIndividual(bestSaleSpuCountIndividual);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_sale_year_report.best_sale_spu_count_individual
     *
     * @param bestSaleSpuCountIndividual the value for supply_chain..mini_program_sale_year_report.best_sale_spu_count_individual
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    public void setBestSaleSpuCountIndividual(Long bestSaleSpuCountIndividual) {
        this.bestSaleSpuCountIndividual = bestSaleSpuCountIndividual;
    }

    /**
     * This method returns the value of the database column supply_chain..mini_program_sale_year_report.best_sale_spu_prompt
     *
     * @return the value of supply_chain..mini_program_sale_year_report.best_sale_spu_prompt
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    public String getBestSaleSpuPrompt() {
        return bestSaleSpuPrompt;
    }

    /**
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    public MiniProgramSaleYearReport withBestSaleSpuPrompt(String bestSaleSpuPrompt) {
        this.setBestSaleSpuPrompt(bestSaleSpuPrompt);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_sale_year_report.best_sale_spu_prompt
     *
     * @param bestSaleSpuPrompt the value for supply_chain..mini_program_sale_year_report.best_sale_spu_prompt
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    public void setBestSaleSpuPrompt(String bestSaleSpuPrompt) {
        this.bestSaleSpuPrompt = bestSaleSpuPrompt == null ? null : bestSaleSpuPrompt.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..mini_program_sale_year_report.year_latest_order_time
     *
     * @return the value of supply_chain..mini_program_sale_year_report.year_latest_order_time
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    public Date getYearLatestOrderTime() {
        return yearLatestOrderTime;
    }

    /**
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    public MiniProgramSaleYearReport withYearLatestOrderTime(Date yearLatestOrderTime) {
        this.setYearLatestOrderTime(yearLatestOrderTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_sale_year_report.year_latest_order_time
     *
     * @param yearLatestOrderTime the value for supply_chain..mini_program_sale_year_report.year_latest_order_time
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    public void setYearLatestOrderTime(Date yearLatestOrderTime) {
        this.yearLatestOrderTime = yearLatestOrderTime;
    }

    /**
     * This method returns the value of the database column supply_chain..mini_program_sale_year_report.join_mall_activity_count
     *
     * @return the value of supply_chain..mini_program_sale_year_report.join_mall_activity_count
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    public Integer getJoinMallActivityCount() {
        return joinMallActivityCount;
    }

    /**
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    public MiniProgramSaleYearReport withJoinMallActivityCount(Integer joinMallActivityCount) {
        this.setJoinMallActivityCount(joinMallActivityCount);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_sale_year_report.join_mall_activity_count
     *
     * @param joinMallActivityCount the value for supply_chain..mini_program_sale_year_report.join_mall_activity_count
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    public void setJoinMallActivityCount(Integer joinMallActivityCount) {
        this.joinMallActivityCount = joinMallActivityCount;
    }

    /**
     * This method returns the value of the database column supply_chain..mini_program_sale_year_report.join_mall_activity_reward_count
     *
     * @return the value of supply_chain..mini_program_sale_year_report.join_mall_activity_reward_count
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    public Integer getJoinMallActivityRewardCount() {
        return joinMallActivityRewardCount;
    }

    /**
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    public MiniProgramSaleYearReport withJoinMallActivityRewardCount(Integer joinMallActivityRewardCount) {
        this.setJoinMallActivityRewardCount(joinMallActivityRewardCount);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_sale_year_report.join_mall_activity_reward_count
     *
     * @param joinMallActivityRewardCount the value for supply_chain..mini_program_sale_year_report.join_mall_activity_reward_count
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    public void setJoinMallActivityRewardCount(Integer joinMallActivityRewardCount) {
        this.joinMallActivityRewardCount = joinMallActivityRewardCount;
    }

    /**
     * This method returns the value of the database column supply_chain..mini_program_sale_year_report.join_mall_best_activity_name
     *
     * @return the value of supply_chain..mini_program_sale_year_report.join_mall_best_activity_name
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    public String getJoinMallBestActivityName() {
        return joinMallBestActivityName;
    }

    /**
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    public MiniProgramSaleYearReport withJoinMallBestActivityName(String joinMallBestActivityName) {
        this.setJoinMallBestActivityName(joinMallBestActivityName);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_sale_year_report.join_mall_best_activity_name
     *
     * @param joinMallBestActivityName the value for supply_chain..mini_program_sale_year_report.join_mall_best_activity_name
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    public void setJoinMallBestActivityName(String joinMallBestActivityName) {
        this.joinMallBestActivityName = joinMallBestActivityName == null ? null : joinMallBestActivityName.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..mini_program_sale_year_report.join_mall_best_activity_ranking
     *
     * @return the value of supply_chain..mini_program_sale_year_report.join_mall_best_activity_ranking
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    public Long getJoinMallBestActivityRanking() {
        return joinMallBestActivityRanking;
    }

    /**
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    public MiniProgramSaleYearReport withJoinMallBestActivityRanking(Long joinMallBestActivityRanking) {
        this.setJoinMallBestActivityRanking(joinMallBestActivityRanking);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_sale_year_report.join_mall_best_activity_ranking
     *
     * @param joinMallBestActivityRanking the value for supply_chain..mini_program_sale_year_report.join_mall_best_activity_ranking
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    public void setJoinMallBestActivityRanking(Long joinMallBestActivityRanking) {
        this.joinMallBestActivityRanking = joinMallBestActivityRanking;
    }

    /**
     * This method returns the value of the database column supply_chain..mini_program_sale_year_report.customer_count
     *
     * @return the value of supply_chain..mini_program_sale_year_report.customer_count
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    public Long getCustomerCount() {
        return customerCount;
    }

    /**
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    public MiniProgramSaleYearReport withCustomerCount(Long customerCount) {
        this.setCustomerCount(customerCount);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_sale_year_report.customer_count
     *
     * @param customerCount the value for supply_chain..mini_program_sale_year_report.customer_count
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    public void setCustomerCount(Long customerCount) {
        this.customerCount = customerCount;
    }

    /**
     * This method returns the value of the database column supply_chain..mini_program_sale_year_report.customer_count_group
     *
     * @return the value of supply_chain..mini_program_sale_year_report.customer_count_group
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    public Long getCustomerCountGroup() {
        return customerCountGroup;
    }

    /**
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    public MiniProgramSaleYearReport withCustomerCountGroup(Long customerCountGroup) {
        this.setCustomerCountGroup(customerCountGroup);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_sale_year_report.customer_count_group
     *
     * @param customerCountGroup the value for supply_chain..mini_program_sale_year_report.customer_count_group
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    public void setCustomerCountGroup(Long customerCountGroup) {
        this.customerCountGroup = customerCountGroup;
    }

    /**
     * This method returns the value of the database column supply_chain..mini_program_sale_year_report.customer_count_individual
     *
     * @return the value of supply_chain..mini_program_sale_year_report.customer_count_individual
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    public Long getCustomerCountIndividual() {
        return customerCountIndividual;
    }

    /**
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    public MiniProgramSaleYearReport withCustomerCountIndividual(Long customerCountIndividual) {
        this.setCustomerCountIndividual(customerCountIndividual);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_sale_year_report.customer_count_individual
     *
     * @param customerCountIndividual the value for supply_chain..mini_program_sale_year_report.customer_count_individual
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    public void setCustomerCountIndividual(Long customerCountIndividual) {
        this.customerCountIndividual = customerCountIndividual;
    }

    /**
     * This method returns the value of the database column supply_chain..mini_program_sale_year_report.customer_total_count
     *
     * @return the value of supply_chain..mini_program_sale_year_report.customer_total_count
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    public Long getCustomerTotalCount() {
        return customerTotalCount;
    }

    /**
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    public MiniProgramSaleYearReport withCustomerTotalCount(Long customerTotalCount) {
        this.setCustomerTotalCount(customerTotalCount);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_sale_year_report.customer_total_count
     *
     * @param customerTotalCount the value for supply_chain..mini_program_sale_year_report.customer_total_count
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    public void setCustomerTotalCount(Long customerTotalCount) {
        this.customerTotalCount = customerTotalCount;
    }

    /**
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", userId=").append(userId);
        sb.append(", name=").append(name);
        sb.append(", year=").append(year);
        sb.append(", registerIndex=").append(registerIndex);
        sb.append(", registerTime=").append(registerTime);
        sb.append(", roleType=").append(roleType);
        sb.append(", provinceCode=").append(provinceCode);
        sb.append(", provinceName=").append(provinceName);
        sb.append(", cityCode=").append(cityCode);
        sb.append(", cityName=").append(cityName);
        sb.append(", firstOrderTime=").append(firstOrderTime);
        sb.append(", yearLastOrderTime=").append(yearLastOrderTime);
        sb.append(", firstOrderSpuCode=").append(firstOrderSpuCode);
        sb.append(", firstOrderSpuName=").append(firstOrderSpuName);
        sb.append(", orderTotalPrice=").append(orderTotalPrice);
        sb.append(", orderTotalCount=").append(orderTotalCount);
        sb.append(", orderTotalCountPrepay=").append(orderTotalCountPrepay);
        sb.append(", orderTotalCountAfterpay=").append(orderTotalCountAfterpay);
        sb.append(", orderTotalCountMix=").append(orderTotalCountMix);
        sb.append(", orderTotalPoint=").append(orderTotalPoint);
        sb.append(", spuTotalCount=").append(spuTotalCount);
        sb.append(", saleRanking=").append(saleRanking);
        sb.append(", bestSaleSpuCode=").append(bestSaleSpuCode);
        sb.append(", bestSaleSpuName=").append(bestSaleSpuName);
        sb.append(", bestSaleSpuOrderCount=").append(bestSaleSpuOrderCount);
        sb.append(", bestSaleSpuCodeGroup=").append(bestSaleSpuCodeGroup);
        sb.append(", bestSaleSpuNameGroup=").append(bestSaleSpuNameGroup);
        sb.append(", bestSaleSpuCountGroup=").append(bestSaleSpuCountGroup);
        sb.append(", bestSaleSpuCodeIndividual=").append(bestSaleSpuCodeIndividual);
        sb.append(", bestSaleSpuNameIndividual=").append(bestSaleSpuNameIndividual);
        sb.append(", bestSaleSpuCountIndividual=").append(bestSaleSpuCountIndividual);
        sb.append(", bestSaleSpuPrompt=").append(bestSaleSpuPrompt);
        sb.append(", yearLatestOrderTime=").append(yearLatestOrderTime);
        sb.append(", joinMallActivityCount=").append(joinMallActivityCount);
        sb.append(", joinMallActivityRewardCount=").append(joinMallActivityRewardCount);
        sb.append(", joinMallBestActivityName=").append(joinMallBestActivityName);
        sb.append(", joinMallBestActivityRanking=").append(joinMallBestActivityRanking);
        sb.append(", customerCount=").append(customerCount);
        sb.append(", customerCountGroup=").append(customerCountGroup);
        sb.append(", customerCountIndividual=").append(customerCountIndividual);
        sb.append(", customerTotalCount=").append(customerTotalCount);
        sb.append("]");
        return sb.toString();
    }

    /**
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        MiniProgramSaleYearReport other = (MiniProgramSaleYearReport) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getUserId() == null ? other.getUserId() == null : this.getUserId().equals(other.getUserId()))
            && (this.getName() == null ? other.getName() == null : this.getName().equals(other.getName()))
            && (this.getYear() == null ? other.getYear() == null : this.getYear().equals(other.getYear()))
            && (this.getRegisterIndex() == null ? other.getRegisterIndex() == null : this.getRegisterIndex().equals(other.getRegisterIndex()))
            && (this.getRegisterTime() == null ? other.getRegisterTime() == null : this.getRegisterTime().equals(other.getRegisterTime()))
            && (this.getRoleType() == null ? other.getRoleType() == null : this.getRoleType().equals(other.getRoleType()))
            && (this.getProvinceCode() == null ? other.getProvinceCode() == null : this.getProvinceCode().equals(other.getProvinceCode()))
            && (this.getProvinceName() == null ? other.getProvinceName() == null : this.getProvinceName().equals(other.getProvinceName()))
            && (this.getCityCode() == null ? other.getCityCode() == null : this.getCityCode().equals(other.getCityCode()))
            && (this.getCityName() == null ? other.getCityName() == null : this.getCityName().equals(other.getCityName()))
            && (this.getFirstOrderTime() == null ? other.getFirstOrderTime() == null : this.getFirstOrderTime().equals(other.getFirstOrderTime()))
            && (this.getYearLastOrderTime() == null ? other.getYearLastOrderTime() == null : this.getYearLastOrderTime().equals(other.getYearLastOrderTime()))
            && (this.getFirstOrderSpuCode() == null ? other.getFirstOrderSpuCode() == null : this.getFirstOrderSpuCode().equals(other.getFirstOrderSpuCode()))
            && (this.getFirstOrderSpuName() == null ? other.getFirstOrderSpuName() == null : this.getFirstOrderSpuName().equals(other.getFirstOrderSpuName()))
            && (this.getOrderTotalPrice() == null ? other.getOrderTotalPrice() == null : this.getOrderTotalPrice().equals(other.getOrderTotalPrice()))
            && (this.getOrderTotalCount() == null ? other.getOrderTotalCount() == null : this.getOrderTotalCount().equals(other.getOrderTotalCount()))
            && (this.getOrderTotalCountPrepay() == null ? other.getOrderTotalCountPrepay() == null : this.getOrderTotalCountPrepay().equals(other.getOrderTotalCountPrepay()))
            && (this.getOrderTotalCountAfterpay() == null ? other.getOrderTotalCountAfterpay() == null : this.getOrderTotalCountAfterpay().equals(other.getOrderTotalCountAfterpay()))
            && (this.getOrderTotalCountMix() == null ? other.getOrderTotalCountMix() == null : this.getOrderTotalCountMix().equals(other.getOrderTotalCountMix()))
            && (this.getOrderTotalPoint() == null ? other.getOrderTotalPoint() == null : this.getOrderTotalPoint().equals(other.getOrderTotalPoint()))
            && (this.getSpuTotalCount() == null ? other.getSpuTotalCount() == null : this.getSpuTotalCount().equals(other.getSpuTotalCount()))
            && (this.getSaleRanking() == null ? other.getSaleRanking() == null : this.getSaleRanking().equals(other.getSaleRanking()))
            && (this.getBestSaleSpuCode() == null ? other.getBestSaleSpuCode() == null : this.getBestSaleSpuCode().equals(other.getBestSaleSpuCode()))
            && (this.getBestSaleSpuName() == null ? other.getBestSaleSpuName() == null : this.getBestSaleSpuName().equals(other.getBestSaleSpuName()))
            && (this.getBestSaleSpuOrderCount() == null ? other.getBestSaleSpuOrderCount() == null : this.getBestSaleSpuOrderCount().equals(other.getBestSaleSpuOrderCount()))
            && (this.getBestSaleSpuCodeGroup() == null ? other.getBestSaleSpuCodeGroup() == null : this.getBestSaleSpuCodeGroup().equals(other.getBestSaleSpuCodeGroup()))
            && (this.getBestSaleSpuNameGroup() == null ? other.getBestSaleSpuNameGroup() == null : this.getBestSaleSpuNameGroup().equals(other.getBestSaleSpuNameGroup()))
            && (this.getBestSaleSpuCountGroup() == null ? other.getBestSaleSpuCountGroup() == null : this.getBestSaleSpuCountGroup().equals(other.getBestSaleSpuCountGroup()))
            && (this.getBestSaleSpuCodeIndividual() == null ? other.getBestSaleSpuCodeIndividual() == null : this.getBestSaleSpuCodeIndividual().equals(other.getBestSaleSpuCodeIndividual()))
            && (this.getBestSaleSpuNameIndividual() == null ? other.getBestSaleSpuNameIndividual() == null : this.getBestSaleSpuNameIndividual().equals(other.getBestSaleSpuNameIndividual()))
            && (this.getBestSaleSpuCountIndividual() == null ? other.getBestSaleSpuCountIndividual() == null : this.getBestSaleSpuCountIndividual().equals(other.getBestSaleSpuCountIndividual()))
            && (this.getBestSaleSpuPrompt() == null ? other.getBestSaleSpuPrompt() == null : this.getBestSaleSpuPrompt().equals(other.getBestSaleSpuPrompt()))
            && (this.getYearLatestOrderTime() == null ? other.getYearLatestOrderTime() == null : this.getYearLatestOrderTime().equals(other.getYearLatestOrderTime()))
            && (this.getJoinMallActivityCount() == null ? other.getJoinMallActivityCount() == null : this.getJoinMallActivityCount().equals(other.getJoinMallActivityCount()))
            && (this.getJoinMallActivityRewardCount() == null ? other.getJoinMallActivityRewardCount() == null : this.getJoinMallActivityRewardCount().equals(other.getJoinMallActivityRewardCount()))
            && (this.getJoinMallBestActivityName() == null ? other.getJoinMallBestActivityName() == null : this.getJoinMallBestActivityName().equals(other.getJoinMallBestActivityName()))
            && (this.getJoinMallBestActivityRanking() == null ? other.getJoinMallBestActivityRanking() == null : this.getJoinMallBestActivityRanking().equals(other.getJoinMallBestActivityRanking()))
            && (this.getCustomerCount() == null ? other.getCustomerCount() == null : this.getCustomerCount().equals(other.getCustomerCount()))
            && (this.getCustomerCountGroup() == null ? other.getCustomerCountGroup() == null : this.getCustomerCountGroup().equals(other.getCustomerCountGroup()))
            && (this.getCustomerCountIndividual() == null ? other.getCustomerCountIndividual() == null : this.getCustomerCountIndividual().equals(other.getCustomerCountIndividual()))
            && (this.getCustomerTotalCount() == null ? other.getCustomerTotalCount() == null : this.getCustomerTotalCount().equals(other.getCustomerTotalCount()));
    }

    /**
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getUserId() == null) ? 0 : getUserId().hashCode());
        result = prime * result + ((getName() == null) ? 0 : getName().hashCode());
        result = prime * result + ((getYear() == null) ? 0 : getYear().hashCode());
        result = prime * result + ((getRegisterIndex() == null) ? 0 : getRegisterIndex().hashCode());
        result = prime * result + ((getRegisterTime() == null) ? 0 : getRegisterTime().hashCode());
        result = prime * result + ((getRoleType() == null) ? 0 : getRoleType().hashCode());
        result = prime * result + ((getProvinceCode() == null) ? 0 : getProvinceCode().hashCode());
        result = prime * result + ((getProvinceName() == null) ? 0 : getProvinceName().hashCode());
        result = prime * result + ((getCityCode() == null) ? 0 : getCityCode().hashCode());
        result = prime * result + ((getCityName() == null) ? 0 : getCityName().hashCode());
        result = prime * result + ((getFirstOrderTime() == null) ? 0 : getFirstOrderTime().hashCode());
        result = prime * result + ((getYearLastOrderTime() == null) ? 0 : getYearLastOrderTime().hashCode());
        result = prime * result + ((getFirstOrderSpuCode() == null) ? 0 : getFirstOrderSpuCode().hashCode());
        result = prime * result + ((getFirstOrderSpuName() == null) ? 0 : getFirstOrderSpuName().hashCode());
        result = prime * result + ((getOrderTotalPrice() == null) ? 0 : getOrderTotalPrice().hashCode());
        result = prime * result + ((getOrderTotalCount() == null) ? 0 : getOrderTotalCount().hashCode());
        result = prime * result + ((getOrderTotalCountPrepay() == null) ? 0 : getOrderTotalCountPrepay().hashCode());
        result = prime * result + ((getOrderTotalCountAfterpay() == null) ? 0 : getOrderTotalCountAfterpay().hashCode());
        result = prime * result + ((getOrderTotalCountMix() == null) ? 0 : getOrderTotalCountMix().hashCode());
        result = prime * result + ((getOrderTotalPoint() == null) ? 0 : getOrderTotalPoint().hashCode());
        result = prime * result + ((getSpuTotalCount() == null) ? 0 : getSpuTotalCount().hashCode());
        result = prime * result + ((getSaleRanking() == null) ? 0 : getSaleRanking().hashCode());
        result = prime * result + ((getBestSaleSpuCode() == null) ? 0 : getBestSaleSpuCode().hashCode());
        result = prime * result + ((getBestSaleSpuName() == null) ? 0 : getBestSaleSpuName().hashCode());
        result = prime * result + ((getBestSaleSpuOrderCount() == null) ? 0 : getBestSaleSpuOrderCount().hashCode());
        result = prime * result + ((getBestSaleSpuCodeGroup() == null) ? 0 : getBestSaleSpuCodeGroup().hashCode());
        result = prime * result + ((getBestSaleSpuNameGroup() == null) ? 0 : getBestSaleSpuNameGroup().hashCode());
        result = prime * result + ((getBestSaleSpuCountGroup() == null) ? 0 : getBestSaleSpuCountGroup().hashCode());
        result = prime * result + ((getBestSaleSpuCodeIndividual() == null) ? 0 : getBestSaleSpuCodeIndividual().hashCode());
        result = prime * result + ((getBestSaleSpuNameIndividual() == null) ? 0 : getBestSaleSpuNameIndividual().hashCode());
        result = prime * result + ((getBestSaleSpuCountIndividual() == null) ? 0 : getBestSaleSpuCountIndividual().hashCode());
        result = prime * result + ((getBestSaleSpuPrompt() == null) ? 0 : getBestSaleSpuPrompt().hashCode());
        result = prime * result + ((getYearLatestOrderTime() == null) ? 0 : getYearLatestOrderTime().hashCode());
        result = prime * result + ((getJoinMallActivityCount() == null) ? 0 : getJoinMallActivityCount().hashCode());
        result = prime * result + ((getJoinMallActivityRewardCount() == null) ? 0 : getJoinMallActivityRewardCount().hashCode());
        result = prime * result + ((getJoinMallBestActivityName() == null) ? 0 : getJoinMallBestActivityName().hashCode());
        result = prime * result + ((getJoinMallBestActivityRanking() == null) ? 0 : getJoinMallBestActivityRanking().hashCode());
        result = prime * result + ((getCustomerCount() == null) ? 0 : getCustomerCount().hashCode());
        result = prime * result + ((getCustomerCountGroup() == null) ? 0 : getCustomerCountGroup().hashCode());
        result = prime * result + ((getCustomerCountIndividual() == null) ? 0 : getCustomerCountIndividual().hashCode());
        result = prime * result + ((getCustomerTotalCount() == null) ? 0 : getCustomerTotalCount().hashCode());
        return result;
    }

    /**
     *
     * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
     */
    public enum Column {
        id("id", "id", "VARCHAR", false),
        userId("user_id", "userId", "VARCHAR", false),
        name("name", "name", "VARCHAR", true),
        year("year", "year", "INTEGER", true),
        registerIndex("register_index", "registerIndex", "BIGINT", false),
        registerTime("register_time", "registerTime", "TIMESTAMP", false),
        roleType("role_type", "roleType", "VARCHAR", false),
        provinceCode("province_code", "provinceCode", "VARCHAR", false),
        provinceName("province_name", "provinceName", "VARCHAR", false),
        cityCode("city_code", "cityCode", "VARCHAR", false),
        cityName("city_name", "cityName", "VARCHAR", false),
        firstOrderTime("first_order_time", "firstOrderTime", "TIMESTAMP", false),
        yearLastOrderTime("year_last_order_time", "yearLastOrderTime", "TIMESTAMP", false),
        firstOrderSpuCode("first_order_spu_code", "firstOrderSpuCode", "VARCHAR", false),
        firstOrderSpuName("first_order_spu_name", "firstOrderSpuName", "VARCHAR", false),
        orderTotalPrice("order_total_price", "orderTotalPrice", "BIGINT", false),
        orderTotalCount("order_total_count", "orderTotalCount", "BIGINT", false),
        orderTotalCountPrepay("order_total_count_prepay", "orderTotalCountPrepay", "BIGINT", false),
        orderTotalCountAfterpay("order_total_count_afterpay", "orderTotalCountAfterpay", "BIGINT", false),
        orderTotalCountMix("order_total_count_mix", "orderTotalCountMix", "BIGINT", false),
        orderTotalPoint("order_total_point", "orderTotalPoint", "BIGINT", false),
        spuTotalCount("spu_total_count", "spuTotalCount", "BIGINT", false),
        saleRanking("sale_ranking", "saleRanking", "BIGINT", false),
        bestSaleSpuCode("best_sale_spu_code", "bestSaleSpuCode", "VARCHAR", false),
        bestSaleSpuName("best_sale_spu_name", "bestSaleSpuName", "VARCHAR", false),
        bestSaleSpuOrderCount("best_sale_spu_order_count", "bestSaleSpuOrderCount", "BIGINT", false),
        bestSaleSpuCodeGroup("best_sale_spu_code_group", "bestSaleSpuCodeGroup", "VARCHAR", false),
        bestSaleSpuNameGroup("best_sale_spu_name_group", "bestSaleSpuNameGroup", "VARCHAR", false),
        bestSaleSpuCountGroup("best_sale_spu_count_group", "bestSaleSpuCountGroup", "BIGINT", false),
        bestSaleSpuCodeIndividual("best_sale_spu_code_individual", "bestSaleSpuCodeIndividual", "VARCHAR", false),
        bestSaleSpuNameIndividual("best_sale_spu_name_individual", "bestSaleSpuNameIndividual", "VARCHAR", false),
        bestSaleSpuCountIndividual("best_sale_spu_count_individual", "bestSaleSpuCountIndividual", "BIGINT", false),
        bestSaleSpuPrompt("best_sale_spu_prompt", "bestSaleSpuPrompt", "VARCHAR", false),
        yearLatestOrderTime("year_latest_order_time", "yearLatestOrderTime", "TIMESTAMP", false),
        joinMallActivityCount("join_mall_activity_count", "joinMallActivityCount", "INTEGER", false),
        joinMallActivityRewardCount("join_mall_activity_reward_count", "joinMallActivityRewardCount", "INTEGER", false),
        joinMallBestActivityName("join_mall_best_activity_name", "joinMallBestActivityName", "VARCHAR", false),
        joinMallBestActivityRanking("join_mall_best_activity_ranking", "joinMallBestActivityRanking", "BIGINT", false),
        customerCount("customer_count", "customerCount", "BIGINT", false),
        customerCountGroup("customer_count_group", "customerCountGroup", "BIGINT", false),
        customerCountIndividual("customer_count_individual", "customerCountIndividual", "BIGINT", false),
        customerTotalCount("customer_total_count", "customerTotalCount", "BIGINT", false);

        /**
         *
         * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
         */
        private static final String BEGINNING_DELIMITER = "`";

        /**
         *
         * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
         */
        private static final String ENDING_DELIMITER = "`";

        /**
         *
         * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
         */
        private final String column;

        /**
         *
         * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
         */
        private final boolean isColumnNameDelimited;

        /**
         *
         * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
         */
        private final String javaProperty;

        /**
         *
         * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
         */
        private final String jdbcType;

        /**
         *
         * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
         */
        public String value() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
         */
        public String getValue() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
         */
        public String getJavaProperty() {
            return this.javaProperty;
        }

        /**
         *
         * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
         */
        public String getJdbcType() {
            return this.jdbcType;
        }

        /**
         *
         * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
         */
        Column(String column, String javaProperty, String jdbcType, boolean isColumnNameDelimited) {
            this.column = column;
            this.javaProperty = javaProperty;
            this.jdbcType = jdbcType;
            this.isColumnNameDelimited = isColumnNameDelimited;
        }

        /**
         *
         * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
         */
        public String desc() {
            return this.getEscapedColumnName() + " DESC";
        }

        /**
         *
         * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
         */
        public String asc() {
            return this.getEscapedColumnName() + " ASC";
        }

        /**
         *
         * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
         */
        public static Column[] excludes(Column ... excludes) {
            ArrayList<Column> columns = new ArrayList<>(Arrays.asList(Column.values()));
            if (excludes != null && excludes.length > 0) {
                columns.removeAll(new ArrayList<>(Arrays.asList(excludes)));
            }
            return columns.toArray(new Column[]{});
        }

        /**
         *
         * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
         */
        public static Column[] all() {
            return Column.values();
        }

        /**
         *
         * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
         */
        public String getEscapedColumnName() {
            if (this.isColumnNameDelimited) {
                return new StringBuilder().append(BEGINNING_DELIMITER).append(this.column).append(ENDING_DELIMITER).toString();
            } else {
                return this.column;
            }
        }

        /**
         *
         * @mbg.generated Thu Jan 02 20:38:07 GMT+08:00 2025
         */
        public String getAliasedEscapedColumnName() {
            return this.getEscapedColumnName();
        }
    }
}