package com.chinamobile.retail.pojo.entity;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class ProductFlowInstanceSpuExample {
    /**
     *
     * @mbg.generated Tue Jun 04 15:44:18 CST 2024
     */
    protected String orderByClause;

    /**
     *
     * @mbg.generated Tue Jun 04 15:44:18 CST 2024
     */
    protected boolean distinct;

    /**
     *
     * @mbg.generated Tue Jun 04 15:44:18 CST 2024
     */
    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    /**
     *
     * @mbg.generated Tue Jun 04 15:44:18 CST 2024
     */
    public ProductFlowInstanceSpuExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    /**
     *
     * @mbg.generated Tue Jun 04 15:44:18 CST 2024
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     *
     * @mbg.generated Tue Jun 04 15:44:18 CST 2024
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     *
     * @mbg.generated Tue Jun 04 15:44:18 CST 2024
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     *
     * @mbg.generated Tue Jun 04 15:44:18 CST 2024
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     *
     * @mbg.generated Tue Jun 04 15:44:18 CST 2024
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     *
     * @mbg.generated Tue Jun 04 15:44:18 CST 2024
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     *
     * @mbg.generated Tue Jun 04 15:44:18 CST 2024
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     *
     * @mbg.generated Tue Jun 04 15:44:18 CST 2024
     */
    public ProductFlowInstanceSpuExample orderBy(String orderByClause) {
        this.setOrderByClause(orderByClause);
        return this;
    }

    /**
     *
     * @mbg.generated Tue Jun 04 15:44:18 CST 2024
     */
    public ProductFlowInstanceSpuExample orderBy(String ... orderByClauses) {
        StringBuffer sb = new StringBuffer();
        for (int i = 0; i < orderByClauses.length; i++) {
            sb.append(orderByClauses[i]);
            if (i < orderByClauses.length - 1) {
                sb.append(" , ");
            }
        }
        this.setOrderByClause(sb.toString());
        return this;
    }

    /**
     *
     * @mbg.generated Tue Jun 04 15:44:18 CST 2024
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     *
     * @mbg.generated Tue Jun 04 15:44:18 CST 2024
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria(this);
        return criteria;
    }

    /**
     *
     * @mbg.generated Tue Jun 04 15:44:18 CST 2024
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    public void setPageInfo(Integer currentPage, Integer pageSize) {
        if(pageSize<1) throw new IllegalArgumentException("页大小不能小于1！");
        this.limit=pageSize;
        if(currentPage<1) throw new IllegalArgumentException("页数不能小于1！");
        this.offset=(currentPage-1)*pageSize;
    }

    /**
     *
     * @mbg.generated Tue Jun 04 15:44:18 CST 2024
     */
    public static Criteria newAndCreateCriteria() {
        ProductFlowInstanceSpuExample example = new ProductFlowInstanceSpuExample();
        return example.createCriteria();
    }

    /**
     *
     * @mbg.generated Tue Jun 04 15:44:18 CST 2024
     */
    public ProductFlowInstanceSpuExample when(boolean condition, IExampleWhen then) {
        if (condition) {
            then.example(this);
        }
        return this;
    }

    /**
     *
     * @mbg.generated Tue Jun 04 15:44:18 CST 2024
     */
    public ProductFlowInstanceSpuExample when(boolean condition, IExampleWhen then, IExampleWhen otherwise) {
        if (condition) {
            then.example(this);
        } else {
            otherwise.example(this);
        }
        return this;
    }

    /**
     *
     * @mbg.generated Tue Jun 04 15:44:18 CST 2024
     */
    public ProductFlowInstanceSpuExample distinct(boolean distinct) {
        this.setDistinct(distinct);
        return this;
    }

    /**
     *
     * @mbg.generated Tue Jun 04 15:44:18 CST 2024
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(String value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdEqualToColumn(ProductFlowInstanceSpu.Column column) {
            addCriterion(new StringBuilder("id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(String value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualToColumn(ProductFlowInstanceSpu.Column column) {
            addCriterion(new StringBuilder("id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(String value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanColumn(ProductFlowInstanceSpu.Column column) {
            addCriterion(new StringBuilder("id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(String value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualToColumn(ProductFlowInstanceSpu.Column column) {
            addCriterion(new StringBuilder("id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdLessThan(String value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanColumn(ProductFlowInstanceSpu.Column column) {
            addCriterion(new StringBuilder("id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(String value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualToColumn(ProductFlowInstanceSpu.Column column) {
            addCriterion(new StringBuilder("id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdLike(String value) {
            addCriterion("id like", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotLike(String value) {
            addCriterion("id not like", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<String> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<String> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(String value1, String value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(String value1, String value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andFlowInstanceIdIsNull() {
            addCriterion("flow_instance_id is null");
            return (Criteria) this;
        }

        public Criteria andFlowInstanceIdIsNotNull() {
            addCriterion("flow_instance_id is not null");
            return (Criteria) this;
        }

        public Criteria andFlowInstanceIdEqualTo(String value) {
            addCriterion("flow_instance_id =", value, "flowInstanceId");
            return (Criteria) this;
        }

        public Criteria andFlowInstanceIdEqualToColumn(ProductFlowInstanceSpu.Column column) {
            addCriterion(new StringBuilder("flow_instance_id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andFlowInstanceIdNotEqualTo(String value) {
            addCriterion("flow_instance_id <>", value, "flowInstanceId");
            return (Criteria) this;
        }

        public Criteria andFlowInstanceIdNotEqualToColumn(ProductFlowInstanceSpu.Column column) {
            addCriterion(new StringBuilder("flow_instance_id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andFlowInstanceIdGreaterThan(String value) {
            addCriterion("flow_instance_id >", value, "flowInstanceId");
            return (Criteria) this;
        }

        public Criteria andFlowInstanceIdGreaterThanColumn(ProductFlowInstanceSpu.Column column) {
            addCriterion(new StringBuilder("flow_instance_id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andFlowInstanceIdGreaterThanOrEqualTo(String value) {
            addCriterion("flow_instance_id >=", value, "flowInstanceId");
            return (Criteria) this;
        }

        public Criteria andFlowInstanceIdGreaterThanOrEqualToColumn(ProductFlowInstanceSpu.Column column) {
            addCriterion(new StringBuilder("flow_instance_id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andFlowInstanceIdLessThan(String value) {
            addCriterion("flow_instance_id <", value, "flowInstanceId");
            return (Criteria) this;
        }

        public Criteria andFlowInstanceIdLessThanColumn(ProductFlowInstanceSpu.Column column) {
            addCriterion(new StringBuilder("flow_instance_id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andFlowInstanceIdLessThanOrEqualTo(String value) {
            addCriterion("flow_instance_id <=", value, "flowInstanceId");
            return (Criteria) this;
        }

        public Criteria andFlowInstanceIdLessThanOrEqualToColumn(ProductFlowInstanceSpu.Column column) {
            addCriterion(new StringBuilder("flow_instance_id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andFlowInstanceIdLike(String value) {
            addCriterion("flow_instance_id like", value, "flowInstanceId");
            return (Criteria) this;
        }

        public Criteria andFlowInstanceIdNotLike(String value) {
            addCriterion("flow_instance_id not like", value, "flowInstanceId");
            return (Criteria) this;
        }

        public Criteria andFlowInstanceIdIn(List<String> values) {
            addCriterion("flow_instance_id in", values, "flowInstanceId");
            return (Criteria) this;
        }

        public Criteria andFlowInstanceIdNotIn(List<String> values) {
            addCriterion("flow_instance_id not in", values, "flowInstanceId");
            return (Criteria) this;
        }

        public Criteria andFlowInstanceIdBetween(String value1, String value2) {
            addCriterion("flow_instance_id between", value1, value2, "flowInstanceId");
            return (Criteria) this;
        }

        public Criteria andFlowInstanceIdNotBetween(String value1, String value2) {
            addCriterion("flow_instance_id not between", value1, value2, "flowInstanceId");
            return (Criteria) this;
        }

        public Criteria andFlowIdIsNull() {
            addCriterion("flow_id is null");
            return (Criteria) this;
        }

        public Criteria andFlowIdIsNotNull() {
            addCriterion("flow_id is not null");
            return (Criteria) this;
        }

        public Criteria andFlowIdEqualTo(String value) {
            addCriterion("flow_id =", value, "flowId");
            return (Criteria) this;
        }

        public Criteria andFlowIdEqualToColumn(ProductFlowInstanceSpu.Column column) {
            addCriterion(new StringBuilder("flow_id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andFlowIdNotEqualTo(String value) {
            addCriterion("flow_id <>", value, "flowId");
            return (Criteria) this;
        }

        public Criteria andFlowIdNotEqualToColumn(ProductFlowInstanceSpu.Column column) {
            addCriterion(new StringBuilder("flow_id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andFlowIdGreaterThan(String value) {
            addCriterion("flow_id >", value, "flowId");
            return (Criteria) this;
        }

        public Criteria andFlowIdGreaterThanColumn(ProductFlowInstanceSpu.Column column) {
            addCriterion(new StringBuilder("flow_id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andFlowIdGreaterThanOrEqualTo(String value) {
            addCriterion("flow_id >=", value, "flowId");
            return (Criteria) this;
        }

        public Criteria andFlowIdGreaterThanOrEqualToColumn(ProductFlowInstanceSpu.Column column) {
            addCriterion(new StringBuilder("flow_id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andFlowIdLessThan(String value) {
            addCriterion("flow_id <", value, "flowId");
            return (Criteria) this;
        }

        public Criteria andFlowIdLessThanColumn(ProductFlowInstanceSpu.Column column) {
            addCriterion(new StringBuilder("flow_id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andFlowIdLessThanOrEqualTo(String value) {
            addCriterion("flow_id <=", value, "flowId");
            return (Criteria) this;
        }

        public Criteria andFlowIdLessThanOrEqualToColumn(ProductFlowInstanceSpu.Column column) {
            addCriterion(new StringBuilder("flow_id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andFlowIdLike(String value) {
            addCriterion("flow_id like", value, "flowId");
            return (Criteria) this;
        }

        public Criteria andFlowIdNotLike(String value) {
            addCriterion("flow_id not like", value, "flowId");
            return (Criteria) this;
        }

        public Criteria andFlowIdIn(List<String> values) {
            addCriterion("flow_id in", values, "flowId");
            return (Criteria) this;
        }

        public Criteria andFlowIdNotIn(List<String> values) {
            addCriterion("flow_id not in", values, "flowId");
            return (Criteria) this;
        }

        public Criteria andFlowIdBetween(String value1, String value2) {
            addCriterion("flow_id between", value1, value2, "flowId");
            return (Criteria) this;
        }

        public Criteria andFlowIdNotBetween(String value1, String value2) {
            addCriterion("flow_id not between", value1, value2, "flowId");
            return (Criteria) this;
        }

        public Criteria andShelfCatagoryIdIsNull() {
            addCriterion("shelf_catagory_id is null");
            return (Criteria) this;
        }

        public Criteria andShelfCatagoryIdIsNotNull() {
            addCriterion("shelf_catagory_id is not null");
            return (Criteria) this;
        }

        public Criteria andShelfCatagoryIdEqualTo(String value) {
            addCriterion("shelf_catagory_id =", value, "shelfCatagoryId");
            return (Criteria) this;
        }

        public Criteria andShelfCatagoryIdEqualToColumn(ProductFlowInstanceSpu.Column column) {
            addCriterion(new StringBuilder("shelf_catagory_id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andShelfCatagoryIdNotEqualTo(String value) {
            addCriterion("shelf_catagory_id <>", value, "shelfCatagoryId");
            return (Criteria) this;
        }

        public Criteria andShelfCatagoryIdNotEqualToColumn(ProductFlowInstanceSpu.Column column) {
            addCriterion(new StringBuilder("shelf_catagory_id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andShelfCatagoryIdGreaterThan(String value) {
            addCriterion("shelf_catagory_id >", value, "shelfCatagoryId");
            return (Criteria) this;
        }

        public Criteria andShelfCatagoryIdGreaterThanColumn(ProductFlowInstanceSpu.Column column) {
            addCriterion(new StringBuilder("shelf_catagory_id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andShelfCatagoryIdGreaterThanOrEqualTo(String value) {
            addCriterion("shelf_catagory_id >=", value, "shelfCatagoryId");
            return (Criteria) this;
        }

        public Criteria andShelfCatagoryIdGreaterThanOrEqualToColumn(ProductFlowInstanceSpu.Column column) {
            addCriterion(new StringBuilder("shelf_catagory_id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andShelfCatagoryIdLessThan(String value) {
            addCriterion("shelf_catagory_id <", value, "shelfCatagoryId");
            return (Criteria) this;
        }

        public Criteria andShelfCatagoryIdLessThanColumn(ProductFlowInstanceSpu.Column column) {
            addCriterion(new StringBuilder("shelf_catagory_id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andShelfCatagoryIdLessThanOrEqualTo(String value) {
            addCriterion("shelf_catagory_id <=", value, "shelfCatagoryId");
            return (Criteria) this;
        }

        public Criteria andShelfCatagoryIdLessThanOrEqualToColumn(ProductFlowInstanceSpu.Column column) {
            addCriterion(new StringBuilder("shelf_catagory_id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andShelfCatagoryIdLike(String value) {
            addCriterion("shelf_catagory_id like", value, "shelfCatagoryId");
            return (Criteria) this;
        }

        public Criteria andShelfCatagoryIdNotLike(String value) {
            addCriterion("shelf_catagory_id not like", value, "shelfCatagoryId");
            return (Criteria) this;
        }

        public Criteria andShelfCatagoryIdIn(List<String> values) {
            addCriterion("shelf_catagory_id in", values, "shelfCatagoryId");
            return (Criteria) this;
        }

        public Criteria andShelfCatagoryIdNotIn(List<String> values) {
            addCriterion("shelf_catagory_id not in", values, "shelfCatagoryId");
            return (Criteria) this;
        }

        public Criteria andShelfCatagoryIdBetween(String value1, String value2) {
            addCriterion("shelf_catagory_id between", value1, value2, "shelfCatagoryId");
            return (Criteria) this;
        }

        public Criteria andShelfCatagoryIdNotBetween(String value1, String value2) {
            addCriterion("shelf_catagory_id not between", value1, value2, "shelfCatagoryId");
            return (Criteria) this;
        }

        public Criteria andFirstDirectoryIdIsNull() {
            addCriterion("first_directory_id is null");
            return (Criteria) this;
        }

        public Criteria andFirstDirectoryIdIsNotNull() {
            addCriterion("first_directory_id is not null");
            return (Criteria) this;
        }

        public Criteria andFirstDirectoryIdEqualTo(String value) {
            addCriterion("first_directory_id =", value, "firstDirectoryId");
            return (Criteria) this;
        }

        public Criteria andFirstDirectoryIdEqualToColumn(ProductFlowInstanceSpu.Column column) {
            addCriterion(new StringBuilder("first_directory_id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andFirstDirectoryIdNotEqualTo(String value) {
            addCriterion("first_directory_id <>", value, "firstDirectoryId");
            return (Criteria) this;
        }

        public Criteria andFirstDirectoryIdNotEqualToColumn(ProductFlowInstanceSpu.Column column) {
            addCriterion(new StringBuilder("first_directory_id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andFirstDirectoryIdGreaterThan(String value) {
            addCriterion("first_directory_id >", value, "firstDirectoryId");
            return (Criteria) this;
        }

        public Criteria andFirstDirectoryIdGreaterThanColumn(ProductFlowInstanceSpu.Column column) {
            addCriterion(new StringBuilder("first_directory_id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andFirstDirectoryIdGreaterThanOrEqualTo(String value) {
            addCriterion("first_directory_id >=", value, "firstDirectoryId");
            return (Criteria) this;
        }

        public Criteria andFirstDirectoryIdGreaterThanOrEqualToColumn(ProductFlowInstanceSpu.Column column) {
            addCriterion(new StringBuilder("first_directory_id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andFirstDirectoryIdLessThan(String value) {
            addCriterion("first_directory_id <", value, "firstDirectoryId");
            return (Criteria) this;
        }

        public Criteria andFirstDirectoryIdLessThanColumn(ProductFlowInstanceSpu.Column column) {
            addCriterion(new StringBuilder("first_directory_id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andFirstDirectoryIdLessThanOrEqualTo(String value) {
            addCriterion("first_directory_id <=", value, "firstDirectoryId");
            return (Criteria) this;
        }

        public Criteria andFirstDirectoryIdLessThanOrEqualToColumn(ProductFlowInstanceSpu.Column column) {
            addCriterion(new StringBuilder("first_directory_id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andFirstDirectoryIdLike(String value) {
            addCriterion("first_directory_id like", value, "firstDirectoryId");
            return (Criteria) this;
        }

        public Criteria andFirstDirectoryIdNotLike(String value) {
            addCriterion("first_directory_id not like", value, "firstDirectoryId");
            return (Criteria) this;
        }

        public Criteria andFirstDirectoryIdIn(List<String> values) {
            addCriterion("first_directory_id in", values, "firstDirectoryId");
            return (Criteria) this;
        }

        public Criteria andFirstDirectoryIdNotIn(List<String> values) {
            addCriterion("first_directory_id not in", values, "firstDirectoryId");
            return (Criteria) this;
        }

        public Criteria andFirstDirectoryIdBetween(String value1, String value2) {
            addCriterion("first_directory_id between", value1, value2, "firstDirectoryId");
            return (Criteria) this;
        }

        public Criteria andFirstDirectoryIdNotBetween(String value1, String value2) {
            addCriterion("first_directory_id not between", value1, value2, "firstDirectoryId");
            return (Criteria) this;
        }

        public Criteria andSecondDirectoryIdIsNull() {
            addCriterion("second_directory_id is null");
            return (Criteria) this;
        }

        public Criteria andSecondDirectoryIdIsNotNull() {
            addCriterion("second_directory_id is not null");
            return (Criteria) this;
        }

        public Criteria andSecondDirectoryIdEqualTo(String value) {
            addCriterion("second_directory_id =", value, "secondDirectoryId");
            return (Criteria) this;
        }

        public Criteria andSecondDirectoryIdEqualToColumn(ProductFlowInstanceSpu.Column column) {
            addCriterion(new StringBuilder("second_directory_id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSecondDirectoryIdNotEqualTo(String value) {
            addCriterion("second_directory_id <>", value, "secondDirectoryId");
            return (Criteria) this;
        }

        public Criteria andSecondDirectoryIdNotEqualToColumn(ProductFlowInstanceSpu.Column column) {
            addCriterion(new StringBuilder("second_directory_id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSecondDirectoryIdGreaterThan(String value) {
            addCriterion("second_directory_id >", value, "secondDirectoryId");
            return (Criteria) this;
        }

        public Criteria andSecondDirectoryIdGreaterThanColumn(ProductFlowInstanceSpu.Column column) {
            addCriterion(new StringBuilder("second_directory_id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSecondDirectoryIdGreaterThanOrEqualTo(String value) {
            addCriterion("second_directory_id >=", value, "secondDirectoryId");
            return (Criteria) this;
        }

        public Criteria andSecondDirectoryIdGreaterThanOrEqualToColumn(ProductFlowInstanceSpu.Column column) {
            addCriterion(new StringBuilder("second_directory_id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSecondDirectoryIdLessThan(String value) {
            addCriterion("second_directory_id <", value, "secondDirectoryId");
            return (Criteria) this;
        }

        public Criteria andSecondDirectoryIdLessThanColumn(ProductFlowInstanceSpu.Column column) {
            addCriterion(new StringBuilder("second_directory_id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSecondDirectoryIdLessThanOrEqualTo(String value) {
            addCriterion("second_directory_id <=", value, "secondDirectoryId");
            return (Criteria) this;
        }

        public Criteria andSecondDirectoryIdLessThanOrEqualToColumn(ProductFlowInstanceSpu.Column column) {
            addCriterion(new StringBuilder("second_directory_id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSecondDirectoryIdLike(String value) {
            addCriterion("second_directory_id like", value, "secondDirectoryId");
            return (Criteria) this;
        }

        public Criteria andSecondDirectoryIdNotLike(String value) {
            addCriterion("second_directory_id not like", value, "secondDirectoryId");
            return (Criteria) this;
        }

        public Criteria andSecondDirectoryIdIn(List<String> values) {
            addCriterion("second_directory_id in", values, "secondDirectoryId");
            return (Criteria) this;
        }

        public Criteria andSecondDirectoryIdNotIn(List<String> values) {
            addCriterion("second_directory_id not in", values, "secondDirectoryId");
            return (Criteria) this;
        }

        public Criteria andSecondDirectoryIdBetween(String value1, String value2) {
            addCriterion("second_directory_id between", value1, value2, "secondDirectoryId");
            return (Criteria) this;
        }

        public Criteria andSecondDirectoryIdNotBetween(String value1, String value2) {
            addCriterion("second_directory_id not between", value1, value2, "secondDirectoryId");
            return (Criteria) this;
        }

        public Criteria andSpuCodeIsNull() {
            addCriterion("spu_code is null");
            return (Criteria) this;
        }

        public Criteria andSpuCodeIsNotNull() {
            addCriterion("spu_code is not null");
            return (Criteria) this;
        }

        public Criteria andSpuCodeEqualTo(String value) {
            addCriterion("spu_code =", value, "spuCode");
            return (Criteria) this;
        }

        public Criteria andSpuCodeEqualToColumn(ProductFlowInstanceSpu.Column column) {
            addCriterion(new StringBuilder("spu_code = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpuCodeNotEqualTo(String value) {
            addCriterion("spu_code <>", value, "spuCode");
            return (Criteria) this;
        }

        public Criteria andSpuCodeNotEqualToColumn(ProductFlowInstanceSpu.Column column) {
            addCriterion(new StringBuilder("spu_code <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpuCodeGreaterThan(String value) {
            addCriterion("spu_code >", value, "spuCode");
            return (Criteria) this;
        }

        public Criteria andSpuCodeGreaterThanColumn(ProductFlowInstanceSpu.Column column) {
            addCriterion(new StringBuilder("spu_code > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpuCodeGreaterThanOrEqualTo(String value) {
            addCriterion("spu_code >=", value, "spuCode");
            return (Criteria) this;
        }

        public Criteria andSpuCodeGreaterThanOrEqualToColumn(ProductFlowInstanceSpu.Column column) {
            addCriterion(new StringBuilder("spu_code >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpuCodeLessThan(String value) {
            addCriterion("spu_code <", value, "spuCode");
            return (Criteria) this;
        }

        public Criteria andSpuCodeLessThanColumn(ProductFlowInstanceSpu.Column column) {
            addCriterion(new StringBuilder("spu_code < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpuCodeLessThanOrEqualTo(String value) {
            addCriterion("spu_code <=", value, "spuCode");
            return (Criteria) this;
        }

        public Criteria andSpuCodeLessThanOrEqualToColumn(ProductFlowInstanceSpu.Column column) {
            addCriterion(new StringBuilder("spu_code <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpuCodeLike(String value) {
            addCriterion("spu_code like", value, "spuCode");
            return (Criteria) this;
        }

        public Criteria andSpuCodeNotLike(String value) {
            addCriterion("spu_code not like", value, "spuCode");
            return (Criteria) this;
        }

        public Criteria andSpuCodeIn(List<String> values) {
            addCriterion("spu_code in", values, "spuCode");
            return (Criteria) this;
        }

        public Criteria andSpuCodeNotIn(List<String> values) {
            addCriterion("spu_code not in", values, "spuCode");
            return (Criteria) this;
        }

        public Criteria andSpuCodeBetween(String value1, String value2) {
            addCriterion("spu_code between", value1, value2, "spuCode");
            return (Criteria) this;
        }

        public Criteria andSpuCodeNotBetween(String value1, String value2) {
            addCriterion("spu_code not between", value1, value2, "spuCode");
            return (Criteria) this;
        }

        public Criteria andSpuNameIsNull() {
            addCriterion("spu_name is null");
            return (Criteria) this;
        }

        public Criteria andSpuNameIsNotNull() {
            addCriterion("spu_name is not null");
            return (Criteria) this;
        }

        public Criteria andSpuNameEqualTo(String value) {
            addCriterion("spu_name =", value, "spuName");
            return (Criteria) this;
        }

        public Criteria andSpuNameEqualToColumn(ProductFlowInstanceSpu.Column column) {
            addCriterion(new StringBuilder("spu_name = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpuNameNotEqualTo(String value) {
            addCriterion("spu_name <>", value, "spuName");
            return (Criteria) this;
        }

        public Criteria andSpuNameNotEqualToColumn(ProductFlowInstanceSpu.Column column) {
            addCriterion(new StringBuilder("spu_name <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpuNameGreaterThan(String value) {
            addCriterion("spu_name >", value, "spuName");
            return (Criteria) this;
        }

        public Criteria andSpuNameGreaterThanColumn(ProductFlowInstanceSpu.Column column) {
            addCriterion(new StringBuilder("spu_name > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpuNameGreaterThanOrEqualTo(String value) {
            addCriterion("spu_name >=", value, "spuName");
            return (Criteria) this;
        }

        public Criteria andSpuNameGreaterThanOrEqualToColumn(ProductFlowInstanceSpu.Column column) {
            addCriterion(new StringBuilder("spu_name >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpuNameLessThan(String value) {
            addCriterion("spu_name <", value, "spuName");
            return (Criteria) this;
        }

        public Criteria andSpuNameLessThanColumn(ProductFlowInstanceSpu.Column column) {
            addCriterion(new StringBuilder("spu_name < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpuNameLessThanOrEqualTo(String value) {
            addCriterion("spu_name <=", value, "spuName");
            return (Criteria) this;
        }

        public Criteria andSpuNameLessThanOrEqualToColumn(ProductFlowInstanceSpu.Column column) {
            addCriterion(new StringBuilder("spu_name <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpuNameLike(String value) {
            addCriterion("spu_name like", value, "spuName");
            return (Criteria) this;
        }

        public Criteria andSpuNameNotLike(String value) {
            addCriterion("spu_name not like", value, "spuName");
            return (Criteria) this;
        }

        public Criteria andSpuNameIn(List<String> values) {
            addCriterion("spu_name in", values, "spuName");
            return (Criteria) this;
        }

        public Criteria andSpuNameNotIn(List<String> values) {
            addCriterion("spu_name not in", values, "spuName");
            return (Criteria) this;
        }

        public Criteria andSpuNameBetween(String value1, String value2) {
            addCriterion("spu_name between", value1, value2, "spuName");
            return (Criteria) this;
        }

        public Criteria andSpuNameNotBetween(String value1, String value2) {
            addCriterion("spu_name not between", value1, value2, "spuName");
            return (Criteria) this;
        }

        public Criteria andProductStandardIsNull() {
            addCriterion("product_standard is null");
            return (Criteria) this;
        }

        public Criteria andProductStandardIsNotNull() {
            addCriterion("product_standard is not null");
            return (Criteria) this;
        }

        public Criteria andProductStandardEqualTo(Integer value) {
            addCriterion("product_standard =", value, "productStandard");
            return (Criteria) this;
        }

        public Criteria andProductStandardEqualToColumn(ProductFlowInstanceSpu.Column column) {
            addCriterion(new StringBuilder("product_standard = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductStandardNotEqualTo(Integer value) {
            addCriterion("product_standard <>", value, "productStandard");
            return (Criteria) this;
        }

        public Criteria andProductStandardNotEqualToColumn(ProductFlowInstanceSpu.Column column) {
            addCriterion(new StringBuilder("product_standard <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductStandardGreaterThan(Integer value) {
            addCriterion("product_standard >", value, "productStandard");
            return (Criteria) this;
        }

        public Criteria andProductStandardGreaterThanColumn(ProductFlowInstanceSpu.Column column) {
            addCriterion(new StringBuilder("product_standard > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductStandardGreaterThanOrEqualTo(Integer value) {
            addCriterion("product_standard >=", value, "productStandard");
            return (Criteria) this;
        }

        public Criteria andProductStandardGreaterThanOrEqualToColumn(ProductFlowInstanceSpu.Column column) {
            addCriterion(new StringBuilder("product_standard >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductStandardLessThan(Integer value) {
            addCriterion("product_standard <", value, "productStandard");
            return (Criteria) this;
        }

        public Criteria andProductStandardLessThanColumn(ProductFlowInstanceSpu.Column column) {
            addCriterion(new StringBuilder("product_standard < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductStandardLessThanOrEqualTo(Integer value) {
            addCriterion("product_standard <=", value, "productStandard");
            return (Criteria) this;
        }

        public Criteria andProductStandardLessThanOrEqualToColumn(ProductFlowInstanceSpu.Column column) {
            addCriterion(new StringBuilder("product_standard <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductStandardIn(List<Integer> values) {
            addCriterion("product_standard in", values, "productStandard");
            return (Criteria) this;
        }

        public Criteria andProductStandardNotIn(List<Integer> values) {
            addCriterion("product_standard not in", values, "productStandard");
            return (Criteria) this;
        }

        public Criteria andProductStandardBetween(Integer value1, Integer value2) {
            addCriterion("product_standard between", value1, value2, "productStandard");
            return (Criteria) this;
        }

        public Criteria andProductStandardNotBetween(Integer value1, Integer value2) {
            addCriterion("product_standard not between", value1, value2, "productStandard");
            return (Criteria) this;
        }

        public Criteria andProductTypeIsNull() {
            addCriterion("product_type is null");
            return (Criteria) this;
        }

        public Criteria andProductTypeIsNotNull() {
            addCriterion("product_type is not null");
            return (Criteria) this;
        }

        public Criteria andProductTypeEqualTo(Integer value) {
            addCriterion("product_type =", value, "productType");
            return (Criteria) this;
        }

        public Criteria andProductTypeEqualToColumn(ProductFlowInstanceSpu.Column column) {
            addCriterion(new StringBuilder("product_type = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductTypeNotEqualTo(Integer value) {
            addCriterion("product_type <>", value, "productType");
            return (Criteria) this;
        }

        public Criteria andProductTypeNotEqualToColumn(ProductFlowInstanceSpu.Column column) {
            addCriterion(new StringBuilder("product_type <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductTypeGreaterThan(Integer value) {
            addCriterion("product_type >", value, "productType");
            return (Criteria) this;
        }

        public Criteria andProductTypeGreaterThanColumn(ProductFlowInstanceSpu.Column column) {
            addCriterion(new StringBuilder("product_type > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("product_type >=", value, "productType");
            return (Criteria) this;
        }

        public Criteria andProductTypeGreaterThanOrEqualToColumn(ProductFlowInstanceSpu.Column column) {
            addCriterion(new StringBuilder("product_type >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductTypeLessThan(Integer value) {
            addCriterion("product_type <", value, "productType");
            return (Criteria) this;
        }

        public Criteria andProductTypeLessThanColumn(ProductFlowInstanceSpu.Column column) {
            addCriterion(new StringBuilder("product_type < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductTypeLessThanOrEqualTo(Integer value) {
            addCriterion("product_type <=", value, "productType");
            return (Criteria) this;
        }

        public Criteria andProductTypeLessThanOrEqualToColumn(ProductFlowInstanceSpu.Column column) {
            addCriterion(new StringBuilder("product_type <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductTypeIn(List<Integer> values) {
            addCriterion("product_type in", values, "productType");
            return (Criteria) this;
        }

        public Criteria andProductTypeNotIn(List<Integer> values) {
            addCriterion("product_type not in", values, "productType");
            return (Criteria) this;
        }

        public Criteria andProductTypeBetween(Integer value1, Integer value2) {
            addCriterion("product_type between", value1, value2, "productType");
            return (Criteria) this;
        }

        public Criteria andProductTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("product_type not between", value1, value2, "productType");
            return (Criteria) this;
        }

        public Criteria andUrlIsNull() {
            addCriterion("url is null");
            return (Criteria) this;
        }

        public Criteria andUrlIsNotNull() {
            addCriterion("url is not null");
            return (Criteria) this;
        }

        public Criteria andUrlEqualTo(String value) {
            addCriterion("url =", value, "url");
            return (Criteria) this;
        }

        public Criteria andUrlEqualToColumn(ProductFlowInstanceSpu.Column column) {
            addCriterion(new StringBuilder("url = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUrlNotEqualTo(String value) {
            addCriterion("url <>", value, "url");
            return (Criteria) this;
        }

        public Criteria andUrlNotEqualToColumn(ProductFlowInstanceSpu.Column column) {
            addCriterion(new StringBuilder("url <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUrlGreaterThan(String value) {
            addCriterion("url >", value, "url");
            return (Criteria) this;
        }

        public Criteria andUrlGreaterThanColumn(ProductFlowInstanceSpu.Column column) {
            addCriterion(new StringBuilder("url > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUrlGreaterThanOrEqualTo(String value) {
            addCriterion("url >=", value, "url");
            return (Criteria) this;
        }

        public Criteria andUrlGreaterThanOrEqualToColumn(ProductFlowInstanceSpu.Column column) {
            addCriterion(new StringBuilder("url >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUrlLessThan(String value) {
            addCriterion("url <", value, "url");
            return (Criteria) this;
        }

        public Criteria andUrlLessThanColumn(ProductFlowInstanceSpu.Column column) {
            addCriterion(new StringBuilder("url < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUrlLessThanOrEqualTo(String value) {
            addCriterion("url <=", value, "url");
            return (Criteria) this;
        }

        public Criteria andUrlLessThanOrEqualToColumn(ProductFlowInstanceSpu.Column column) {
            addCriterion(new StringBuilder("url <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUrlLike(String value) {
            addCriterion("url like", value, "url");
            return (Criteria) this;
        }

        public Criteria andUrlNotLike(String value) {
            addCriterion("url not like", value, "url");
            return (Criteria) this;
        }

        public Criteria andUrlIn(List<String> values) {
            addCriterion("url in", values, "url");
            return (Criteria) this;
        }

        public Criteria andUrlNotIn(List<String> values) {
            addCriterion("url not in", values, "url");
            return (Criteria) this;
        }

        public Criteria andUrlBetween(String value1, String value2) {
            addCriterion("url between", value1, value2, "url");
            return (Criteria) this;
        }

        public Criteria andUrlNotBetween(String value1, String value2) {
            addCriterion("url not between", value1, value2, "url");
            return (Criteria) this;
        }

        public Criteria andManagerIsNull() {
            addCriterion("manager is null");
            return (Criteria) this;
        }

        public Criteria andManagerIsNotNull() {
            addCriterion("manager is not null");
            return (Criteria) this;
        }

        public Criteria andManagerEqualTo(String value) {
            addCriterion("manager =", value, "manager");
            return (Criteria) this;
        }

        public Criteria andManagerEqualToColumn(ProductFlowInstanceSpu.Column column) {
            addCriterion(new StringBuilder("manager = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andManagerNotEqualTo(String value) {
            addCriterion("manager <>", value, "manager");
            return (Criteria) this;
        }

        public Criteria andManagerNotEqualToColumn(ProductFlowInstanceSpu.Column column) {
            addCriterion(new StringBuilder("manager <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andManagerGreaterThan(String value) {
            addCriterion("manager >", value, "manager");
            return (Criteria) this;
        }

        public Criteria andManagerGreaterThanColumn(ProductFlowInstanceSpu.Column column) {
            addCriterion(new StringBuilder("manager > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andManagerGreaterThanOrEqualTo(String value) {
            addCriterion("manager >=", value, "manager");
            return (Criteria) this;
        }

        public Criteria andManagerGreaterThanOrEqualToColumn(ProductFlowInstanceSpu.Column column) {
            addCriterion(new StringBuilder("manager >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andManagerLessThan(String value) {
            addCriterion("manager <", value, "manager");
            return (Criteria) this;
        }

        public Criteria andManagerLessThanColumn(ProductFlowInstanceSpu.Column column) {
            addCriterion(new StringBuilder("manager < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andManagerLessThanOrEqualTo(String value) {
            addCriterion("manager <=", value, "manager");
            return (Criteria) this;
        }

        public Criteria andManagerLessThanOrEqualToColumn(ProductFlowInstanceSpu.Column column) {
            addCriterion(new StringBuilder("manager <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andManagerLike(String value) {
            addCriterion("manager like", value, "manager");
            return (Criteria) this;
        }

        public Criteria andManagerNotLike(String value) {
            addCriterion("manager not like", value, "manager");
            return (Criteria) this;
        }

        public Criteria andManagerIn(List<String> values) {
            addCriterion("manager in", values, "manager");
            return (Criteria) this;
        }

        public Criteria andManagerNotIn(List<String> values) {
            addCriterion("manager not in", values, "manager");
            return (Criteria) this;
        }

        public Criteria andManagerBetween(String value1, String value2) {
            addCriterion("manager between", value1, value2, "manager");
            return (Criteria) this;
        }

        public Criteria andManagerNotBetween(String value1, String value2) {
            addCriterion("manager not between", value1, value2, "manager");
            return (Criteria) this;
        }

        public Criteria andProductDescIsNull() {
            addCriterion("product_desc is null");
            return (Criteria) this;
        }

        public Criteria andProductDescIsNotNull() {
            addCriterion("product_desc is not null");
            return (Criteria) this;
        }

        public Criteria andProductDescEqualTo(String value) {
            addCriterion("product_desc =", value, "productDesc");
            return (Criteria) this;
        }

        public Criteria andProductDescEqualToColumn(ProductFlowInstanceSpu.Column column) {
            addCriterion(new StringBuilder("product_desc = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductDescNotEqualTo(String value) {
            addCriterion("product_desc <>", value, "productDesc");
            return (Criteria) this;
        }

        public Criteria andProductDescNotEqualToColumn(ProductFlowInstanceSpu.Column column) {
            addCriterion(new StringBuilder("product_desc <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductDescGreaterThan(String value) {
            addCriterion("product_desc >", value, "productDesc");
            return (Criteria) this;
        }

        public Criteria andProductDescGreaterThanColumn(ProductFlowInstanceSpu.Column column) {
            addCriterion(new StringBuilder("product_desc > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductDescGreaterThanOrEqualTo(String value) {
            addCriterion("product_desc >=", value, "productDesc");
            return (Criteria) this;
        }

        public Criteria andProductDescGreaterThanOrEqualToColumn(ProductFlowInstanceSpu.Column column) {
            addCriterion(new StringBuilder("product_desc >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductDescLessThan(String value) {
            addCriterion("product_desc <", value, "productDesc");
            return (Criteria) this;
        }

        public Criteria andProductDescLessThanColumn(ProductFlowInstanceSpu.Column column) {
            addCriterion(new StringBuilder("product_desc < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductDescLessThanOrEqualTo(String value) {
            addCriterion("product_desc <=", value, "productDesc");
            return (Criteria) this;
        }

        public Criteria andProductDescLessThanOrEqualToColumn(ProductFlowInstanceSpu.Column column) {
            addCriterion(new StringBuilder("product_desc <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductDescLike(String value) {
            addCriterion("product_desc like", value, "productDesc");
            return (Criteria) this;
        }

        public Criteria andProductDescNotLike(String value) {
            addCriterion("product_desc not like", value, "productDesc");
            return (Criteria) this;
        }

        public Criteria andProductDescIn(List<String> values) {
            addCriterion("product_desc in", values, "productDesc");
            return (Criteria) this;
        }

        public Criteria andProductDescNotIn(List<String> values) {
            addCriterion("product_desc not in", values, "productDesc");
            return (Criteria) this;
        }

        public Criteria andProductDescBetween(String value1, String value2) {
            addCriterion("product_desc between", value1, value2, "productDesc");
            return (Criteria) this;
        }

        public Criteria andProductDescNotBetween(String value1, String value2) {
            addCriterion("product_desc not between", value1, value2, "productDesc");
            return (Criteria) this;
        }

        public Criteria andApplicationAreaIsNull() {
            addCriterion("application_area is null");
            return (Criteria) this;
        }

        public Criteria andApplicationAreaIsNotNull() {
            addCriterion("application_area is not null");
            return (Criteria) this;
        }

        public Criteria andApplicationAreaEqualTo(String value) {
            addCriterion("application_area =", value, "applicationArea");
            return (Criteria) this;
        }

        public Criteria andApplicationAreaEqualToColumn(ProductFlowInstanceSpu.Column column) {
            addCriterion(new StringBuilder("application_area = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andApplicationAreaNotEqualTo(String value) {
            addCriterion("application_area <>", value, "applicationArea");
            return (Criteria) this;
        }

        public Criteria andApplicationAreaNotEqualToColumn(ProductFlowInstanceSpu.Column column) {
            addCriterion(new StringBuilder("application_area <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andApplicationAreaGreaterThan(String value) {
            addCriterion("application_area >", value, "applicationArea");
            return (Criteria) this;
        }

        public Criteria andApplicationAreaGreaterThanColumn(ProductFlowInstanceSpu.Column column) {
            addCriterion(new StringBuilder("application_area > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andApplicationAreaGreaterThanOrEqualTo(String value) {
            addCriterion("application_area >=", value, "applicationArea");
            return (Criteria) this;
        }

        public Criteria andApplicationAreaGreaterThanOrEqualToColumn(ProductFlowInstanceSpu.Column column) {
            addCriterion(new StringBuilder("application_area >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andApplicationAreaLessThan(String value) {
            addCriterion("application_area <", value, "applicationArea");
            return (Criteria) this;
        }

        public Criteria andApplicationAreaLessThanColumn(ProductFlowInstanceSpu.Column column) {
            addCriterion(new StringBuilder("application_area < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andApplicationAreaLessThanOrEqualTo(String value) {
            addCriterion("application_area <=", value, "applicationArea");
            return (Criteria) this;
        }

        public Criteria andApplicationAreaLessThanOrEqualToColumn(ProductFlowInstanceSpu.Column column) {
            addCriterion(new StringBuilder("application_area <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andApplicationAreaLike(String value) {
            addCriterion("application_area like", value, "applicationArea");
            return (Criteria) this;
        }

        public Criteria andApplicationAreaNotLike(String value) {
            addCriterion("application_area not like", value, "applicationArea");
            return (Criteria) this;
        }

        public Criteria andApplicationAreaIn(List<String> values) {
            addCriterion("application_area in", values, "applicationArea");
            return (Criteria) this;
        }

        public Criteria andApplicationAreaNotIn(List<String> values) {
            addCriterion("application_area not in", values, "applicationArea");
            return (Criteria) this;
        }

        public Criteria andApplicationAreaBetween(String value1, String value2) {
            addCriterion("application_area between", value1, value2, "applicationArea");
            return (Criteria) this;
        }

        public Criteria andApplicationAreaNotBetween(String value1, String value2) {
            addCriterion("application_area not between", value1, value2, "applicationArea");
            return (Criteria) this;
        }

        public Criteria andIsHiddenShelfIsNull() {
            addCriterion("is_hidden_shelf is null");
            return (Criteria) this;
        }

        public Criteria andIsHiddenShelfIsNotNull() {
            addCriterion("is_hidden_shelf is not null");
            return (Criteria) this;
        }

        public Criteria andIsHiddenShelfEqualTo(String value) {
            addCriterion("is_hidden_shelf =", value, "isHiddenShelf");
            return (Criteria) this;
        }

        public Criteria andIsHiddenShelfEqualToColumn(ProductFlowInstanceSpu.Column column) {
            addCriterion(new StringBuilder("is_hidden_shelf = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIsHiddenShelfNotEqualTo(String value) {
            addCriterion("is_hidden_shelf <>", value, "isHiddenShelf");
            return (Criteria) this;
        }

        public Criteria andIsHiddenShelfNotEqualToColumn(ProductFlowInstanceSpu.Column column) {
            addCriterion(new StringBuilder("is_hidden_shelf <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIsHiddenShelfGreaterThan(String value) {
            addCriterion("is_hidden_shelf >", value, "isHiddenShelf");
            return (Criteria) this;
        }

        public Criteria andIsHiddenShelfGreaterThanColumn(ProductFlowInstanceSpu.Column column) {
            addCriterion(new StringBuilder("is_hidden_shelf > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIsHiddenShelfGreaterThanOrEqualTo(String value) {
            addCriterion("is_hidden_shelf >=", value, "isHiddenShelf");
            return (Criteria) this;
        }

        public Criteria andIsHiddenShelfGreaterThanOrEqualToColumn(ProductFlowInstanceSpu.Column column) {
            addCriterion(new StringBuilder("is_hidden_shelf >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIsHiddenShelfLessThan(String value) {
            addCriterion("is_hidden_shelf <", value, "isHiddenShelf");
            return (Criteria) this;
        }

        public Criteria andIsHiddenShelfLessThanColumn(ProductFlowInstanceSpu.Column column) {
            addCriterion(new StringBuilder("is_hidden_shelf < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIsHiddenShelfLessThanOrEqualTo(String value) {
            addCriterion("is_hidden_shelf <=", value, "isHiddenShelf");
            return (Criteria) this;
        }

        public Criteria andIsHiddenShelfLessThanOrEqualToColumn(ProductFlowInstanceSpu.Column column) {
            addCriterion(new StringBuilder("is_hidden_shelf <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIsHiddenShelfLike(String value) {
            addCriterion("is_hidden_shelf like", value, "isHiddenShelf");
            return (Criteria) this;
        }

        public Criteria andIsHiddenShelfNotLike(String value) {
            addCriterion("is_hidden_shelf not like", value, "isHiddenShelf");
            return (Criteria) this;
        }

        public Criteria andIsHiddenShelfIn(List<String> values) {
            addCriterion("is_hidden_shelf in", values, "isHiddenShelf");
            return (Criteria) this;
        }

        public Criteria andIsHiddenShelfNotIn(List<String> values) {
            addCriterion("is_hidden_shelf not in", values, "isHiddenShelf");
            return (Criteria) this;
        }

        public Criteria andIsHiddenShelfBetween(String value1, String value2) {
            addCriterion("is_hidden_shelf between", value1, value2, "isHiddenShelf");
            return (Criteria) this;
        }

        public Criteria andIsHiddenShelfNotBetween(String value1, String value2) {
            addCriterion("is_hidden_shelf not between", value1, value2, "isHiddenShelf");
            return (Criteria) this;
        }

        public Criteria andSpuServiceProviderIsNull() {
            addCriterion("spu_service_provider is null");
            return (Criteria) this;
        }

        public Criteria andSpuServiceProviderIsNotNull() {
            addCriterion("spu_service_provider is not null");
            return (Criteria) this;
        }

        public Criteria andSpuServiceProviderEqualTo(String value) {
            addCriterion("spu_service_provider =", value, "spuServiceProvider");
            return (Criteria) this;
        }

        public Criteria andSpuServiceProviderEqualToColumn(ProductFlowInstanceSpu.Column column) {
            addCriterion(new StringBuilder("spu_service_provider = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpuServiceProviderNotEqualTo(String value) {
            addCriterion("spu_service_provider <>", value, "spuServiceProvider");
            return (Criteria) this;
        }

        public Criteria andSpuServiceProviderNotEqualToColumn(ProductFlowInstanceSpu.Column column) {
            addCriterion(new StringBuilder("spu_service_provider <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpuServiceProviderGreaterThan(String value) {
            addCriterion("spu_service_provider >", value, "spuServiceProvider");
            return (Criteria) this;
        }

        public Criteria andSpuServiceProviderGreaterThanColumn(ProductFlowInstanceSpu.Column column) {
            addCriterion(new StringBuilder("spu_service_provider > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpuServiceProviderGreaterThanOrEqualTo(String value) {
            addCriterion("spu_service_provider >=", value, "spuServiceProvider");
            return (Criteria) this;
        }

        public Criteria andSpuServiceProviderGreaterThanOrEqualToColumn(ProductFlowInstanceSpu.Column column) {
            addCriterion(new StringBuilder("spu_service_provider >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpuServiceProviderLessThan(String value) {
            addCriterion("spu_service_provider <", value, "spuServiceProvider");
            return (Criteria) this;
        }

        public Criteria andSpuServiceProviderLessThanColumn(ProductFlowInstanceSpu.Column column) {
            addCriterion(new StringBuilder("spu_service_provider < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpuServiceProviderLessThanOrEqualTo(String value) {
            addCriterion("spu_service_provider <=", value, "spuServiceProvider");
            return (Criteria) this;
        }

        public Criteria andSpuServiceProviderLessThanOrEqualToColumn(ProductFlowInstanceSpu.Column column) {
            addCriterion(new StringBuilder("spu_service_provider <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpuServiceProviderLike(String value) {
            addCriterion("spu_service_provider like", value, "spuServiceProvider");
            return (Criteria) this;
        }

        public Criteria andSpuServiceProviderNotLike(String value) {
            addCriterion("spu_service_provider not like", value, "spuServiceProvider");
            return (Criteria) this;
        }

        public Criteria andSpuServiceProviderIn(List<String> values) {
            addCriterion("spu_service_provider in", values, "spuServiceProvider");
            return (Criteria) this;
        }

        public Criteria andSpuServiceProviderNotIn(List<String> values) {
            addCriterion("spu_service_provider not in", values, "spuServiceProvider");
            return (Criteria) this;
        }

        public Criteria andSpuServiceProviderBetween(String value1, String value2) {
            addCriterion("spu_service_provider between", value1, value2, "spuServiceProvider");
            return (Criteria) this;
        }

        public Criteria andSpuServiceProviderNotBetween(String value1, String value2) {
            addCriterion("spu_service_provider not between", value1, value2, "spuServiceProvider");
            return (Criteria) this;
        }

        public Criteria andSaleTagIsNull() {
            addCriterion("sale_tag is null");
            return (Criteria) this;
        }

        public Criteria andSaleTagIsNotNull() {
            addCriterion("sale_tag is not null");
            return (Criteria) this;
        }

        public Criteria andSaleTagEqualTo(String value) {
            addCriterion("sale_tag =", value, "saleTag");
            return (Criteria) this;
        }

        public Criteria andSaleTagEqualToColumn(ProductFlowInstanceSpu.Column column) {
            addCriterion(new StringBuilder("sale_tag = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSaleTagNotEqualTo(String value) {
            addCriterion("sale_tag <>", value, "saleTag");
            return (Criteria) this;
        }

        public Criteria andSaleTagNotEqualToColumn(ProductFlowInstanceSpu.Column column) {
            addCriterion(new StringBuilder("sale_tag <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSaleTagGreaterThan(String value) {
            addCriterion("sale_tag >", value, "saleTag");
            return (Criteria) this;
        }

        public Criteria andSaleTagGreaterThanColumn(ProductFlowInstanceSpu.Column column) {
            addCriterion(new StringBuilder("sale_tag > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSaleTagGreaterThanOrEqualTo(String value) {
            addCriterion("sale_tag >=", value, "saleTag");
            return (Criteria) this;
        }

        public Criteria andSaleTagGreaterThanOrEqualToColumn(ProductFlowInstanceSpu.Column column) {
            addCriterion(new StringBuilder("sale_tag >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSaleTagLessThan(String value) {
            addCriterion("sale_tag <", value, "saleTag");
            return (Criteria) this;
        }

        public Criteria andSaleTagLessThanColumn(ProductFlowInstanceSpu.Column column) {
            addCriterion(new StringBuilder("sale_tag < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSaleTagLessThanOrEqualTo(String value) {
            addCriterion("sale_tag <=", value, "saleTag");
            return (Criteria) this;
        }

        public Criteria andSaleTagLessThanOrEqualToColumn(ProductFlowInstanceSpu.Column column) {
            addCriterion(new StringBuilder("sale_tag <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSaleTagLike(String value) {
            addCriterion("sale_tag like", value, "saleTag");
            return (Criteria) this;
        }

        public Criteria andSaleTagNotLike(String value) {
            addCriterion("sale_tag not like", value, "saleTag");
            return (Criteria) this;
        }

        public Criteria andSaleTagIn(List<String> values) {
            addCriterion("sale_tag in", values, "saleTag");
            return (Criteria) this;
        }

        public Criteria andSaleTagNotIn(List<String> values) {
            addCriterion("sale_tag not in", values, "saleTag");
            return (Criteria) this;
        }

        public Criteria andSaleTagBetween(String value1, String value2) {
            addCriterion("sale_tag between", value1, value2, "saleTag");
            return (Criteria) this;
        }

        public Criteria andSaleTagNotBetween(String value1, String value2) {
            addCriterion("sale_tag not between", value1, value2, "saleTag");
            return (Criteria) this;
        }

        public Criteria andSearchWordIsNull() {
            addCriterion("search_word is null");
            return (Criteria) this;
        }

        public Criteria andSearchWordIsNotNull() {
            addCriterion("search_word is not null");
            return (Criteria) this;
        }

        public Criteria andSearchWordEqualTo(String value) {
            addCriterion("search_word =", value, "searchWord");
            return (Criteria) this;
        }

        public Criteria andSearchWordEqualToColumn(ProductFlowInstanceSpu.Column column) {
            addCriterion(new StringBuilder("search_word = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSearchWordNotEqualTo(String value) {
            addCriterion("search_word <>", value, "searchWord");
            return (Criteria) this;
        }

        public Criteria andSearchWordNotEqualToColumn(ProductFlowInstanceSpu.Column column) {
            addCriterion(new StringBuilder("search_word <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSearchWordGreaterThan(String value) {
            addCriterion("search_word >", value, "searchWord");
            return (Criteria) this;
        }

        public Criteria andSearchWordGreaterThanColumn(ProductFlowInstanceSpu.Column column) {
            addCriterion(new StringBuilder("search_word > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSearchWordGreaterThanOrEqualTo(String value) {
            addCriterion("search_word >=", value, "searchWord");
            return (Criteria) this;
        }

        public Criteria andSearchWordGreaterThanOrEqualToColumn(ProductFlowInstanceSpu.Column column) {
            addCriterion(new StringBuilder("search_word >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSearchWordLessThan(String value) {
            addCriterion("search_word <", value, "searchWord");
            return (Criteria) this;
        }

        public Criteria andSearchWordLessThanColumn(ProductFlowInstanceSpu.Column column) {
            addCriterion(new StringBuilder("search_word < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSearchWordLessThanOrEqualTo(String value) {
            addCriterion("search_word <=", value, "searchWord");
            return (Criteria) this;
        }

        public Criteria andSearchWordLessThanOrEqualToColumn(ProductFlowInstanceSpu.Column column) {
            addCriterion(new StringBuilder("search_word <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSearchWordLike(String value) {
            addCriterion("search_word like", value, "searchWord");
            return (Criteria) this;
        }

        public Criteria andSearchWordNotLike(String value) {
            addCriterion("search_word not like", value, "searchWord");
            return (Criteria) this;
        }

        public Criteria andSearchWordIn(List<String> values) {
            addCriterion("search_word in", values, "searchWord");
            return (Criteria) this;
        }

        public Criteria andSearchWordNotIn(List<String> values) {
            addCriterion("search_word not in", values, "searchWord");
            return (Criteria) this;
        }

        public Criteria andSearchWordBetween(String value1, String value2) {
            addCriterion("search_word between", value1, value2, "searchWord");
            return (Criteria) this;
        }

        public Criteria andSearchWordNotBetween(String value1, String value2) {
            addCriterion("search_word not between", value1, value2, "searchWord");
            return (Criteria) this;
        }

        public Criteria andSpuRemarkIsNull() {
            addCriterion("spu_remark is null");
            return (Criteria) this;
        }

        public Criteria andSpuRemarkIsNotNull() {
            addCriterion("spu_remark is not null");
            return (Criteria) this;
        }

        public Criteria andSpuRemarkEqualTo(String value) {
            addCriterion("spu_remark =", value, "spuRemark");
            return (Criteria) this;
        }

        public Criteria andSpuRemarkEqualToColumn(ProductFlowInstanceSpu.Column column) {
            addCriterion(new StringBuilder("spu_remark = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpuRemarkNotEqualTo(String value) {
            addCriterion("spu_remark <>", value, "spuRemark");
            return (Criteria) this;
        }

        public Criteria andSpuRemarkNotEqualToColumn(ProductFlowInstanceSpu.Column column) {
            addCriterion(new StringBuilder("spu_remark <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpuRemarkGreaterThan(String value) {
            addCriterion("spu_remark >", value, "spuRemark");
            return (Criteria) this;
        }

        public Criteria andSpuRemarkGreaterThanColumn(ProductFlowInstanceSpu.Column column) {
            addCriterion(new StringBuilder("spu_remark > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpuRemarkGreaterThanOrEqualTo(String value) {
            addCriterion("spu_remark >=", value, "spuRemark");
            return (Criteria) this;
        }

        public Criteria andSpuRemarkGreaterThanOrEqualToColumn(ProductFlowInstanceSpu.Column column) {
            addCriterion(new StringBuilder("spu_remark >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpuRemarkLessThan(String value) {
            addCriterion("spu_remark <", value, "spuRemark");
            return (Criteria) this;
        }

        public Criteria andSpuRemarkLessThanColumn(ProductFlowInstanceSpu.Column column) {
            addCriterion(new StringBuilder("spu_remark < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpuRemarkLessThanOrEqualTo(String value) {
            addCriterion("spu_remark <=", value, "spuRemark");
            return (Criteria) this;
        }

        public Criteria andSpuRemarkLessThanOrEqualToColumn(ProductFlowInstanceSpu.Column column) {
            addCriterion(new StringBuilder("spu_remark <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpuRemarkLike(String value) {
            addCriterion("spu_remark like", value, "spuRemark");
            return (Criteria) this;
        }

        public Criteria andSpuRemarkNotLike(String value) {
            addCriterion("spu_remark not like", value, "spuRemark");
            return (Criteria) this;
        }

        public Criteria andSpuRemarkIn(List<String> values) {
            addCriterion("spu_remark in", values, "spuRemark");
            return (Criteria) this;
        }

        public Criteria andSpuRemarkNotIn(List<String> values) {
            addCriterion("spu_remark not in", values, "spuRemark");
            return (Criteria) this;
        }

        public Criteria andSpuRemarkBetween(String value1, String value2) {
            addCriterion("spu_remark between", value1, value2, "spuRemark");
            return (Criteria) this;
        }

        public Criteria andSpuRemarkNotBetween(String value1, String value2) {
            addCriterion("spu_remark not between", value1, value2, "spuRemark");
            return (Criteria) this;
        }

        public Criteria andAftermarketAdminInfoIsNull() {
            addCriterion("aftermarket_admin_info is null");
            return (Criteria) this;
        }

        public Criteria andAftermarketAdminInfoIsNotNull() {
            addCriterion("aftermarket_admin_info is not null");
            return (Criteria) this;
        }

        public Criteria andAftermarketAdminInfoEqualTo(String value) {
            addCriterion("aftermarket_admin_info =", value, "aftermarketAdminInfo");
            return (Criteria) this;
        }

        public Criteria andAftermarketAdminInfoEqualToColumn(ProductFlowInstanceSpu.Column column) {
            addCriterion(new StringBuilder("aftermarket_admin_info = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAftermarketAdminInfoNotEqualTo(String value) {
            addCriterion("aftermarket_admin_info <>", value, "aftermarketAdminInfo");
            return (Criteria) this;
        }

        public Criteria andAftermarketAdminInfoNotEqualToColumn(ProductFlowInstanceSpu.Column column) {
            addCriterion(new StringBuilder("aftermarket_admin_info <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAftermarketAdminInfoGreaterThan(String value) {
            addCriterion("aftermarket_admin_info >", value, "aftermarketAdminInfo");
            return (Criteria) this;
        }

        public Criteria andAftermarketAdminInfoGreaterThanColumn(ProductFlowInstanceSpu.Column column) {
            addCriterion(new StringBuilder("aftermarket_admin_info > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAftermarketAdminInfoGreaterThanOrEqualTo(String value) {
            addCriterion("aftermarket_admin_info >=", value, "aftermarketAdminInfo");
            return (Criteria) this;
        }

        public Criteria andAftermarketAdminInfoGreaterThanOrEqualToColumn(ProductFlowInstanceSpu.Column column) {
            addCriterion(new StringBuilder("aftermarket_admin_info >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAftermarketAdminInfoLessThan(String value) {
            addCriterion("aftermarket_admin_info <", value, "aftermarketAdminInfo");
            return (Criteria) this;
        }

        public Criteria andAftermarketAdminInfoLessThanColumn(ProductFlowInstanceSpu.Column column) {
            addCriterion(new StringBuilder("aftermarket_admin_info < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAftermarketAdminInfoLessThanOrEqualTo(String value) {
            addCriterion("aftermarket_admin_info <=", value, "aftermarketAdminInfo");
            return (Criteria) this;
        }

        public Criteria andAftermarketAdminInfoLessThanOrEqualToColumn(ProductFlowInstanceSpu.Column column) {
            addCriterion(new StringBuilder("aftermarket_admin_info <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAftermarketAdminInfoLike(String value) {
            addCriterion("aftermarket_admin_info like", value, "aftermarketAdminInfo");
            return (Criteria) this;
        }

        public Criteria andAftermarketAdminInfoNotLike(String value) {
            addCriterion("aftermarket_admin_info not like", value, "aftermarketAdminInfo");
            return (Criteria) this;
        }

        public Criteria andAftermarketAdminInfoIn(List<String> values) {
            addCriterion("aftermarket_admin_info in", values, "aftermarketAdminInfo");
            return (Criteria) this;
        }

        public Criteria andAftermarketAdminInfoNotIn(List<String> values) {
            addCriterion("aftermarket_admin_info not in", values, "aftermarketAdminInfo");
            return (Criteria) this;
        }

        public Criteria andAftermarketAdminInfoBetween(String value1, String value2) {
            addCriterion("aftermarket_admin_info between", value1, value2, "aftermarketAdminInfo");
            return (Criteria) this;
        }

        public Criteria andAftermarketAdminInfoNotBetween(String value1, String value2) {
            addCriterion("aftermarket_admin_info not between", value1, value2, "aftermarketAdminInfo");
            return (Criteria) this;
        }

        public Criteria andManageDepartmentIsNull() {
            addCriterion("manage_department is null");
            return (Criteria) this;
        }

        public Criteria andManageDepartmentIsNotNull() {
            addCriterion("manage_department is not null");
            return (Criteria) this;
        }

        public Criteria andManageDepartmentEqualTo(String value) {
            addCriterion("manage_department =", value, "manageDepartment");
            return (Criteria) this;
        }

        public Criteria andManageDepartmentEqualToColumn(ProductFlowInstanceSpu.Column column) {
            addCriterion(new StringBuilder("manage_department = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andManageDepartmentNotEqualTo(String value) {
            addCriterion("manage_department <>", value, "manageDepartment");
            return (Criteria) this;
        }

        public Criteria andManageDepartmentNotEqualToColumn(ProductFlowInstanceSpu.Column column) {
            addCriterion(new StringBuilder("manage_department <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andManageDepartmentGreaterThan(String value) {
            addCriterion("manage_department >", value, "manageDepartment");
            return (Criteria) this;
        }

        public Criteria andManageDepartmentGreaterThanColumn(ProductFlowInstanceSpu.Column column) {
            addCriterion(new StringBuilder("manage_department > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andManageDepartmentGreaterThanOrEqualTo(String value) {
            addCriterion("manage_department >=", value, "manageDepartment");
            return (Criteria) this;
        }

        public Criteria andManageDepartmentGreaterThanOrEqualToColumn(ProductFlowInstanceSpu.Column column) {
            addCriterion(new StringBuilder("manage_department >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andManageDepartmentLessThan(String value) {
            addCriterion("manage_department <", value, "manageDepartment");
            return (Criteria) this;
        }

        public Criteria andManageDepartmentLessThanColumn(ProductFlowInstanceSpu.Column column) {
            addCriterion(new StringBuilder("manage_department < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andManageDepartmentLessThanOrEqualTo(String value) {
            addCriterion("manage_department <=", value, "manageDepartment");
            return (Criteria) this;
        }

        public Criteria andManageDepartmentLessThanOrEqualToColumn(ProductFlowInstanceSpu.Column column) {
            addCriterion(new StringBuilder("manage_department <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andManageDepartmentLike(String value) {
            addCriterion("manage_department like", value, "manageDepartment");
            return (Criteria) this;
        }

        public Criteria andManageDepartmentNotLike(String value) {
            addCriterion("manage_department not like", value, "manageDepartment");
            return (Criteria) this;
        }

        public Criteria andManageDepartmentIn(List<String> values) {
            addCriterion("manage_department in", values, "manageDepartment");
            return (Criteria) this;
        }

        public Criteria andManageDepartmentNotIn(List<String> values) {
            addCriterion("manage_department not in", values, "manageDepartment");
            return (Criteria) this;
        }

        public Criteria andManageDepartmentBetween(String value1, String value2) {
            addCriterion("manage_department between", value1, value2, "manageDepartment");
            return (Criteria) this;
        }

        public Criteria andManageDepartmentNotBetween(String value1, String value2) {
            addCriterion("manage_department not between", value1, value2, "manageDepartment");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualToColumn(ProductFlowInstanceSpu.Column column) {
            addCriterion(new StringBuilder("create_time = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualToColumn(ProductFlowInstanceSpu.Column column) {
            addCriterion(new StringBuilder("create_time <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanColumn(ProductFlowInstanceSpu.Column column) {
            addCriterion(new StringBuilder("create_time > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualToColumn(ProductFlowInstanceSpu.Column column) {
            addCriterion(new StringBuilder("create_time >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanColumn(ProductFlowInstanceSpu.Column column) {
            addCriterion(new StringBuilder("create_time < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualToColumn(ProductFlowInstanceSpu.Column column) {
            addCriterion(new StringBuilder("create_time <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualToColumn(ProductFlowInstanceSpu.Column column) {
            addCriterion(new StringBuilder("update_time = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualToColumn(ProductFlowInstanceSpu.Column column) {
            addCriterion(new StringBuilder("update_time <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanColumn(ProductFlowInstanceSpu.Column column) {
            addCriterion(new StringBuilder("update_time > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualToColumn(ProductFlowInstanceSpu.Column column) {
            addCriterion(new StringBuilder("update_time >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanColumn(ProductFlowInstanceSpu.Column column) {
            addCriterion(new StringBuilder("update_time < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualToColumn(ProductFlowInstanceSpu.Column column) {
            addCriterion(new StringBuilder("update_time <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andIdLikeInsensitive(String value) {
            addCriterion("upper(id) like", value.toUpperCase(), "id");
            return (Criteria) this;
        }

        public Criteria andFlowInstanceIdLikeInsensitive(String value) {
            addCriterion("upper(flow_instance_id) like", value.toUpperCase(), "flowInstanceId");
            return (Criteria) this;
        }

        public Criteria andFlowIdLikeInsensitive(String value) {
            addCriterion("upper(flow_id) like", value.toUpperCase(), "flowId");
            return (Criteria) this;
        }

        public Criteria andShelfCatagoryIdLikeInsensitive(String value) {
            addCriterion("upper(shelf_catagory_id) like", value.toUpperCase(), "shelfCatagoryId");
            return (Criteria) this;
        }

        public Criteria andFirstDirectoryIdLikeInsensitive(String value) {
            addCriterion("upper(first_directory_id) like", value.toUpperCase(), "firstDirectoryId");
            return (Criteria) this;
        }

        public Criteria andSecondDirectoryIdLikeInsensitive(String value) {
            addCriterion("upper(second_directory_id) like", value.toUpperCase(), "secondDirectoryId");
            return (Criteria) this;
        }

        public Criteria andSpuCodeLikeInsensitive(String value) {
            addCriterion("upper(spu_code) like", value.toUpperCase(), "spuCode");
            return (Criteria) this;
        }

        public Criteria andSpuNameLikeInsensitive(String value) {
            addCriterion("upper(spu_name) like", value.toUpperCase(), "spuName");
            return (Criteria) this;
        }

        public Criteria andUrlLikeInsensitive(String value) {
            addCriterion("upper(url) like", value.toUpperCase(), "url");
            return (Criteria) this;
        }

        public Criteria andManagerLikeInsensitive(String value) {
            addCriterion("upper(manager) like", value.toUpperCase(), "manager");
            return (Criteria) this;
        }

        public Criteria andProductDescLikeInsensitive(String value) {
            addCriterion("upper(product_desc) like", value.toUpperCase(), "productDesc");
            return (Criteria) this;
        }

        public Criteria andApplicationAreaLikeInsensitive(String value) {
            addCriterion("upper(application_area) like", value.toUpperCase(), "applicationArea");
            return (Criteria) this;
        }

        public Criteria andIsHiddenShelfLikeInsensitive(String value) {
            addCriterion("upper(is_hidden_shelf) like", value.toUpperCase(), "isHiddenShelf");
            return (Criteria) this;
        }

        public Criteria andSpuServiceProviderLikeInsensitive(String value) {
            addCriterion("upper(spu_service_provider) like", value.toUpperCase(), "spuServiceProvider");
            return (Criteria) this;
        }

        public Criteria andSaleTagLikeInsensitive(String value) {
            addCriterion("upper(sale_tag) like", value.toUpperCase(), "saleTag");
            return (Criteria) this;
        }

        public Criteria andSearchWordLikeInsensitive(String value) {
            addCriterion("upper(search_word) like", value.toUpperCase(), "searchWord");
            return (Criteria) this;
        }

        public Criteria andSpuRemarkLikeInsensitive(String value) {
            addCriterion("upper(spu_remark) like", value.toUpperCase(), "spuRemark");
            return (Criteria) this;
        }

        public Criteria andAftermarketAdminInfoLikeInsensitive(String value) {
            addCriterion("upper(aftermarket_admin_info) like", value.toUpperCase(), "aftermarketAdminInfo");
            return (Criteria) this;
        }

        public Criteria andManageDepartmentLikeInsensitive(String value) {
            addCriterion("upper(manage_department) like", value.toUpperCase(), "manageDepartment");
            return (Criteria) this;
        }
    }

    /**
     *
     * @mbg.generated do_not_delete_during_merge Tue Jun 04 15:44:18 CST 2024
     */
    public static class Criteria extends GeneratedCriteria {
        /**
         *
         * @mbg.generated Tue Jun 04 15:44:18 CST 2024
         */
        private ProductFlowInstanceSpuExample example;

        /**
         *
         * @mbg.generated Tue Jun 04 15:44:18 CST 2024
         */
        protected Criteria(ProductFlowInstanceSpuExample example) {
            super();
            this.example = example;
        }

        /**
         *
         * @mbg.generated Tue Jun 04 15:44:18 CST 2024
         */
        public ProductFlowInstanceSpuExample example() {
            return this.example;
        }

        /**
         *
         * @mbg.generated Tue Jun 04 15:44:18 CST 2024
         */
        @Deprecated
        public Criteria andIf(boolean ifAdd, ICriteriaAdd add) {
            if (ifAdd) {
                add.add(this);
            }
            return this;
        }

        /**
         *
         * @mbg.generated Tue Jun 04 15:44:18 CST 2024
         */
        public Criteria when(boolean condition, ICriteriaWhen then) {
            if (condition) {
                then.criteria(this);
            }
            return this;
        }

        /**
         *
         * @mbg.generated Tue Jun 04 15:44:18 CST 2024
         */
        public Criteria when(boolean condition, ICriteriaWhen then, ICriteriaWhen otherwise) {
            if (condition) {
                then.criteria(this);
            } else {
                otherwise.criteria(this);
            }
            return this;
        }

        @Deprecated
        public interface ICriteriaAdd {
            /**
             *
             * @mbg.generated Tue Jun 04 15:44:18 CST 2024
             */
            Criteria add(Criteria add);
        }
    }

    /**
     *
     * @mbg.generated Tue Jun 04 15:44:18 CST 2024
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }

    public interface ICriteriaWhen {
        /**
         *
         * @mbg.generated Tue Jun 04 15:44:18 CST 2024
         */
        void criteria(Criteria criteria);
    }

    public interface IExampleWhen {
        /**
         *
         * @mbg.generated Tue Jun 04 15:44:18 CST 2024
         */
        void example(com.chinamobile.retail.pojo.entity.ProductFlowInstanceSpuExample example);
    }
}