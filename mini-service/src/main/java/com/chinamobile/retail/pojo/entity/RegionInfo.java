package com.chinamobile.retail.pojo.entity;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;

/**
 * Created By MBG-GUI-EXTENSION https://github.com/spawpaw/mybatis-generator-gui-extension
 * Description:
 * 区县编码
 *
 * <AUTHOR>
public class RegionInfo implements Serializable {
    /**
     * 区县ID
     *
     *
     * @mbg.generated Mon May 12 11:21:18 CST 2025
     */
    private String regionId;

    /**
     * 区县名称
     *
     *
     * @mbg.generated Mon May 12 11:21:18 CST 2025
     */
    private String regionName;

    /**
     * 地市ID
     *
     *
     * @mbg.generated Mon May 12 11:21:18 CST 2025
     */
    private String location;

    /**
     * 区县编码
     *
     *
     * @mbg.generated Mon May 12 11:21:18 CST 2025
     */
    private String regionCode;

    /**
     *
     * @mbg.generated Mon May 12 11:21:18 CST 2025
     */
    private static final long serialVersionUID = 1L;

    /**
     * This method returns the value of the database column supply_chain..region_info.region_id
     *
     * @return the value of supply_chain..region_info.region_id
     *
     * @mbg.generated Mon May 12 11:21:18 CST 2025
     */
    public String getRegionId() {
        return regionId;
    }

    /**
     *
     * @mbg.generated Mon May 12 11:21:18 CST 2025
     */
    public RegionInfo withRegionId(String regionId) {
        this.setRegionId(regionId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..region_info.region_id
     *
     * @param regionId the value for supply_chain..region_info.region_id
     *
     * @mbg.generated Mon May 12 11:21:18 CST 2025
     */
    public void setRegionId(String regionId) {
        this.regionId = regionId;
    }

    /**
     * This method returns the value of the database column supply_chain..region_info.region_name
     *
     * @return the value of supply_chain..region_info.region_name
     *
     * @mbg.generated Mon May 12 11:21:18 CST 2025
     */
    public String getRegionName() {
        return regionName;
    }

    /**
     *
     * @mbg.generated Mon May 12 11:21:18 CST 2025
     */
    public RegionInfo withRegionName(String regionName) {
        this.setRegionName(regionName);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..region_info.region_name
     *
     * @param regionName the value for supply_chain..region_info.region_name
     *
     * @mbg.generated Mon May 12 11:21:18 CST 2025
     */
    public void setRegionName(String regionName) {
        this.regionName = regionName;
    }

    /**
     * This method returns the value of the database column supply_chain..region_info.location
     *
     * @return the value of supply_chain..region_info.location
     *
     * @mbg.generated Mon May 12 11:21:18 CST 2025
     */
    public String getLocation() {
        return location;
    }

    /**
     *
     * @mbg.generated Mon May 12 11:21:18 CST 2025
     */
    public RegionInfo withLocation(String location) {
        this.setLocation(location);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..region_info.location
     *
     * @param location the value for supply_chain..region_info.location
     *
     * @mbg.generated Mon May 12 11:21:18 CST 2025
     */
    public void setLocation(String location) {
        this.location = location;
    }

    /**
     * This method returns the value of the database column supply_chain..region_info.region_code
     *
     * @return the value of supply_chain..region_info.region_code
     *
     * @mbg.generated Mon May 12 11:21:18 CST 2025
     */
    public String getRegionCode() {
        return regionCode;
    }

    /**
     *
     * @mbg.generated Mon May 12 11:21:18 CST 2025
     */
    public RegionInfo withRegionCode(String regionCode) {
        this.setRegionCode(regionCode);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..region_info.region_code
     *
     * @param regionCode the value for supply_chain..region_info.region_code
     *
     * @mbg.generated Mon May 12 11:21:18 CST 2025
     */
    public void setRegionCode(String regionCode) {
        this.regionCode = regionCode;
    }

    /**
     *
     * @mbg.generated Mon May 12 11:21:18 CST 2025
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", regionId=").append(regionId);
        sb.append(", regionName=").append(regionName);
        sb.append(", location=").append(location);
        sb.append(", regionCode=").append(regionCode);
        sb.append("]");
        return sb.toString();
    }

    /**
     *
     * @mbg.generated Mon May 12 11:21:18 CST 2025
     */
    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        RegionInfo other = (RegionInfo) that;
        return (this.getRegionId() == null ? other.getRegionId() == null : this.getRegionId().equals(other.getRegionId()))
            && (this.getRegionName() == null ? other.getRegionName() == null : this.getRegionName().equals(other.getRegionName()))
            && (this.getLocation() == null ? other.getLocation() == null : this.getLocation().equals(other.getLocation()))
            && (this.getRegionCode() == null ? other.getRegionCode() == null : this.getRegionCode().equals(other.getRegionCode()));
    }

    /**
     *
     * @mbg.generated Mon May 12 11:21:18 CST 2025
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getRegionId() == null) ? 0 : getRegionId().hashCode());
        result = prime * result + ((getRegionName() == null) ? 0 : getRegionName().hashCode());
        result = prime * result + ((getLocation() == null) ? 0 : getLocation().hashCode());
        result = prime * result + ((getRegionCode() == null) ? 0 : getRegionCode().hashCode());
        return result;
    }

    /**
     *
     * @mbg.generated Mon May 12 11:21:18 CST 2025
     */
    public enum Column {
        regionId("region_id", "regionId", "VARCHAR", false),
        regionName("region_name", "regionName", "VARCHAR", false),
        location("location", "location", "VARCHAR", false),
        regionCode("region_code", "regionCode", "VARCHAR", false);

        /**
         *
         * @mbg.generated Mon May 12 11:21:18 CST 2025
         */
        private static final String BEGINNING_DELIMITER = "\"";

        /**
         *
         * @mbg.generated Mon May 12 11:21:18 CST 2025
         */
        private static final String ENDING_DELIMITER = "\"";

        /**
         *
         * @mbg.generated Mon May 12 11:21:18 CST 2025
         */
        private final String column;

        /**
         *
         * @mbg.generated Mon May 12 11:21:18 CST 2025
         */
        private final boolean isColumnNameDelimited;

        /**
         *
         * @mbg.generated Mon May 12 11:21:18 CST 2025
         */
        private final String javaProperty;

        /**
         *
         * @mbg.generated Mon May 12 11:21:18 CST 2025
         */
        private final String jdbcType;

        /**
         *
         * @mbg.generated Mon May 12 11:21:18 CST 2025
         */
        public String value() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Mon May 12 11:21:18 CST 2025
         */
        public String getValue() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Mon May 12 11:21:18 CST 2025
         */
        public String getJavaProperty() {
            return this.javaProperty;
        }

        /**
         *
         * @mbg.generated Mon May 12 11:21:18 CST 2025
         */
        public String getJdbcType() {
            return this.jdbcType;
        }

        /**
         *
         * @mbg.generated Mon May 12 11:21:18 CST 2025
         */
        Column(String column, String javaProperty, String jdbcType, boolean isColumnNameDelimited) {
            this.column = column;
            this.javaProperty = javaProperty;
            this.jdbcType = jdbcType;
            this.isColumnNameDelimited = isColumnNameDelimited;
        }

        /**
         *
         * @mbg.generated Mon May 12 11:21:18 CST 2025
         */
        public String desc() {
            return this.getEscapedColumnName() + " DESC";
        }

        /**
         *
         * @mbg.generated Mon May 12 11:21:18 CST 2025
         */
        public String asc() {
            return this.getEscapedColumnName() + " ASC";
        }

        /**
         *
         * @mbg.generated Mon May 12 11:21:18 CST 2025
         */
        public static Column[] excludes(Column ... excludes) {
            ArrayList<Column> columns = new ArrayList<>(Arrays.asList(Column.values()));
            if (excludes != null && excludes.length > 0) {
                columns.removeAll(new ArrayList<>(Arrays.asList(excludes)));
            }
            return columns.toArray(new Column[]{});
        }

        /**
         *
         * @mbg.generated Mon May 12 11:21:18 CST 2025
         */
        public static Column[] all() {
            return Column.values();
        }

        /**
         *
         * @mbg.generated Mon May 12 11:21:18 CST 2025
         */
        public String getEscapedColumnName() {
            if (this.isColumnNameDelimited) {
                return new StringBuilder().append(BEGINNING_DELIMITER).append(this.column).append(ENDING_DELIMITER).toString();
            } else {
                return this.column;
            }
        }

        /**
         *
         * @mbg.generated Mon May 12 11:21:18 CST 2025
         */
        public String getAliasedEscapedColumnName() {
            return this.getEscapedColumnName();
        }
    }
}