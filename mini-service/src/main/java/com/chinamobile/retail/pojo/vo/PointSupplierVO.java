package com.chinamobile.retail.pojo.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;


/**
 * 积分供应商前端视图
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PointSupplierVO implements Serializable {
    /**
     * 主键
     */
    private String id;

    /**
     * 积分供应商全称
     */
    private String fullName;

    /**
     * 简称
     */
    private String abbreviationName;

    /**
     * 统一社会信用代码
     */
    private String code;

    /**
     * 联系人
     */
    private String contact;

    /**
     * 联系电话
     */
    private String phone;

    /**
     * 已关联商品
     * */
    private Integer associated;

    /**
     * 已生效商品
     * */
    private Integer valid;

    /**
     * 唯一识别码（拉卡拉商户）
     */
    private String uniqueId;


}