package com.chinamobile.retail.pojo.entity;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;

/**
 * Created By MBG-GUI-EXTENSION https://github.com/spawpaw/mybatis-generator-gui-extension
 * Description:
 * 积分供应商表
 *
 * <AUTHOR>
public class PointSupplier implements Serializable {
    /**
     * 主键
     *
     *
     * @mbg.generated Wed Oct 19 17:42:53 CST 2022
     */
    private String id;

    /**
     * 积分供应商全称
     *
     *
     * @mbg.generated Wed Oct 19 17:42:53 CST 2022
     */
    private String fullName;

    /**
     * 简称
     *
     *
     * @mbg.generated Wed Oct 19 17:42:53 CST 2022
     */
    private String abbreviationName;

    /**
     * 统一社会信用代码
     *
     *
     * @mbg.generated Wed Oct 19 17:42:53 CST 2022
     */
    private String code;

    /**
     * 联系人
     *
     *
     * @mbg.generated Wed Oct 19 17:42:53 CST 2022
     */
    private String contact;

    /**
     * 联系电话
     *
     *
     * @mbg.generated Wed Oct 19 17:42:53 CST 2022
     */
    private String phone;

    /**
     * 创建时间
     *
     *
     * @mbg.generated Wed Oct 19 17:42:53 CST 2022
     */
    private Date createTime;

    /**
     * 更新时间
     *
     *
     * @mbg.generated Wed Oct 19 17:42:53 CST 2022
     */
    private Date updateTime;

    /**
     * 唯一识别码（拉卡拉商户）
     *
     *
     * @mbg.generated Wed Oct 19 17:42:53 CST 2022
     */
    private String uniqueId;

    /**
     *
     * @mbg.generated Wed Oct 19 17:42:53 CST 2022
     */
    private static final long serialVersionUID = 1L;

    /**
     * This method returns the value of the database column supply_chain..point_supplier.id
     *
     * @return the value of supply_chain..point_supplier.id
     *
     * @mbg.generated Wed Oct 19 17:42:53 CST 2022
     */
    public String getId() {
        return id;
    }

    /**
     *
     * @mbg.generated Wed Oct 19 17:42:53 CST 2022
     */
    public PointSupplier withId(String id) {
        this.setId(id);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..point_supplier.id
     *
     * @param id the value for supply_chain..point_supplier.id
     *
     * @mbg.generated Wed Oct 19 17:42:53 CST 2022
     */
    public void setId(String id) {
        this.id = id;
    }

    /**
     * This method returns the value of the database column supply_chain..point_supplier.full_name
     *
     * @return the value of supply_chain..point_supplier.full_name
     *
     * @mbg.generated Wed Oct 19 17:42:53 CST 2022
     */
    public String getFullName() {
        return fullName;
    }

    /**
     *
     * @mbg.generated Wed Oct 19 17:42:53 CST 2022
     */
    public PointSupplier withFullName(String fullName) {
        this.setFullName(fullName);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..point_supplier.full_name
     *
     * @param fullName the value for supply_chain..point_supplier.full_name
     *
     * @mbg.generated Wed Oct 19 17:42:53 CST 2022
     */
    public void setFullName(String fullName) {
        this.fullName = fullName;
    }

    /**
     * This method returns the value of the database column supply_chain..point_supplier.abbreviation_name
     *
     * @return the value of supply_chain..point_supplier.abbreviation_name
     *
     * @mbg.generated Wed Oct 19 17:42:53 CST 2022
     */
    public String getAbbreviationName() {
        return abbreviationName;
    }

    /**
     *
     * @mbg.generated Wed Oct 19 17:42:53 CST 2022
     */
    public PointSupplier withAbbreviationName(String abbreviationName) {
        this.setAbbreviationName(abbreviationName);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..point_supplier.abbreviation_name
     *
     * @param abbreviationName the value for supply_chain..point_supplier.abbreviation_name
     *
     * @mbg.generated Wed Oct 19 17:42:53 CST 2022
     */
    public void setAbbreviationName(String abbreviationName) {
        this.abbreviationName = abbreviationName;
    }

    /**
     * This method returns the value of the database column supply_chain..point_supplier.code
     *
     * @return the value of supply_chain..point_supplier.code
     *
     * @mbg.generated Wed Oct 19 17:42:53 CST 2022
     */
    public String getCode() {
        return code;
    }

    /**
     *
     * @mbg.generated Wed Oct 19 17:42:53 CST 2022
     */
    public PointSupplier withCode(String code) {
        this.setCode(code);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..point_supplier.code
     *
     * @param code the value for supply_chain..point_supplier.code
     *
     * @mbg.generated Wed Oct 19 17:42:53 CST 2022
     */
    public void setCode(String code) {
        this.code = code;
    }

    /**
     * This method returns the value of the database column supply_chain..point_supplier.contact
     *
     * @return the value of supply_chain..point_supplier.contact
     *
     * @mbg.generated Wed Oct 19 17:42:53 CST 2022
     */
    public String getContact() {
        return contact;
    }

    /**
     *
     * @mbg.generated Wed Oct 19 17:42:53 CST 2022
     */
    public PointSupplier withContact(String contact) {
        this.setContact(contact);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..point_supplier.contact
     *
     * @param contact the value for supply_chain..point_supplier.contact
     *
     * @mbg.generated Wed Oct 19 17:42:53 CST 2022
     */
    public void setContact(String contact) {
        this.contact = contact;
    }

    /**
     * This method returns the value of the database column supply_chain..point_supplier.phone
     *
     * @return the value of supply_chain..point_supplier.phone
     *
     * @mbg.generated Wed Oct 19 17:42:53 CST 2022
     */
    public String getPhone() {
        return phone;
    }

    /**
     *
     * @mbg.generated Wed Oct 19 17:42:53 CST 2022
     */
    public PointSupplier withPhone(String phone) {
        this.setPhone(phone);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..point_supplier.phone
     *
     * @param phone the value for supply_chain..point_supplier.phone
     *
     * @mbg.generated Wed Oct 19 17:42:53 CST 2022
     */
    public void setPhone(String phone) {
        this.phone = phone;
    }

    /**
     * This method returns the value of the database column supply_chain..point_supplier.create_time
     *
     * @return the value of supply_chain..point_supplier.create_time
     *
     * @mbg.generated Wed Oct 19 17:42:53 CST 2022
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     *
     * @mbg.generated Wed Oct 19 17:42:53 CST 2022
     */
    public PointSupplier withCreateTime(Date createTime) {
        this.setCreateTime(createTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..point_supplier.create_time
     *
     * @param createTime the value for supply_chain..point_supplier.create_time
     *
     * @mbg.generated Wed Oct 19 17:42:53 CST 2022
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * This method returns the value of the database column supply_chain..point_supplier.update_time
     *
     * @return the value of supply_chain..point_supplier.update_time
     *
     * @mbg.generated Wed Oct 19 17:42:53 CST 2022
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     *
     * @mbg.generated Wed Oct 19 17:42:53 CST 2022
     */
    public PointSupplier withUpdateTime(Date updateTime) {
        this.setUpdateTime(updateTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..point_supplier.update_time
     *
     * @param updateTime the value for supply_chain..point_supplier.update_time
     *
     * @mbg.generated Wed Oct 19 17:42:53 CST 2022
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * This method returns the value of the database column supply_chain..point_supplier.unique_id
     *
     * @return the value of supply_chain..point_supplier.unique_id
     *
     * @mbg.generated Wed Oct 19 17:42:53 CST 2022
     */
    public String getUniqueId() {
        return uniqueId;
    }

    /**
     *
     * @mbg.generated Wed Oct 19 17:42:53 CST 2022
     */
    public PointSupplier withUniqueId(String uniqueId) {
        this.setUniqueId(uniqueId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..point_supplier.unique_id
     *
     * @param uniqueId the value for supply_chain..point_supplier.unique_id
     *
     * @mbg.generated Wed Oct 19 17:42:53 CST 2022
     */
    public void setUniqueId(String uniqueId) {
        this.uniqueId = uniqueId;
    }

    /**
     *
     * @mbg.generated Wed Oct 19 17:42:53 CST 2022
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", fullName=").append(fullName);
        sb.append(", abbreviationName=").append(abbreviationName);
        sb.append(", code=").append(code);
        sb.append(", contact=").append(contact);
        sb.append(", phone=").append(phone);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", uniqueId=").append(uniqueId);
        sb.append("]");
        return sb.toString();
    }

    /**
     *
     * @mbg.generated Wed Oct 19 17:42:53 CST 2022
     */
    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        PointSupplier other = (PointSupplier) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getFullName() == null ? other.getFullName() == null : this.getFullName().equals(other.getFullName()))
            && (this.getAbbreviationName() == null ? other.getAbbreviationName() == null : this.getAbbreviationName().equals(other.getAbbreviationName()))
            && (this.getCode() == null ? other.getCode() == null : this.getCode().equals(other.getCode()))
            && (this.getContact() == null ? other.getContact() == null : this.getContact().equals(other.getContact()))
            && (this.getPhone() == null ? other.getPhone() == null : this.getPhone().equals(other.getPhone()))
            && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()))
            && (this.getUpdateTime() == null ? other.getUpdateTime() == null : this.getUpdateTime().equals(other.getUpdateTime()))
            && (this.getUniqueId() == null ? other.getUniqueId() == null : this.getUniqueId().equals(other.getUniqueId()));
    }

    /**
     *
     * @mbg.generated Wed Oct 19 17:42:53 CST 2022
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getFullName() == null) ? 0 : getFullName().hashCode());
        result = prime * result + ((getAbbreviationName() == null) ? 0 : getAbbreviationName().hashCode());
        result = prime * result + ((getCode() == null) ? 0 : getCode().hashCode());
        result = prime * result + ((getContact() == null) ? 0 : getContact().hashCode());
        result = prime * result + ((getPhone() == null) ? 0 : getPhone().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        result = prime * result + ((getUpdateTime() == null) ? 0 : getUpdateTime().hashCode());
        result = prime * result + ((getUniqueId() == null) ? 0 : getUniqueId().hashCode());
        return result;
    }

    /**
     *
     * @mbg.generated Wed Oct 19 17:42:53 CST 2022
     */
    public enum Column {
        id("id", "id", "VARCHAR", false),
        fullName("full_name", "fullName", "VARCHAR", false),
        abbreviationName("abbreviation_name", "abbreviationName", "VARCHAR", false),
        code("code", "code", "VARCHAR", false),
        contact("contact", "contact", "VARCHAR", false),
        phone("phone", "phone", "VARCHAR", false),
        createTime("create_time", "createTime", "TIMESTAMP", false),
        updateTime("update_time", "updateTime", "TIMESTAMP", false),
        uniqueId("unique_id", "uniqueId", "VARCHAR", false);

        /**
         *
         * @mbg.generated Wed Oct 19 17:42:53 CST 2022
         */
        private static final String BEGINNING_DELIMITER = "\"";

        /**
         *
         * @mbg.generated Wed Oct 19 17:42:53 CST 2022
         */
        private static final String ENDING_DELIMITER = "\"";

        /**
         *
         * @mbg.generated Wed Oct 19 17:42:53 CST 2022
         */
        private final String column;

        /**
         *
         * @mbg.generated Wed Oct 19 17:42:53 CST 2022
         */
        private final boolean isColumnNameDelimited;

        /**
         *
         * @mbg.generated Wed Oct 19 17:42:53 CST 2022
         */
        private final String javaProperty;

        /**
         *
         * @mbg.generated Wed Oct 19 17:42:53 CST 2022
         */
        private final String jdbcType;

        /**
         *
         * @mbg.generated Wed Oct 19 17:42:53 CST 2022
         */
        public String value() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Wed Oct 19 17:42:53 CST 2022
         */
        public String getValue() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Wed Oct 19 17:42:53 CST 2022
         */
        public String getJavaProperty() {
            return this.javaProperty;
        }

        /**
         *
         * @mbg.generated Wed Oct 19 17:42:53 CST 2022
         */
        public String getJdbcType() {
            return this.jdbcType;
        }

        /**
         *
         * @mbg.generated Wed Oct 19 17:42:53 CST 2022
         */
        Column(String column, String javaProperty, String jdbcType, boolean isColumnNameDelimited) {
            this.column = column;
            this.javaProperty = javaProperty;
            this.jdbcType = jdbcType;
            this.isColumnNameDelimited = isColumnNameDelimited;
        }

        /**
         *
         * @mbg.generated Wed Oct 19 17:42:53 CST 2022
         */
        public String desc() {
            return this.getEscapedColumnName() + " DESC";
        }

        /**
         *
         * @mbg.generated Wed Oct 19 17:42:53 CST 2022
         */
        public String asc() {
            return this.getEscapedColumnName() + " ASC";
        }

        /**
         *
         * @mbg.generated Wed Oct 19 17:42:53 CST 2022
         */
        public static Column[] excludes(Column ... excludes) {
            ArrayList<Column> columns = new ArrayList<>(Arrays.asList(Column.values()));
            if (excludes != null && excludes.length > 0) {
                columns.removeAll(new ArrayList<>(Arrays.asList(excludes)));
            }
            return columns.toArray(new Column[]{});
        }

        /**
         *
         * @mbg.generated Wed Oct 19 17:42:53 CST 2022
         */
        public static Column[] all() {
            return Column.values();
        }

        /**
         *
         * @mbg.generated Wed Oct 19 17:42:53 CST 2022
         */
        public String getEscapedColumnName() {
            if (this.isColumnNameDelimited) {
                return new StringBuilder().append(BEGINNING_DELIMITER).append(this.column).append(ENDING_DELIMITER).toString();
            } else {
                return this.column;
            }
        }

        /**
         *
         * @mbg.generated Wed Oct 19 17:42:53 CST 2022
         */
        public String getAliasedEscapedColumnName() {
            return this.getEscapedColumnName();
        }
    }
}