package com.chinamobile.retail.pojo.entity;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;

/**
 * Created By MBG-GUI-EXTENSION https://github.com/spawpaw/mybatis-generator-gui-extension
 * Description:
 * 小程序活动中奖记录
 *
 * <AUTHOR>
public class MiniProgramActivityUserAward implements Serializable {
    /**
     * 主键
     *
     *
     * @mbg.generated Fri Jul 26 16:05:07 CST 2024
     */
    private String id;

    /**
     * 小程序活动id
     *
     *
     * @mbg.generated Fri Jul 26 16:05:07 CST 2024
     */
    private String activityId;

    /**
     * 小程序奖品id
     *
     *
     * @mbg.generated Fri Jul 26 16:05:07 CST 2024
     */
    private String awardId;

    /**
     * 小程序用户id
     *
     *
     * @mbg.generated Fri Jul 26 16:05:07 CST 2024
     */
    private String userId;

    /**
     * 排名
     *
     *
     * @mbg.generated Fri Jul 26 16:05:07 CST 2024
     */
    private Integer ranking;

    /**
     * 订单总金额
     *
     *
     * @mbg.generated Fri Jul 26 16:05:07 CST 2024
     */
    private Long amount;

    /**
     * 订单数量
     *
     *
     * @mbg.generated Fri Jul 26 16:05:07 CST 2024
     */
    private Integer total;

    /**
     * 创建时间
     *
     *
     * @mbg.generated Fri Jul 26 16:05:07 CST 2024
     */
    private Date createTime;

    /**
     * 更新时间
     *
     *
     * @mbg.generated Fri Jul 26 16:05:07 CST 2024
     */
    private Date updateTime;

    /**
     * 奖品收货地址
     *
     *
     * @mbg.generated Fri Jul 26 16:05:07 CST 2024
     */
    private String addressId;

    /**
     * 奖品状态 0：未领取 1：待发货 2：已发货
     *
     *
     * @mbg.generated Fri Jul 26 16:05:07 CST 2024
     */
    private Integer status;

    /**
     * 物流单号
     *
     *
     * @mbg.generated Fri Jul 26 16:05:07 CST 2024
     */
    private String logisticsCode;

    /**
     *
     * @mbg.generated Fri Jul 26 16:05:07 CST 2024
     */
    private static final long serialVersionUID = 1L;

    /**
     * This method returns the value of the database column supply_chain..mini_program_activity_user_award.id
     *
     * @return the value of supply_chain..mini_program_activity_user_award.id
     *
     * @mbg.generated Fri Jul 26 16:05:07 CST 2024
     */
    public String getId() {
        return id;
    }

    /**
     *
     * @mbg.generated Fri Jul 26 16:05:07 CST 2024
     */
    public MiniProgramActivityUserAward withId(String id) {
        this.setId(id);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_activity_user_award.id
     *
     * @param id the value for supply_chain..mini_program_activity_user_award.id
     *
     * @mbg.generated Fri Jul 26 16:05:07 CST 2024
     */
    public void setId(String id) {
        this.id = id;
    }

    /**
     * This method returns the value of the database column supply_chain..mini_program_activity_user_award.activity_id
     *
     * @return the value of supply_chain..mini_program_activity_user_award.activity_id
     *
     * @mbg.generated Fri Jul 26 16:05:07 CST 2024
     */
    public String getActivityId() {
        return activityId;
    }

    /**
     *
     * @mbg.generated Fri Jul 26 16:05:07 CST 2024
     */
    public MiniProgramActivityUserAward withActivityId(String activityId) {
        this.setActivityId(activityId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_activity_user_award.activity_id
     *
     * @param activityId the value for supply_chain..mini_program_activity_user_award.activity_id
     *
     * @mbg.generated Fri Jul 26 16:05:07 CST 2024
     */
    public void setActivityId(String activityId) {
        this.activityId = activityId;
    }

    /**
     * This method returns the value of the database column supply_chain..mini_program_activity_user_award.award_id
     *
     * @return the value of supply_chain..mini_program_activity_user_award.award_id
     *
     * @mbg.generated Fri Jul 26 16:05:07 CST 2024
     */
    public String getAwardId() {
        return awardId;
    }

    /**
     *
     * @mbg.generated Fri Jul 26 16:05:07 CST 2024
     */
    public MiniProgramActivityUserAward withAwardId(String awardId) {
        this.setAwardId(awardId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_activity_user_award.award_id
     *
     * @param awardId the value for supply_chain..mini_program_activity_user_award.award_id
     *
     * @mbg.generated Fri Jul 26 16:05:07 CST 2024
     */
    public void setAwardId(String awardId) {
        this.awardId = awardId;
    }

    /**
     * This method returns the value of the database column supply_chain..mini_program_activity_user_award.user_id
     *
     * @return the value of supply_chain..mini_program_activity_user_award.user_id
     *
     * @mbg.generated Fri Jul 26 16:05:07 CST 2024
     */
    public String getUserId() {
        return userId;
    }

    /**
     *
     * @mbg.generated Fri Jul 26 16:05:07 CST 2024
     */
    public MiniProgramActivityUserAward withUserId(String userId) {
        this.setUserId(userId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_activity_user_award.user_id
     *
     * @param userId the value for supply_chain..mini_program_activity_user_award.user_id
     *
     * @mbg.generated Fri Jul 26 16:05:07 CST 2024
     */
    public void setUserId(String userId) {
        this.userId = userId;
    }

    /**
     * This method returns the value of the database column supply_chain..mini_program_activity_user_award.ranking
     *
     * @return the value of supply_chain..mini_program_activity_user_award.ranking
     *
     * @mbg.generated Fri Jul 26 16:05:07 CST 2024
     */
    public Integer getRanking() {
        return ranking;
    }

    /**
     *
     * @mbg.generated Fri Jul 26 16:05:07 CST 2024
     */
    public MiniProgramActivityUserAward withRanking(Integer ranking) {
        this.setRanking(ranking);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_activity_user_award.ranking
     *
     * @param ranking the value for supply_chain..mini_program_activity_user_award.ranking
     *
     * @mbg.generated Fri Jul 26 16:05:07 CST 2024
     */
    public void setRanking(Integer ranking) {
        this.ranking = ranking;
    }

    /**
     * This method returns the value of the database column supply_chain..mini_program_activity_user_award.amount
     *
     * @return the value of supply_chain..mini_program_activity_user_award.amount
     *
     * @mbg.generated Fri Jul 26 16:05:07 CST 2024
     */
    public Long getAmount() {
        return amount;
    }

    /**
     *
     * @mbg.generated Fri Jul 26 16:05:07 CST 2024
     */
    public MiniProgramActivityUserAward withAmount(Long amount) {
        this.setAmount(amount);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_activity_user_award.amount
     *
     * @param amount the value for supply_chain..mini_program_activity_user_award.amount
     *
     * @mbg.generated Fri Jul 26 16:05:07 CST 2024
     */
    public void setAmount(Long amount) {
        this.amount = amount;
    }

    /**
     * This method returns the value of the database column supply_chain..mini_program_activity_user_award.total
     *
     * @return the value of supply_chain..mini_program_activity_user_award.total
     *
     * @mbg.generated Fri Jul 26 16:05:07 CST 2024
     */
    public Integer getTotal() {
        return total;
    }

    /**
     *
     * @mbg.generated Fri Jul 26 16:05:07 CST 2024
     */
    public MiniProgramActivityUserAward withTotal(Integer total) {
        this.setTotal(total);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_activity_user_award.total
     *
     * @param total the value for supply_chain..mini_program_activity_user_award.total
     *
     * @mbg.generated Fri Jul 26 16:05:07 CST 2024
     */
    public void setTotal(Integer total) {
        this.total = total;
    }

    /**
     * This method returns the value of the database column supply_chain..mini_program_activity_user_award.create_time
     *
     * @return the value of supply_chain..mini_program_activity_user_award.create_time
     *
     * @mbg.generated Fri Jul 26 16:05:07 CST 2024
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     *
     * @mbg.generated Fri Jul 26 16:05:07 CST 2024
     */
    public MiniProgramActivityUserAward withCreateTime(Date createTime) {
        this.setCreateTime(createTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_activity_user_award.create_time
     *
     * @param createTime the value for supply_chain..mini_program_activity_user_award.create_time
     *
     * @mbg.generated Fri Jul 26 16:05:07 CST 2024
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * This method returns the value of the database column supply_chain..mini_program_activity_user_award.update_time
     *
     * @return the value of supply_chain..mini_program_activity_user_award.update_time
     *
     * @mbg.generated Fri Jul 26 16:05:07 CST 2024
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     *
     * @mbg.generated Fri Jul 26 16:05:07 CST 2024
     */
    public MiniProgramActivityUserAward withUpdateTime(Date updateTime) {
        this.setUpdateTime(updateTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_activity_user_award.update_time
     *
     * @param updateTime the value for supply_chain..mini_program_activity_user_award.update_time
     *
     * @mbg.generated Fri Jul 26 16:05:07 CST 2024
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * This method returns the value of the database column supply_chain..mini_program_activity_user_award.address_id
     *
     * @return the value of supply_chain..mini_program_activity_user_award.address_id
     *
     * @mbg.generated Fri Jul 26 16:05:07 CST 2024
     */
    public String getAddressId() {
        return addressId;
    }

    /**
     *
     * @mbg.generated Fri Jul 26 16:05:07 CST 2024
     */
    public MiniProgramActivityUserAward withAddressId(String addressId) {
        this.setAddressId(addressId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_activity_user_award.address_id
     *
     * @param addressId the value for supply_chain..mini_program_activity_user_award.address_id
     *
     * @mbg.generated Fri Jul 26 16:05:07 CST 2024
     */
    public void setAddressId(String addressId) {
        this.addressId = addressId;
    }

    /**
     * This method returns the value of the database column supply_chain..mini_program_activity_user_award.status
     *
     * @return the value of supply_chain..mini_program_activity_user_award.status
     *
     * @mbg.generated Fri Jul 26 16:05:07 CST 2024
     */
    public Integer getStatus() {
        return status;
    }

    /**
     *
     * @mbg.generated Fri Jul 26 16:05:07 CST 2024
     */
    public MiniProgramActivityUserAward withStatus(Integer status) {
        this.setStatus(status);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_activity_user_award.status
     *
     * @param status the value for supply_chain..mini_program_activity_user_award.status
     *
     * @mbg.generated Fri Jul 26 16:05:07 CST 2024
     */
    public void setStatus(Integer status) {
        this.status = status;
    }

    /**
     * This method returns the value of the database column supply_chain..mini_program_activity_user_award.logistics_code
     *
     * @return the value of supply_chain..mini_program_activity_user_award.logistics_code
     *
     * @mbg.generated Fri Jul 26 16:05:07 CST 2024
     */
    public String getLogisticsCode() {
        return logisticsCode;
    }

    /**
     *
     * @mbg.generated Fri Jul 26 16:05:07 CST 2024
     */
    public MiniProgramActivityUserAward withLogisticsCode(String logisticsCode) {
        this.setLogisticsCode(logisticsCode);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_activity_user_award.logistics_code
     *
     * @param logisticsCode the value for supply_chain..mini_program_activity_user_award.logistics_code
     *
     * @mbg.generated Fri Jul 26 16:05:07 CST 2024
     */
    public void setLogisticsCode(String logisticsCode) {
        this.logisticsCode = logisticsCode;
    }

    /**
     *
     * @mbg.generated Fri Jul 26 16:05:07 CST 2024
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", activityId=").append(activityId);
        sb.append(", awardId=").append(awardId);
        sb.append(", userId=").append(userId);
        sb.append(", ranking=").append(ranking);
        sb.append(", amount=").append(amount);
        sb.append(", total=").append(total);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", addressId=").append(addressId);
        sb.append(", status=").append(status);
        sb.append(", logisticsCode=").append(logisticsCode);
        sb.append("]");
        return sb.toString();
    }

    /**
     *
     * @mbg.generated Fri Jul 26 16:05:07 CST 2024
     */
    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        MiniProgramActivityUserAward other = (MiniProgramActivityUserAward) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getActivityId() == null ? other.getActivityId() == null : this.getActivityId().equals(other.getActivityId()))
            && (this.getAwardId() == null ? other.getAwardId() == null : this.getAwardId().equals(other.getAwardId()))
            && (this.getUserId() == null ? other.getUserId() == null : this.getUserId().equals(other.getUserId()))
            && (this.getRanking() == null ? other.getRanking() == null : this.getRanking().equals(other.getRanking()))
            && (this.getAmount() == null ? other.getAmount() == null : this.getAmount().equals(other.getAmount()))
            && (this.getTotal() == null ? other.getTotal() == null : this.getTotal().equals(other.getTotal()))
            && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()))
            && (this.getUpdateTime() == null ? other.getUpdateTime() == null : this.getUpdateTime().equals(other.getUpdateTime()))
            && (this.getAddressId() == null ? other.getAddressId() == null : this.getAddressId().equals(other.getAddressId()))
            && (this.getStatus() == null ? other.getStatus() == null : this.getStatus().equals(other.getStatus()))
            && (this.getLogisticsCode() == null ? other.getLogisticsCode() == null : this.getLogisticsCode().equals(other.getLogisticsCode()));
    }

    /**
     *
     * @mbg.generated Fri Jul 26 16:05:07 CST 2024
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getActivityId() == null) ? 0 : getActivityId().hashCode());
        result = prime * result + ((getAwardId() == null) ? 0 : getAwardId().hashCode());
        result = prime * result + ((getUserId() == null) ? 0 : getUserId().hashCode());
        result = prime * result + ((getRanking() == null) ? 0 : getRanking().hashCode());
        result = prime * result + ((getAmount() == null) ? 0 : getAmount().hashCode());
        result = prime * result + ((getTotal() == null) ? 0 : getTotal().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        result = prime * result + ((getUpdateTime() == null) ? 0 : getUpdateTime().hashCode());
        result = prime * result + ((getAddressId() == null) ? 0 : getAddressId().hashCode());
        result = prime * result + ((getStatus() == null) ? 0 : getStatus().hashCode());
        result = prime * result + ((getLogisticsCode() == null) ? 0 : getLogisticsCode().hashCode());
        return result;
    }

    /**
     *
     * @mbg.generated Fri Jul 26 16:05:07 CST 2024
     */
    public enum Column {
        id("id", "id", "VARCHAR", false),
        activityId("activity_id", "activityId", "VARCHAR", false),
        awardId("award_id", "awardId", "VARCHAR", false),
        userId("user_id", "userId", "VARCHAR", false),
        ranking("ranking", "ranking", "INTEGER", false),
        amount("amount", "amount", "BIGINT", false),
        total("total", "total", "INTEGER", false),
        createTime("create_time", "createTime", "TIMESTAMP", false),
        updateTime("update_time", "updateTime", "TIMESTAMP", false),
        addressId("address_id", "addressId", "VARCHAR", false),
        status("status", "status", "INTEGER", false),
        logisticsCode("logistics_code", "logisticsCode", "VARCHAR", false);

        /**
         *
         * @mbg.generated Fri Jul 26 16:05:07 CST 2024
         */
        private static final String BEGINNING_DELIMITER = "\"";

        /**
         *
         * @mbg.generated Fri Jul 26 16:05:07 CST 2024
         */
        private static final String ENDING_DELIMITER = "\"";

        /**
         *
         * @mbg.generated Fri Jul 26 16:05:07 CST 2024
         */
        private final String column;

        /**
         *
         * @mbg.generated Fri Jul 26 16:05:07 CST 2024
         */
        private final boolean isColumnNameDelimited;

        /**
         *
         * @mbg.generated Fri Jul 26 16:05:07 CST 2024
         */
        private final String javaProperty;

        /**
         *
         * @mbg.generated Fri Jul 26 16:05:07 CST 2024
         */
        private final String jdbcType;

        /**
         *
         * @mbg.generated Fri Jul 26 16:05:07 CST 2024
         */
        public String value() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Fri Jul 26 16:05:07 CST 2024
         */
        public String getValue() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Fri Jul 26 16:05:07 CST 2024
         */
        public String getJavaProperty() {
            return this.javaProperty;
        }

        /**
         *
         * @mbg.generated Fri Jul 26 16:05:07 CST 2024
         */
        public String getJdbcType() {
            return this.jdbcType;
        }

        /**
         *
         * @mbg.generated Fri Jul 26 16:05:07 CST 2024
         */
        Column(String column, String javaProperty, String jdbcType, boolean isColumnNameDelimited) {
            this.column = column;
            this.javaProperty = javaProperty;
            this.jdbcType = jdbcType;
            this.isColumnNameDelimited = isColumnNameDelimited;
        }

        /**
         *
         * @mbg.generated Fri Jul 26 16:05:07 CST 2024
         */
        public String desc() {
            return this.getEscapedColumnName() + " DESC";
        }

        /**
         *
         * @mbg.generated Fri Jul 26 16:05:07 CST 2024
         */
        public String asc() {
            return this.getEscapedColumnName() + " ASC";
        }

        /**
         *
         * @mbg.generated Fri Jul 26 16:05:07 CST 2024
         */
        public static Column[] excludes(Column ... excludes) {
            ArrayList<Column> columns = new ArrayList<>(Arrays.asList(Column.values()));
            if (excludes != null && excludes.length > 0) {
                columns.removeAll(new ArrayList<>(Arrays.asList(excludes)));
            }
            return columns.toArray(new Column[]{});
        }

        /**
         *
         * @mbg.generated Fri Jul 26 16:05:07 CST 2024
         */
        public static Column[] all() {
            return Column.values();
        }

        /**
         *
         * @mbg.generated Fri Jul 26 16:05:07 CST 2024
         */
        public String getEscapedColumnName() {
            if (this.isColumnNameDelimited) {
                return new StringBuilder().append(BEGINNING_DELIMITER).append(this.column).append(ENDING_DELIMITER).toString();
            } else {
                return this.column;
            }
        }

        /**
         *
         * @mbg.generated Fri Jul 26 16:05:07 CST 2024
         */
        public String getAliasedEscapedColumnName() {
            return this.getEscapedColumnName();
        }
    }
}